/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;


/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Aspect
@Component
@Slf4j
public class MasterOnlyAspect {

    @Autowired
    ClusterManager clusterManager;

    /**
     * 检查是否是主节点
     *
     * @param joinPoint 切点
     * @return Object
     * @throws Throwable 异常
     */
    @Around("@annotation(com.hihonor.iot.plc.boot.MasterOnly)")
    public Object checkMasterBeforeExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!clusterManager.isLeader()) {
            log.info("Current node is not the Leader. Method {} will not be executed.", joinPoint.getSignature());
            return null;  // 直接返回，不执行目标方法
        }
        log.info("Current node is the Leader. Method {} will be executed.", joinPoint.getSignature());

        // 在主机（Leader）上执行目标方法
        return joinPoint.proceed();
    }
}

