/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
public class BatchReadTransferBoardPropertiesRequest {

    @NotEmpty(message = "过板台属性列表不能为空")
    private List<@Valid TransferBoardProperty> transferBoardProperties; // 过板台属性列表

    @Data
    public static class TransferBoardProperty {

        @NotBlank(message = "过板台名称不能为空")
        private String name; // 过板台名称

        @NotEmpty(message = "属性名称列表不能为空")
        @Size(min = 1, message = "属性名称列表长度必须大于0")
        private List<@NotBlank(message = "属性名称不能为空") String> propertyNames; // 要获取的属性名称列表
    }
}