/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
public class Point {
    private String name;
    private String address;

    /**
     * 获取地址的类型（如DT、R等）
     * @return 地址类型
     */
    public String getAddressType() {
        if (isAddressRange()) {
            // 如果是范围地址，取第一个地址的类型
            return address.split("-")[0].replaceAll("\\d", "");
        } else {
            // 如果是单个地址，直接移除数字
            return address.replaceAll("\\d", "");
        }
    }
    /**
     * 获取起始地址
     * @return 起始地址
     */
    public String getStartAddress() {
        return address.split("-")[0];
    }

    /**
     * 获取结束地址
     * @return 结束地址，如果是单个地址，则返回null
     */
    public String getEndAddress() {
        String[] parts = address.split("-");
        return parts.length > 1 ? parts[1] : null;
    }

    /**
     * 判断是否是地址范围
     * @return 如果是地址范围，则返回true；否则返回false
     */
    public boolean isAddressRange() {
        return address.contains("-");
    }
}