/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;


import java.nio.charset.StandardCharsets;
import java.util.List;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;


/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Slf4j
public class CustomLineDecoder extends ByteToMessageDecoder {


    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf buffer, List<Object> out)  {
        // 检查是否有可读字节
        while (buffer.readableBytes() > 0) {
            // 查找下一个 CR 的索引
            int crIndex = buffer.indexOf(buffer.readerIndex(), buffer.writerIndex(), (byte) 0x0D);

            // 如果没有找到 CR，检查剩余字节的长度
            if (crIndex == -1) {
                log.info("缓冲区中有不完整的消息，等待更多数据。");
                return; // 保留在缓冲区中，等待更多数据
            }

            // 提取包含 CR 的消息
            ByteBuf frame = buffer.readSlice(crIndex); // 不包含 CR

            // 将提取的帧转换为字符串，并去掉 CR 字节
            String decodedData = frame.toString(StandardCharsets.US_ASCII); // 保留回车符

            // 将有效消息添加到输出列表
            out.add(decodedData);
            log.info("输出数据: {}", decodedData);

            // 跳过 CR 字节
            buffer.skipBytes(1);
        }
    }

    /**
     * 检查数据是否有效。
     * 有效条件：长度为16位或包含3个逗号。
     *
     * @param data 要检查的数据
     * @return true 如果数据有效，false 否则
     */
    private boolean isValidData(String data) {
        return data != null && (data.length() == 16 || countOccurrences(data, ',') == 3);
    }

    /**
     * 计算字符串中指定字符的出现次数。
     *
     * @param data 要检查的字符串
     * @param ch   要计算的字符
     * @return 指定字符的出现次数
     */
    private int countOccurrences(String data, char ch) {
        int count = 0;
        for (char c : data.toCharArray()) {
            if (c == ch) {
                count++;
            }
        }
        return count;
    }
}

