/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.controller.request;

import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Data
public class SendDataRequest {

    @NotBlank(message = "deviceName must not be blank")
    private String iotId;

    @NotNull(message = "rows must not be null")
    private List<Map<String, Object>> rows;
}
