@startuml
actor "主控制系统" as Main
participant "摄像头" as Camera
participant "板台" as Plate
participant "条码验证服务" as BarcodeService

Main -> Camera : 初始化连接
Main -> Plate : 初始化连接
Main -> Main : 开始循环处理

loop 直到收到停止指令
    Main -> Camera : 检查连接状态
    Camera --> Main : 返回连接状态

    alt 摄像头已连接
        Main -> Camera : 读取条码
        Camera --> Main : 返回条码数据

        alt 条码数据为NULL
            Main -> Main : 跳过本次循环，重新读取条码
        else 条码有效
            Main -> BarcodeService : 验证条码
            BarcodeService --> Main : 返回验证结果

            alt 验证通过
                Main -> Plate : 检查板台连接状态
                Plate --> Main : 返回连接状态

                alt 板台已连接
                    Main -> Plate : 发送允许通过信号(OK1)
                    Main -> Plate : 发送重置信号(OK0)
                else 板台未连接
                    Main -> Main : 记录错误日志
                end
            else 验证不通过
                Main -> Plate : 检查板台连接状态
                Plate --> Main : 返回连接状态

                alt 板台已连接
                    Main -> Plate : 发送告警信号(1)
                    Main -> Plate : 发送复位信号(0)
                else 板台未连接
                    Main -> Main : 记录错误日志
                end
            end
        end

    else 摄像头未连接
        Main -> Main : 记录错误日志
    end

    Main -> Main : 等待/让出CPU时间
end

Main -> Main : 停止处理
Main -> Camera : 断开连接
Main -> Plate : 断开连接

@enduml