/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Service
@Slf4j
public class CameraPlateService {

    @Autowired
    private CameraPlateAssociationMapper cameraPlateAssociationMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    /**
     * 根据相机名获取关联的过板台设备名。
     * 如果没有找到相应的过板台设备名，返回null。
     *
     * @param cameraDeviceName 相机设备名
     * @return 过板台设备名或null
     */
    public String getPlateDeviceNameByCameraName(String cameraDeviceName) {
        return cameraPlateAssociationMapper.getPlateDeviceNameByCameraName(cameraDeviceName);
    }

    /**
     * 获取所有相机与过板台的关联信息。
     * @return 关联信息列表
     */
    public  List<CameraPlateAssociation> getCameraPlateAssociations() {
        return cameraPlateAssociationMapper.selectList(null);
    }

    /**
     * 根据过板台设备名获取关联的相机名。
     * 如果没有找到相应的相机名，返回null。
     *
     * @param plateDeviceName 过板台设备名
     * @return 相机设备名或null
     */
    public List<CameraPlateAssociation> getCameraDeviceNameByPlateName(String plateDeviceName) {
        return cameraPlateAssociationMapper.getCameraDeviceNameByPlateName(plateDeviceName);
    }

    /**
     * 根据相机设备名获取关联的过板台设备名。
     * @param cameraDeviceName 相机设备名
     * @return  关联信息列表
     */

    public List<CameraPlateAssociation>  getCameraPlateAssociationsByCameraName(List<String> cameraDeviceName) {
        return cameraPlateAssociationMapper.selectByCameraDeviceNames(cameraDeviceName);
    }


    /**
     * 插入或更新相机与过板台的关联信息。
     * 如果存在相同的相机名，则更新关联的过板台设备名和描述；如果不存在，则插入新的记录。
     * 返回操作是否成功。
     *
     * @param association 相机与过板台的关联信息实体
     * @return 操作是否成功（true: 成功, false: 失败）
     */
    public boolean upsertCameraPlateAssociation(CameraPlateAssociation association) {
        int affectedRows = cameraPlateAssociationMapper.upsertCameraPlateAssociation(association);
        return affectedRows > 0;
    }

    /**
     * 批量插入或更新相机与过板台的关联信息。
     * 对于每个相机名，如果存在相同的相机名，则更新关联的过板台设备名和描述；如果不存在，则插入新的记录。
     * 返回每个相机名对应的操作是否成功的Map。
     *
     * @param associations 相机与过板台的关联信息实体列表
     * @return 每个相机名对应的操作是否成功的Map
     */
    public Map<String, Boolean> upsertCameraPlateAssociations(List<CameraPlateAssociation> associations) {
        Map<String, Boolean> results = new HashMap<>();
        for (CameraPlateAssociation association : associations) {
            try {
                int affectedRows = cameraPlateAssociationMapper.upsertCameraPlateAssociation(association);
                boolean isSuccess = affectedRows > 0;
                results.put(association.getCameraDeviceName(), isSuccess);

                // 如果操作成功，发布创建或更新成功事件
                if (isSuccess) {
                    CameraPlateAssociationCreateEvent event = new CameraPlateAssociationCreateEvent(this, association);
                    eventPublisher.publishEvent(event);
                }
            } catch (Exception e) {
                // 记录错误日志
                log.error("Failed to upsert CameraPlateAssociation for camera device: " + association.getCameraDeviceName(), e);
                // 将结果设置为 false 表示失败
                results.put(association.getCameraDeviceName(), false);
            }
        }
        return results;
    }

    /**
     * 根据相机名和过板台设备名进行模糊查询，并支持分页。
     * 如果参数为空或空字符串，则不使用该条件。
     *
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param cameraDeviceName 相机设备名
     * @param plateDeviceName 过板台设备名
     * @return 分页后的关联信息列表
     */
    public IPage<CameraPlateAssociation> selectByCameraOrPlateName(int pageNum, int pageSize, String cameraDeviceName, String plateDeviceName) {
        Page<CameraPlateAssociation> page = new Page<>(pageNum, pageSize);
        return cameraPlateAssociationMapper.selectByCameraOrPlateName(page, cameraDeviceName, plateDeviceName);
    }

    /**
     * 批量删除相机设备的关联记录，并返回每个相机设备名对应的操作结果。
     * @param cameraDeviceNames 相机设备名列表
     * @return 每个相机设备名对应的操作是否成功的Map
     */
    public Map<String, Boolean> deleteCameraPlateAssociations(List<String> cameraDeviceNames) {
        List<CameraPlateAssociation> cameraPlateAssociations = getCameraPlateAssociationsByCameraName(cameraDeviceNames);
        Map<String, Boolean> results = new HashMap<>();
        for (CameraPlateAssociation cameraPlateAssociation : cameraPlateAssociations) {
            try {
                int affectedRows = cameraPlateAssociationMapper.deleteByCameraDeviceName(cameraPlateAssociation.getCameraDeviceName());
                boolean isDeleted = affectedRows > 0;
                results.put(cameraPlateAssociation.getCameraDeviceName(), isDeleted);

                // 如果删除成功，发布删除成功事件
                if (isDeleted) {
                    CameraPlateAssociationDeleteEvent event = new CameraPlateAssociationDeleteEvent(this, cameraPlateAssociation);
                    eventPublisher.publishEvent(event);
                }
            } catch (Exception e) {
                // 记录错误日志
                log.error("Failed to delete CameraPlateAssociation for camera device: " + cameraPlateAssociation.getCameraDeviceName(), e);
                // 将结果设置为 false 表示失败
                results.put(cameraPlateAssociation.getCameraDeviceName(), false);
            }
        }
        return results;
    }

    /**
     *
     * @param cameraName
     * @return
     */
    public List<String> getUnboundCameraNames(String cameraName) {
        return cameraPlateAssociationMapper.getUnboundCameraNames(cameraName);
    }

    /**
     * 获取指定相机设备名的关联信息。
     * @param trackNumber 轨道号
     * @param plateName 过板台设备名
     * @return
     */
    public List<String> getUnboundPlateNamesByTrackAndName(Integer trackNumber, String plateName) {
        return cameraPlateAssociationMapper.getUnboundPlateNamesByTrackAndName(trackNumber, plateName);
    }

    /**
     * 获取指定相机设备名的关联信息。
     * @param cameraDeviceName 相机设备名
     * @param plateDeviceName 过板台设备名
     * @return 关联信息实体
     */
    public List<CameraPlateAssociation> selectAllByCameraOrPlateName(String cameraDeviceName, String plateDeviceName) {
        return cameraPlateAssociationMapper.selectAllByCameraOrPlateName(cameraDeviceName, plateDeviceName);
    }


}
