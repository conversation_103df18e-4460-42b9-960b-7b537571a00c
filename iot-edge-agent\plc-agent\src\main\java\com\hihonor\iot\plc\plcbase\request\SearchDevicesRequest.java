/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase.request;

import javax.validation.constraints.Min;

import org.springframework.validation.annotation.Validated;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Validated
@Data
public class SearchDevicesRequest {
    private String type;
    private String name;
    private String tag;
    private Boolean status;

    @Min(value = 1, message = "Page number should be at least 1")
    private int page = 1;

    @Min(value = 1, message = "Page size should be at least 1")
    private int size = 10;

    // Getters and Setters
}
