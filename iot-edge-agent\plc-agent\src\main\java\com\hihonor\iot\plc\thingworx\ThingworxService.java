/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.thingworx;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import com.hihonor.iot.edge.common.config.BaseConfig;
import com.thingworx.communications.client.ConnectedThingClient;
import com.thingworx.metadata.DataShapeDefinition;
import com.thingworx.metadata.FieldDefinition;
import com.thingworx.metadata.collections.FieldDefinitionCollection;
import com.thingworx.types.BaseTypes;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Component
@Lazy
public class ThingworxService {
    @Value("${thingworx.url}")
    private String thingworxUrl;
    @Autowired
    private WebClient.Builder webClientBuilder;
    @Autowired
    BaseConfig baseConfig;

    @Autowired
    ConnectedThingClient client;

    String nameValues = "plateValue";


    public DataShapeDefinition getNameValues() {
        return client.getDataShapeDefinition(nameValues);
    }

    void defineNameValuesDataShape() {
        FieldDefinitionCollection fieds = new FieldDefinitionCollection();
        fieds.addFieldDefinition(new FieldDefinition("name", BaseTypes.STRING));
        fieds.addFieldDefinition(new FieldDefinition("value", BaseTypes.NUMBER));
        client.defineDataShapeDefinition(nameValues, fieds);

    }

    /**
     * init
     */
    public void init() {
        defineNameValuesDataShape();
    }

    /**
     * invokeService
     *
     * @param thingName   thingName
     * @param ServiceName ServiceName
     * @param Body        Body
     * @return String
     */
    public String invokeService(String thingName, String ServiceName, String Body) {
        String url = thingworxUrl + "Things/" + thingName + "/Services/" + ServiceName;
        WebClient webClient = webClientBuilder.build();
        String response = webClient.post().uri(url).header("appKey", baseConfig.getAppKey()).header("Accept", "application/json").header("Content-Type", "application/json").bodyValue(Body).retrieve().bodyToMono(String.class).block();
        return response;

    }

    /**
     * invokeService
     *
     * @param thingName   thingName
     * @param ServiceName ServiceName
     * @return String
     */
    public String invokeService(String thingName, String ServiceName) {

        String url = thingworxUrl + "Things/" + thingName + "/Services/" + ServiceName;
        WebClient webClient = webClientBuilder.build();
        // 拉取数据，这一部分不加锁
        String response = webClient.post().uri(url).header("appKey", baseConfig.getAppKey()).header("Accept", "application/json").header("Content-Type", "application/json").retrieve().bodyToMono(String.class).block();
        return response;

    }


}
