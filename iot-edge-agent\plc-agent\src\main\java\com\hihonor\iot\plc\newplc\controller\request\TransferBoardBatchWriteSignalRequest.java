/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
public class TransferBoardBatchWriteSignalRequest {

    @NotEmpty(message = "过板信号列表不能为空")
    @Valid
    private List<Signal> signalList;

    /**
     * 将过板信号列表转换为 Map
     *
     * @return 过板信号 Map,key 为信号名称,value 为信号值
     */
    public Map<String, String> toSignalMap() {
        return signalList.stream().collect(Collectors.toMap(Signal::getName, Signal::getValue));
    }

    @Data
    public static class Signal {

        @NotBlank(message = "信号名称不能为空")
        private String name;

        @NotBlank(message = "信号值不能为空")
        @Pattern(regexp = "^[01]$", message = "信号值只能为0或1")
        private String value;
    }
}
