/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.trafficswitch.service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.hihonor.iot.plc.trafficswitch.dto.ClusterApiResponse;
import com.hihonor.iot.plc.trafficswitch.dto.NodeInfo;
import com.hihonor.iot.plc.trafficswitch.dto.UpstreamNode;
import com.hihonor.it.apimall.sdk.handler.AuthenticationService;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 查询集群节点信息的服务。
 * <p>
 * 该服务通过HTTP API获取配置的上游服务的节点列表。
 * 它需要 {@code AuthenticationService} 来获取认证令牌，
 * 并通过配置文件中的属性来确定API端点和目标上游ID。
 * </p>
 */
@Service
@Slf4j
public class ClusterNodeService {

    private final WebClient webClient;
    @Autowired
    private AuthenticationService authenticationService; // 强依赖，如果不存在则启动失败

    @Value("${trafficswitch.api.base-url:http://agw.beta.hihonor.com}")
    private String apiBaseUrl;

    @Value("${trafficswitch.api.upstream-id}")
    private String upstreamId;

    /**
     * 构造函数，用于注入依赖。
     *
     * @param webClientBuilder 用于构建 WebClient 实例
     */
    @Autowired
    public ClusterNodeService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();

    }

    /**
     * 获取指定上游ID的集群节点列表。
     *
     * @return 节点信息列表 ({@code List<NodeInfo>})；如果获取失败或未找到节点，则返回空列表。
     */
    public List<NodeInfo> getClusterNodes() {
        log.info("开始获取集群节点信息，上游ID: {}", upstreamId);

        String token;
        try {
            // 注意：用户提供的示例中 authenticationService.getToken() 没有参数
            // String token =
            // authenticationService.getToken("your_service_identifier_if_needed");
            token = authenticationService.getToken();
            if (token == null || token.isEmpty()) {
                log.error("获取认证Token失败，Token为空。无法继续查询集群节点。");
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("调用 AuthenticationService 获取Token时发生异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
        // 根据用户提供的cURL示例，Token直接作为Authorization Header的值，不需要加"Bearer "前缀
        // 如果API网关要求Bearer Token，则应为: "Bearer " + token
        final String authTokenHeader = token;

        String url = apiBaseUrl + "/v1/upstream?id=" + upstreamId;
        log.debug("请求URL: {}", url);

        try {
            ClusterApiResponse apiResponse = webClient.get()
                    .uri(url)
                    .header(HttpHeaders.AUTHORIZATION, authTokenHeader)
                    .accept(MediaType.APPLICATION_JSON)
                    .retrieve()
                    .onStatus(status -> status.isError(), clientResponse -> {
                        log.error("查询集群节点API请求失败，状态码: {}", clientResponse.statusCode());
                        return clientResponse.bodyToMono(String.class)
                                .flatMap(body -> {
                                    log.error("API错误响应体: {}", body);
                                    return Mono.error(new RuntimeException(
                                            "API请求失败，状态码: " + clientResponse.statusCode() + ", 响应: " + body));
                                });
                    })
                    .bodyToMono(ClusterApiResponse.class)
                    .block(); // 在非WebFlux控制器中，通常会阻塞获取结果

            if (apiResponse != null && apiResponse.getUpstreams() != null && !apiResponse.getUpstreams().isEmpty()) {
                UpstreamNode firstUpstream = apiResponse.getUpstreams().get(0);
                if (firstUpstream != null && firstUpstream.getNodes() != null) {
                    log.info("成功获取并解析到 {} 个节点信息，来自上游ID: {} (名称: {})",
                            firstUpstream.getNodes().size(), firstUpstream.getId(), firstUpstream.getName());
                    return firstUpstream.getNodes();
                }
            }
            log.warn("未能从API响应中获取到有效的节点信息。上游ID: {}, 响应: {}", upstreamId, apiResponse);
            return Collections.emptyList();

        } catch (Exception e) {
            log.error("获取集群节点信息过程中发生严重错误。上游ID: {}, URL: {}, 错误: {}", upstreamId, url, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 更新指定上游服务中节点的权重。
     * <p>
     * 此方法会将匹配 {@code targetIp} 和 {@code targetPort} 的节点的权重设置为1，
     * 其他所有节点的权重设置为0。然后通过API PATCH请求更新上游服务配置。
     * </p>
     *
     * @param originalNodes 当前的节点信息列表。
     * @param targetIp      目标节点的IP地址，该节点的权重将被设置为1。
     * @param targetPort    目标节点的端口号，该节点的权重将被设置为1。
     * @return {@code true} 如果更新操作API调用成功（HTTP 2xx状态）；{@code false}
     *         如果发生任何错误或API调用失败。
     */
    public boolean updateNodeWeights(List<NodeInfo> originalNodes, String targetIp, int targetPort) {
        log.info("开始更新节点权重，目标IP: {}, 目标端口: {}. 上游ID: {}", targetIp, targetPort, upstreamId);

        if (originalNodes == null) {
            log.warn("提供的节点列表为null，无法更新权重。");
            return false; // Changed from null
        }

        List<NodeInfo> modifiedNodes = originalNodes.stream()
                .map(originalNode -> {
                    NodeInfo updatedNode = new NodeInfo();
                    updatedNode.setHost(originalNode.getHost());
                    updatedNode.setPort(originalNode.getPort());
                    updatedNode.setPriority(originalNode.getPriority());
                    updatedNode.setDescription(originalNode.getDescription());

                    if (originalNode.getHost().equals(targetIp) && originalNode.getPort() == targetPort) {
                        updatedNode.setWeight(1);
                    } else {
                        updatedNode.setWeight(0);
                    }
                    return updatedNode;
                })
                .collect(Collectors.toList());

        boolean targetNodeFoundAndExplicitlySet = modifiedNodes.stream()
                .anyMatch(n -> n.getHost().equals(targetIp) && n.getPort() == targetPort && n.getWeight() == 1);

        if (targetNodeFoundAndExplicitlySet) {
            log.info("节点 {}:{} 的权重已设置为1。其他关联节点的权重已设置为0。", targetIp, targetPort);
        } else {
            log.warn("目标节点 {}:{} 未在提供的列表中找到。所有提供的节点权重已设置为0。", targetIp, targetPort);
            return false;
        }
        log.debug("将要发送的节点列表: {}", modifiedNodes);

        String token;
        try {
            token = authenticationService.getToken();
            if (token == null || token.isEmpty()) {
                log.error("获取认证Token失败，Token为空。无法更新节点权重。");
                return false; // Changed from null
            }
        } catch (Exception e) {
            log.error("调用 AuthenticationService 获取Token时发生异常: {}", e.getMessage(), e);
            return false; // Changed from null
        }
        final String authTokenHeader = token;

        Map<String, List<NodeInfo>> payload = Collections.singletonMap("nodes", modifiedNodes);
        String url = apiBaseUrl + "/v1/upstream/" + upstreamId;
        log.debug("PATCH请求URL: {}", url);

        try {
            // Retrieve the body as a String first to handle cases where the body might not
            // be SwitchTrafficResponse
            // or is empty, but still a 2xx success.
            return webClient.patch()
                    .uri(url)
                    .header(HttpHeaders.AUTHORIZATION, authTokenHeader)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(payload)
                    .retrieve()
                    .onStatus(status -> status.isError(), clientResponse -> // Handles 4xx/5xx errors
                    clientResponse.bodyToMono(String.class)
                            .defaultIfEmpty("[无响应体]") // Provide a default for empty error bodies
                            .flatMap(errorBody -> {
                                log.error("更新节点权重API请求失败，状态码: {}, 响应体: {}", clientResponse.statusCode(), errorBody);
                                return Mono.error(new RuntimeException("API请求失败，状态码: " + clientResponse.statusCode()));
                            }))
                    .bodyToMono(String.class) // Get response body as String to check for actual content later if needed
                    .doOnSuccess(responseBody -> {
                        // Try to parse SwitchTrafficResponse for logging, but don't fail if it's not
                        // parsable or empty for a 2xx
                        try {
                            // This is a bit of a hack to log if possible. Better would be to check headers
                            // or use a more generic DTO.
                            if (responseBody != null && !responseBody.trim().isEmpty()
                                    && responseBody.trim().startsWith("{")) {
                                // A more robust way would be to use ObjectMapper to try parsing
                                log.info("更新节点权重API调用成功 (HTTP 2xx)。响应体 (尝试解析): {}", responseBody);
                                // ObjectMapper objectMapper = new ObjectMapper();
                                // SwitchTrafficResponse resp = objectMapper.readValue(responseBody,
                                // SwitchTrafficResponse.class);
                                // log.info("成功更新节点权重。上游ID: {}, 任务ID: {}", resp.getId(), resp.getJobId());
                            } else {
                                log.info("更新节点权重API调用成功 (HTTP 2xx)，响应体为空或非JSON对象。");
                            }
                        } catch (Exception parseEx) {
                            log.warn("更新节点权重API调用成功 (HTTP 2xx)，但响应体无法解析为SwitchTrafficResponse: {}. 原始响应体: {}",
                                    parseEx.getMessage(), responseBody);
                        }
                    })
                    .map(responseBody -> true) // If onStatus didn't throw an error, it's a success (HTTP 2xx)
                    .onErrorResume(throwable -> {
                        // This catches errors from onStatus (re-thrown) or other WebClient issues
                        log.error("更新节点权重过程中发生错误(WebClient Pipeline): {}", throwable.getMessage());
                        return Mono.just(false);
                    })
                    .block(); // Block for the boolean result

        } catch (Exception e) {
            log.error("更新节点权重时发生同步执行错误: {}", e.getMessage(), e);
            return false; // Changed from null
        }
    }

    /**
     * 设置指定IP和端口的节点为主要节点（权重为1），并更新集群中其他节点的权重为0。
     * <p>
     * 此方法首先查询当前的集群节点信息，然后调用更新权重的方法。
     * </p>
     *
     * @param targetIp   目标主节点的IP地址。
     * @param targetPort 目标主节点的端口号。
     * @return {@code true} 如果查询节点列表和更新权重两个操作均成功；否则返回 {@code false}。
     */
    public boolean setPrimaryNodeAndUpdateWeights(String targetIp, int targetPort) {
        log.info("开始编排操作：设置主节点并更新权重。目标IP: {}, 端口: {}. 上游ID: {}", targetIp, targetPort, upstreamId);

        List<NodeInfo> currentNodes;
        try {
            currentNodes = getClusterNodes();
        } catch (Exception e) {
            log.error("编排操作失败：调用 getClusterNodes() 时发生意外异常: {}", e.getMessage(), e);
            return false;
        }

        // getClusterNodes() returns an empty list on failure or if no nodes are found.
        // A null check is defensive; isEmpty is the primary concern for proceeding.
        if (currentNodes == null || currentNodes.isEmpty()) {
            log.warn("编排操作中断：未能获取到有效的当前节点列表 (getClusterNodes 返回空列表或null)。无法继续更新权重。");
            return false;
        }

        log.info("编排操作：成功获取到 {} 个当前节点。准备为目标 {}:{} 更新权重。", currentNodes.size(), targetIp, targetPort);

        boolean updateSuccessful;
        try {
            updateSuccessful = updateNodeWeights(currentNodes, targetIp, targetPort);
        } catch (Exception e) {
            log.error("编排操作失败：调用 updateNodeWeights() 时发生意外异常: {}", e.getMessage(), e);
            return false;
        }

        if (updateSuccessful) {
            log.info("编排操作成功完成：主节点设置和权重更新成功。目标IP: {}, 端口: {}", targetIp, targetPort);
        } else {
            log.warn("编排操作未成功：updateNodeWeights 方法返回 false。目标IP: {}, 端口: {}", targetIp, targetPort);
        }
        return updateSuccessful;
    }

}