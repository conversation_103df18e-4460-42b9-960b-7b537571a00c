/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.thing;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.sdk.entity.EntityThing;
import com.thingworx.communications.client.ConnectedThingClient;
import com.thingworx.relationships.RelationshipTypes;
import com.thingworx.types.InfoTable;
import com.thingworx.types.collections.ValueCollection;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
// 使用Slf4j来注入日志对象
@Slf4j
@Component
public class ThingAgent {

    @Autowired
    private ApplicationContext applicationContext;  // Spring应用上下文
    @Autowired
    ConnectedThingClient client;

    @Autowired
    ThingManagement thingManagement;

    /**
     * 初始化THING
     */
    public void initThing() {
        try {
            // 创建一个集合来保存getThingList返回的所有THING名称
            Set<String> newThingNames = new HashSet<>();

            // 1. 扫描带有@ThingTemplate注解的所有类
            Map<String, Object> beansWithAnnotation = applicationContext.getBeansWithAnnotation(ThingTemplate.class);
            for (Map.Entry<String, Object> entry : beansWithAnnotation.entrySet()) {
                Object bean = entry.getValue();
                ThingTemplate annotation = bean.getClass().getAnnotation(ThingTemplate.class);
                // 2. 根据注解值来拉取THING的信息
                List<String> things = getThingList(annotation.value(), annotation.tag());
                newThingNames.addAll(things);
                for (String name : things) {
                    if (thingManagement.thingMap.containsKey(name)) {
                        continue;
                    }
                    log.info(" Add thing: {}", name);
                    // 3. 动态创建并初始化实例
                    EntityThing entityThing = (EntityThing) bean.getClass().getConstructor().newInstance();
                    // 手动进行依赖注入
                    applicationContext.getAutowireCapableBeanFactory().autowireBean(entityThing);
                    org.json.JSONObject jsonObject = new org.json.JSONObject();
                    jsonObject.put("name", name);
                    new Thread(() -> {
                        try {
                            entityThing.initialize(jsonObject);
                            thingManagement.thingMap.put(name, entityThing);
                        } catch (Exception ex) {
                            log.error("General error in initializing thing: {}", ex.getMessage());
                            // 处理其他潜在的异常
                        }
                    }).start();

                }
            }
            // 删除在新列表中不存在的THING
            Iterator<Map.Entry<String, EntityThing>> iterator = thingManagement.thingMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, EntityThing> entry = iterator.next();
                String name = entry.getKey();
                // 如果THING名称不在新获取的列表中
                if (!newThingNames.contains(name)) {
                    // 执行销毁操作（根据您的具体需求来实现）
                    EntityThing entityThing = entry.getValue();
                    entityThing.destroy();
                    // 从thingManagement.thingMap中删除
                    iterator.remove();
                    log.info(" Remove thing: {}", name);
                }
            }
            // 其他初始化代码...
        } catch (JSONException ex) {
            log.error("JSON error during initialization: {}", ex.getMessage());
            // 处理 JSON 异常
        } catch (Exception ex) {
            log.error("initThing error: {}", ex.getMessage());
        }
    }


    /**
     * 获取THING列表
     *
     * @param thingTemplate 模板
     * @param tag           标签
     * @return THING列表
     */
    List<String> getThingList(String thingTemplate, String tag) {
        List<String> stringList = new ArrayList<>();
        try {
            ValueCollection valueCollection = new ValueCollection();
            valueCollection.SetStringValue("thingTemplate", thingTemplate);
            valueCollection.SetStringValue("tags", tag);
            InfoTable res = client.invokeService(RelationshipTypes.ThingworxEntityTypes.Resources, "SearchFunctions", "SearchThingsByTemplate", valueCollection, 2000);
            for (int i = 0; i < res.getLength(); i++) {

                ValueCollection valueCollection1 = res.getRow(i);
                JSONObject jsonObject = valueCollection1.toJSON();
                String thingName = getName(jsonObject);
                String tag1 = getTag(jsonObject);
                stringList.add(thingName);
                log.info("getThingList thingName:{},tag:{}", thingName, tag1);
            }
            log.info("getThingList res:{}", stringList);
        } catch (Exception ex) {
            log.error("getThingList error:{}", ex.getMessage());
        }
        return stringList;
    }

    // 获取名称
    String getName(JSONObject jsonObject) {
        try {
            return jsonObject.getString("name");
        } catch (JSONException e) {
            log.error("getName error:{}", e.getMessage());
        }
        return null;

    }

    String getTag(JSONObject jsonObject) {
        try {
            JSONArray tags = jsonObject.getJSONArray("tags");
            List<String> mergedTags = new ArrayList<>();
            for (int i = 0; i < tags.length(); i++) {
                JSONObject tag = tags.getJSONObject(i);
                String mergedTag = tag.getString("vocabulary") + ":" + tag.getString("vocabularyTerm");
                mergedTags.add(mergedTag);
            }
            return mergedTags.get(0);

        } catch (Exception ex) {
            log.error("getTag error:{}", ex.getMessage());
            return null;
        }


    }


}


