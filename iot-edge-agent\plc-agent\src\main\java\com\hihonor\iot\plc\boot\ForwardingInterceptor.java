/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot;

import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * ForwardingInterceptor类用于实现请求转发的拦截器。
 * 当控制器方法上有@ForwardToNodes注解时，该拦截器将拦截并转发请求。
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Slf4j
@Component
@DependsOn("clusterManager")
public class ForwardingInterceptor implements HandlerInterceptor {

    @Autowired
    private ClusterManager clusterManager; // ClusterManager 用于获取集群信息


    // 在请求头中设置的标志，用于指示请求已被转发
    private static final String HEADER_FORWARD_FLAG = "X-Forwarded-By";
    @Autowired
    @Lazy
    private WebClient webClient;


    /**
     * preHandle方法用于在控制器方法执行前进行拦截。
     * 它会检查控制器方法上是否有@ForwardToNodes注解，如果有则进行请求转发。
     *
     * @param request  HttpServletRequest对象
     * @param response HttpServletResponse对象
     * @param handler  Object，处理请求的控制器方法
     * @return true    请求正常继续
     * @throws IOException 可能抛出IO异常
     */

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        BufferedReader reader = null;

        try {
            Mono<String> resmono = null;

            // 这里检查该请求是否需要转发
            if (shouldForward(handler) && !isForwarded(request)) {
                String requestBody = "";
                reader = request.getReader();
                requestBody = reader.lines().collect(Collectors.joining(System.lineSeparator()));
                log.info("Request {} identified for forwarding", request.getRequestURI());
                // 获取所有节点信息
                ConcurrentHashMap<String, ClusterManager.NodeInfo> nodeInfoMap = clusterManager.NODE_INFO_CONCURRENT_HASH_MAP;
                // 获取请求的URI
                String endpoint = request.getRequestURI();
                // 遍历所有节点并转发请求
                for (ClusterManager.NodeInfo nodeInfo : nodeInfoMap.values()) {
                    String nodeUrl = "http://" + nodeInfo.getIp() + ":" + nodeInfo.getPort() + endpoint;
                    try {
                        Mono<String> temp = forwardRequest(request, nodeUrl, requestBody);
                        if (clusterManager.isLocalNode(nodeInfo.getNodeInfo())) {
                            resmono = temp;
                        } else {
                            temp.subscribe(response1 -> {
                                log.info(" forwoard Response received: {}", response1);
                            }, error -> {
                                log.error(" forwoard eError making request: {}", error.getMessage());
                            }, () -> {
                                log.info(" forwoard complete");
                            });
                        }
                    } catch (Exception e) {
                        log.error("Forwarding request to {} failed: {}", nodeUrl, e.getMessage());
                        // 在这里你可以选择抛出异常或处理异常
                    }
                    log.debug("Forwarding request to {}", nodeUrl);


                }
                response.setContentType("application/json; charset=UTF-8");

                if (resmono == null) {
                    return false;
                }
                String result = resmono.block();
                if (result != null) {
                    response.getWriter().write(result);
                }
                return false;

            } else {
                log.info("Request {} does not require forwarding", request.getRequestURI());
            }
        } catch (Exception ex) {
            log.info("does not require forwarding{}", ex.getMessage());

        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error("close reader error:{}", e.getMessage());
                }
            }
        }
        return true; // 返回true，让请求继续处理

    }

    /**
     * 判断请求是否已经被转发过。
     *
     * @param request 当前的HttpServletRequest对象
     * @return 如果请求已经包含转发标志，则返回true；否则返回false。
     */
    private boolean isForwarded(HttpServletRequest request) {

        return request.getHeader(HEADER_FORWARD_FLAG) != null;
    }


    /**
     * shouldForward方法用于判断是否应转发请求。
     *
     * @param handler 处理请求的控制器方法
     * @return 是否应转发请求
     */
    private boolean shouldForward(Object handler) {
        if (handler instanceof HandlerMethod) {
            Method method = ((HandlerMethod) handler).getMethod();
            // 检查方法是否有@ForwardToNodes注解
            return method.isAnnotationPresent(ForwardToNodes.class);
        }
        return false;
    }


    /**
     * 使用WebClient转发请求到指定URL。
     *
     * @param request    来自客户端的HttpServletRequest对象。
     * @param forwardUrl 需要转发到的目标URL。
     * @param body       请求体
     * @return 转发操作的响应内容。
     */
    public Mono<String> forwardRequest(HttpServletRequest request, String forwardUrl, String body) {
        // 创建一个 MultiValueMap 来存储请求头
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.put(HEADER_FORWARD_FLAG, Collections.singletonList("1"));
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.addAll(headerName, Collections.list(request.getHeaders(headerName)));
        }

        // 读取请求体
        String requestBody = body;

        // 解析请求方法
        HttpMethod resolvedMethod = HttpMethod.resolve(request.getMethod());
        if (resolvedMethod == null) {
            log.error("Unsupported HTTP method");
            throw new UnsupportedOperationException("Unsupported HTTP method");
        }

        // 使用 WebClient 发送转发的请求
        Mono<String> responseMono = webClient.method(resolvedMethod).uri(forwardUrl)
                .headers(httpHeaders -> httpHeaders.addAll(headers)).bodyValue(requestBody).retrieve().bodyToMono(String.class);
        return responseMono;
        // 同步等待响应
    }

    private boolean isUrlValid(String url) {
        return true;
    }

    /**
     * 从HttpServletRequest读取请求体。
     *
     * @param request HttpServletRequest对象。
     * @return 请求体的字符串表示。
     * @throws Exception 如果无法读取请求体。
     */
    private String readRequestBody(HttpServletRequest request) throws Exception {
        // 确保请求被ContentCachingRequestWrapper封装
        if (!(request instanceof ContentCachingRequestWrapper)) {
            throw new IllegalArgumentException("Request is not a ContentCachingRequestWrapper");
        }

        // 将请求转换为ContentCachingRequestWrapper以访问缓存的内容
        ContentCachingRequestWrapper cachingWrapper = (ContentCachingRequestWrapper) request;

        // 从缓存中获取请求体
        byte[] buf = cachingWrapper.getContentAsByteArray();

        // 检查是否实际读取到了数据
        if (buf.length == 0) {
            return "";
        }

        // 根据请求的编码来创建String，如果没有指定，默认为UTF-8
        String characterEncoding = cachingWrapper.getCharacterEncoding();
        if (characterEncoding == null) {
            characterEncoding = StandardCharsets.UTF_8.name();
        }

        // 将字节数组转换为字符串
        return new String(buf, characterEncoding);
    }


}