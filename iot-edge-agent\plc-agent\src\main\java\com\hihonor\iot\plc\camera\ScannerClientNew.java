/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
//ScannerClientNew 类主要用于与扫描枪设备进行通信和交互。它提供了以下主要功能：
//连接管理：
//connect(int timeout): 尝试连接到扫描枪设备，支持连接超时设置。
//closeConnection(): 关闭与扫描枪的连接，包括关闭 SocketChannel 和 Selector。
//命令发送与响应接收：
//sendCommand(String command, int timeout): 发送命令到扫描枪并接收响应。
//sendCommandAndReceiveResponse(String command, int timeout): 内部方法，用于发送命令并接收响应，处理写入和读取操作。
//心跳检测：
//sendHeartbeat(int timeout): 发送心跳命令（PING）以检查设备是否在线。
//条码读取：
//readBarcode(): 读取扫描枪返回的条码。
//LON命令发送：
//sendLonCommand(): 发送 LON 命令到扫描枪并接收响应。
//连接状态检查：
//isConnected(): 检查当前是否已连接到扫描枪。
//资源释放：
//close(): 关闭 SocketChannel 和 Selector，释放资源。
//该类通过 SocketChannel 和 Selector 实现非阻塞 I/O 操作，确保高效的网络通信。
@Slf4j
public class ScannerClientNew {
    private SocketChannel socketChannel;
    private Selector selector;
    private ByteBuffer readBuffer = ByteBuffer.allocate(1024);
    private ByteBuffer writeBuffer = ByteBuffer.allocate(1024);

    private static final String LON_COMMAND = "LON\r";

    private volatile boolean connected = false;
    private String host;
    private int port;

    public boolean isConnected() {
        return   socketChannel!=null && socketChannel.isOpen()&&connected&& socketChannel.isConnected();
    }

    public ScannerClientNew(String host, int port) {
        this.host = host;
        this.port = port;
    }

    private void initializeChannel() throws IOException {
        if (socketChannel == null || !socketChannel.isOpen()) {
            socketChannel = SocketChannel.open();
            socketChannel.configureBlocking(false);
            selector = Selector.open();
        }
    }


    /**
     * 尝试连接到扫描枪。
     * <p>
     * 连接的逻辑流程如下:
     * 1. 检查是否已经连接且SocketChannel不为null,如果是,直接返回true,表示连接成功。
     * 2. 如果没有连接或SocketChannel为null,则关闭之前的连接(如果有)。
     * 3. 创建一个新的SocketChannel,并设置为非阻塞模式。
     * 4. 尝试连接到指定的主机和端口,并设置连接超时时间。
     * 5. 如果连接成功,设置connected标志为true,并打印连接成功的日志,返回true。
     * 6. 如果连接失败,打印连接失败的错误日志,将SocketChannel设置为null,设置connected标志为false,返回false。
     *
     * @param timeout 连接超时时间(毫秒)。
     * @return 如果连接成功, 返回true;否则返回false。
     */
    public boolean connect(int timeout) {
        // 如果已经连接且SocketChannel不为null,直接返回true
        if ( socketChannel != null &&connected&& socketChannel.isOpen() && socketChannel.isConnected()) {
            return true;
        }
        // 关闭之前的连接(如果有)
        closeConnection();
        try {
            // 初始化SocketChannel和Selector
            initializeChannel();
            // 尝试连接到指定的主机和端口
            socketChannel.connect(new InetSocketAddress(host, port));
            // 注册连接事件到Selector
            socketChannel.register(selector, SelectionKey.OP_CONNECT);

            long startTime = System.currentTimeMillis();
            while (true) {
                // 检查是否超时
                if (System.currentTimeMillis() - startTime >= timeout) {
                    throw new IOException("Connection timed out");
                }

                // 等待连接准备就绪
                if (selector.select(timeout) == 0) {
                    continue;
                }

                Set<SelectionKey> selectedKeys = selector.selectedKeys();
                Iterator<SelectionKey> iterator = selectedKeys.iterator();
                while (iterator.hasNext()) {
                    SelectionKey key = iterator.next();
                    iterator.remove();
                    if (key.isConnectable()) {
                        SocketChannel channel = (SocketChannel) key.channel();
                        if (channel.finishConnect()) {
                            // 连接成功,设置connected标志为true
                            connected = true;
                            log.info("Connected to scanner at {}:{}", host, port);
                            return true;
                        } else {
                            // 连接失败
                            throw new IOException("Failed to connect to server");
                        }
                    }
                }
            }
        } catch (IOException e) {
            // 连接失败,打印错误日志
            log.error("Failed to connect to scanner at {}:{}", host, port, e);
            // 将SocketChannel设置为null,并设置connected标志为false
            socketChannel = null;
            connected = false;
            return false;
        }
    }


    /**
     * 发送心跳命令以检查设备是否在线。
     * <p>
     * 发送心跳命令的逻辑流程如下:
     * 1. 发送一个错误的指令到扫描枪。
     * 2. 接收扫描枪的响应。
     * 3. 如果收到响应，返回true表示设备在线。
     * 4. 如果没有收到响应或发生异常，返回false表示设备可能离线。
     *
     * @param timeout 命令发送和响应接收的超时时间(毫秒)。
     * @return 如果设备在线, 返回true;否则返回false。
     */
//    public boolean sendHeartbeat(int timeout) {
//        final String command = "INVALID_COMMAND"; // 发送一个错误的指令
//        try {
//            // 发送错误指令并接收响应
//            String response = sendCommand(command, timeout);
//            if (response != null && !response.isEmpty()) {
//                // 假设响应的最后一个字符是CR，我们需要移除它
//                if (response.endsWith("\r")) {
//                    response = response.substring(0, response.length() - 1);
//                }
//                log.info("收到心跳响应: {}", response);
//                connected = true;
//                return true; // 如果收到响应，返回true表示设备在线
//            } else {
//                log.warn("没有收到心跳命令的响应。");
//                connected = false;
//                return false; // 如果没有收到响应，返回false表示设备可能离线
//            }
//        } catch (Exception e) {
//            connected = false;
//            log.error("发送心跳命令或接收响应时发生错误", e);
//            return false; // 如果发生异常，返回false表示设备可能离线
//        }
//    }


    /**
     * 使用 PING 命令检查设备是否在线。
     *
     * @param timeout PING 操作的超时时间（毫秒）
     * @return 如果设备在线，返回 true；否则返回 false
     */
    public boolean sendHeartbeat(int timeout) {
        try {
            InetAddress address = InetAddress.getByName(host);
            log.info("正在 PING {}...", host);

            boolean reachable = address.isReachable(timeout);

            if (reachable) {
                log.info("PING {} 成功，设备在线", host);
                connected = true;
                return true;
            } else {
                log.warn("PING {} 失败，设备可能离线", host);
                connected = false;
                return false;
            }
        } catch (IOException e) {
            log.error("PING {} 时发生错误", host, e);
            connected = false;
            return false;
        }
    }




    /**
     * 发送命令到扫描枪并接收响应。
     * <p>
     * 发送命令的逻辑流程如下:
     * 1. 调用connect()方法尝试连接到扫描枪,如果连接失败,返回null。
     * 2. 调用sendCommandAndReceiveResponse()方法发送命令并接收响应。
     * 3. 如果发送命令和接收响应成功,打印发送的命令和接收到的响应日志,并返回响应结果。
     * 4. 如果发送命令或接收响应过程中发生IOException,设置connected标志为false,打印发送命令或接收响应失败的错误日志,并返回null。
     * <p>
     * 该方法提供了发送命令到扫描枪并接收响应的功能,用于与扫描枪进行数据交互。
     *
     * @param command 要发送的命令。
     * @param timeout 命令发送和响应接收的超时时间(毫秒)。
     * @return 如果发送命令和接收响应成功, 返回响应结果;否则返回null。
     */
    public String sendCommand(String command, int timeout) {
        // 尝试连接到扫描枪,如果连接失败,返回null
        if (!connect(timeout)) {
            return null;
        }

        try {
            // 发送命令并接收响应
            String response = sendCommandAndReceiveResponse(command, timeout);
            log.info("Sent command: {}, Received response: {}", command, response);
            return response;
        } catch (Exception e) {
            // 发送命令或接收响应失败,设置connected标志为false
            connected = false;
            log.error("Failed to send command or receive response", e);
            return null;
        }
    }


    /**
     * 发送命令到扫描枪并接收响应。
     * <p>
     * 发送命令和接收响应的逻辑流程如下:
     * 1. 初始化SocketChannel和Selector。
     * 2. 注册写事件到Selector。
     * 3. 将命令写入writeBuffer。
     * 4. 在超时时间内循环等待写入和读取操作完成。
     * 5. 如果写入完成，切换到读取模式。
     * 6. 如果读取到完整的响应，返回响应结果。
     * 7. 如果超时，抛出IOException。
     *
     * @param command 要发送的命令。
     * @param timeout 命令发送和响应接收的超时时间(毫秒)。
     * @return 如果发送命令和接收响应成功, 返回响应结果;否则返回null。
     * @throws IOException 如果在发送命令或接收响应过程中发生IO异常。
     */
    private String sendCommandAndReceiveResponse(String command, int timeout) throws IOException {
        // 初始化SocketChannel和Selector
        initializeChannel();
        // 注册写事件到Selector
        socketChannel.register(selector, SelectionKey.OP_WRITE);

        long startTime = System.currentTimeMillis();
        // 清空writeBuffer,将命令转换为字节数组并写入writeBuffer
        writeBuffer.clear();
        writeBuffer.put((command + "\r").getBytes(StandardCharsets.US_ASCII));
        writeBuffer.flip();

        boolean writing = true;
        boolean reading = false;
        StringBuilder response = new StringBuilder();

        while (true) {
            // 检查是否超时
            if (System.currentTimeMillis() - startTime > timeout) {
                throw new IOException("命令操作超时");
            }

            // 等待通道准备就绪
            selector.select(timeout);
            Set<SelectionKey> selectedKeys = selector.selectedKeys();
            Iterator<SelectionKey> iter = selectedKeys.iterator();

            while (iter.hasNext()) {
                SelectionKey key = iter.next();

                if (key.isWritable() && writing) {
                    // 写入命令到SocketChannel
                    socketChannel.write(writeBuffer);
                    if (!writeBuffer.hasRemaining()) {
                        // 写完后准备读
                        key.interestOps(SelectionKey.OP_READ);
                        writing = false;
                        reading = true;
                        readBuffer.clear();
                    }
                } else if (key.isReadable() && reading) {
                    // 从SocketChannel读取响应
                    if (socketChannel.read(readBuffer) > 0) {
                        readBuffer.flip();
                        while (readBuffer.hasRemaining()) {
                            char character = (char) readBuffer.get();
                            response.append(character);
                            if (character == '\r') {
                                log.debug("收到响应: {}", response);
                                return response.toString();
                            }
                        }
                        readBuffer.clear();
                    }
                }
                iter.remove();
            }
        }
    }


    /**
     * 发送LON命令到扫描枪并接收响应。
     * <p>
     * 发送LON命令的逻辑流程如下:
     * 1. 初始化SocketChannel和Selector。
     * 2. 将LON命令写入缓冲区。
     * 3. 在超时时间内循环等待写入和读取操作完成。
     * 4. 如果写入完成，切换到读取模式。
     * 5. 如果读取到完整的响应，返回响应结果。
     *
     * @return 如果发送LON命令和接收响应成功, 返回响应结果;否则返回null。
     */
    public String sendLonCommand() {
        try {
            // 初始化SocketChannel和Selector
            initializeChannel();
            // 将LON命令写入缓冲区
            ByteBuffer buffer = ByteBuffer.wrap(LON_COMMAND.getBytes(StandardCharsets.US_ASCII));
            socketChannel.register(selector, SelectionKey.OP_WRITE);

            // 写数据
            while (buffer.hasRemaining()) {
                selector.select();
                Set<SelectionKey> selectedKeys = selector.selectedKeys();
                Iterator<SelectionKey> iterator = selectedKeys.iterator();
                while (iterator.hasNext()) {
                    SelectionKey key = iterator.next();
                    iterator.remove();
                    if (key.isWritable()) {
                        socketChannel.write(buffer);
                    }
                }
            }
            // 注册读事件
            buffer.clear();
            socketChannel.register(selector, SelectionKey.OP_READ);

            // 接收扫描枪返回的条码
            StringBuilder response = new StringBuilder();
            while (true) {
                if (!isConnected()) {
                    return null;
                }
                selector.select();
                Set<SelectionKey> selectedKeys = selector.selectedKeys();
                Iterator<SelectionKey> iterator = selectedKeys.iterator();
                while (iterator.hasNext()) {
                    SelectionKey key = iterator.next();
                    iterator.remove();
                    if (key.isReadable()) {
                        int bytesRead = socketChannel.read(buffer);
                        if (bytesRead > 0) {
                            buffer.flip();
                            byte[] data = new byte[buffer.remaining()];
                            buffer.get(data);
                            response.append(new String(data, StandardCharsets.US_ASCII));
                            buffer.clear();
//                            if (response.toString().endsWith("\r")) {
                            return response.toString().trim();
                            //    }

                        } else if (bytesRead == -1) {
                            return null;
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("发送LON命令或接收条码失败", e);
            return null;
        }
    }

    /**
     * 读取扫描枪返回的条码。
     * <p>
     * 读取条码的逻辑流程如下:
     * 1. 初始化SocketChannel和Selector。
     * 2. 注册读事件到Selector。
     * 3. 在超时时间内循环等待读取操作完成。
     * 4. 如果读取到完整的条码，返回条码结果。
     *
     * @return 如果读取条码成功, 返回条码结果;否则返回null。
     */
    public String readBarcode() {
        try {
            initializeChannel();
            if(!isConnected())
            {
               if(!connect(2000))
                   return null;
            }
            // 初始化SocketChannel和Selector

            // 注册读事件到Selector
            socketChannel.register(selector, SelectionKey.OP_READ);

            ByteBuffer buffer = ByteBuffer.allocate(1024);
            StringBuilder response = new StringBuilder();

            // 接收扫描枪返回的条码
            while (true) {
                if (!isConnected()) {
                    return null;
                }
            //    selector.select();
                if (selector.select(30000) == 0)
                {
                    log.debug("等待扫描枪响应超时 ip:{} ,name:{}",host);
                    continue;
                }
                Set<SelectionKey> selectedKeys = selector.selectedKeys();
                Iterator<SelectionKey> iterator = selectedKeys.iterator();
                while (iterator.hasNext()) {
                    SelectionKey key = iterator.next();
                    iterator.remove();
                    if (key.isReadable()) {
                        int bytesRead = socketChannel.read(buffer);
                        if (bytesRead > 0) {
                            buffer.flip();
                            byte[] data = new byte[buffer.remaining()];
                            buffer.get(data);
                            response.append(new String(data, StandardCharsets.US_ASCII));
                            buffer.clear();
                           // if (response.toString().endsWith("\r")) {
                            return response.toString().trim();
                            //}

                        } else if (bytesRead == -1) {
                            connected=false;
                            return null;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("读取条码失败", e);
            connected=false;
            return null;
        }
    }

//    public String readBarcode() {
//        try {
//            // 初始化SocketChannel和Selector
//            initializeChannel();
//            // 注册读事件到Selector
//            socketChannel.register(selector, SelectionKey.OP_READ);
//
//            ByteBuffer buffer = ByteBuffer.allocate(1024);
//            StringBuilder response = new StringBuilder();
//
//            // 接收扫描枪返回的条码
//            while (true) {
//                if (!isConnected()) {
//                    return null;
//                }
//
//                selector.select();
//                Set<SelectionKey> selectedKeys = selector.selectedKeys();
//                Iterator<SelectionKey> iterator = selec2
//                        int bytesRead = socketChannel.read(buffer);
//                        if (bytesRead > 0) {
//                            buffer.flip();
//                            byte[] data = new byte[buffer.remaining()];
//                            buffer.get(data);
//                            response.append(new String(data, StandardCharsets.US_ASCII));
//                            buffer.clear();
//                            // 检查是否包含逗号
//                            if (response.toString().contains(",")) {
//                                return response.toString().split(",")[0].trim();
//                            }
//                        } else if (bytesRead == -1) {
//                            return null;
//                        }
//                    }
//                }
//            }
//        } catch (IOException e) {
//            log.error("读取条码失败", e);
//            return null;
//        }
//    }


    public void close() {
        try {
            if (socketChannel != null) {
                socketChannel.close();
            }
            if (selector != null) {
                selector.close();
            }
            socketChannel = null;
        } catch (IOException e) {
            log.info("Failed to close connection to scanner at " + host + ":" + port);
        } finally {
            connected = false;
        }
    }


    /**
     * 关闭与扫描枪的连接。
     * <p>
     * 关闭连接的逻辑流程如下:
     * 1. 检查SocketChannel是否不为null且处于打开状态。
     * 2. 如果满足条件,尝试关闭SocketChannel。
     * 3. 如果关闭过程中发生IOException,打印关闭连接失败的错误日志。
     * 4. 检查Selector是否不为null且处于打开状态。
     * 5. 如果满足条件,尝试关闭Selector。
     * 6. 如果关闭过程中发生IOException,打印关闭Selector失败的错误日志。
     * 7. 设置connected标志为false。
     */
    public void closeConnection() {
        // 检查SocketChannel是否不为null且处于打开状态
        if (socketChannel != null && socketChannel.isOpen()&&connected&&socketChannel.isConnected()) {
            try {
                // 尝试关闭SocketChannel
                socketChannel.close();
                socketChannel=null;
            } catch (IOException e) {
                // 如果关闭过程中发生IOException,打印关闭连接失败的错误日志
                log.error("关闭连接到扫描枪失败 {}:{}", host, port, e);
            }
        }
        // 检查Selector是否不为null且处于打开状态
        if (selector != null && selector.isOpen()) {
            try {
                // 尝试关闭Selector
                selector.close();
                selector=null;
            } catch (IOException e) {
                // 如果关闭过程中发生IOException,打印关闭Selector失败的错误日志
                log.error("关闭Selector失败", e);
            }
        }
        // 设置connected标志为false
        connected = false;
    }
}

