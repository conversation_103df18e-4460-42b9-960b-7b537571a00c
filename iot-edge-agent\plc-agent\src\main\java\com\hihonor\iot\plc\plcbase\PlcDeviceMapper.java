/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Mapper
public interface PlcDeviceMapper extends BaseMapper<PlcDevice> {
    // 这里可以添加其他自定义的数据库操作方法，但基本的CRUD操作已由BaseMapper提供

    @Select("<script>" +
            "SELECT * FROM iot_admin.plc_device " +
            "<where>" +
            "  <if test='plcDeviceType != null and plcDeviceType != \"\"'>" +
            "    AND plc_device_type = #{plcDeviceType} " +
            "  </if>" +
            "  <if test='plcDeviceName != null and plcDeviceName != \"\"'>" +
            "    AND plc_device_name LIKE CONCAT('%', #{plcDeviceName}, '%') " +
            "  </if>" +
            "  <if test='plcTag != null and plcTag != \"\"'>" +
            "    AND plc_tag LIKE CONCAT('%', #{plcTag}, '%') " +
            "  </if>" +
            "  <if test='connectionStatus != null'>" +
            "    AND connection_status = #{connectionStatus} " +
            "  </if>" +
            "</where>" +
            "ORDER BY last_communication DESC" +
            "</script>")
    IPage<PlcDevice> searchByCriteria(Page<PlcDevice> page,
                                      @Param("plcDeviceType") String plcDeviceType,
                                      @Param("plcDeviceName") String plcDeviceName,
                                      @Param("plcTag") String plcTag,
                                      @Param("connectionStatus") Boolean connectionStatus);


    @Delete("DELETE FROM iot_admin.plc_device WHERE plc_device_name = #{plcDeviceName}")
    int deleteByDeviceName(@Param("plcDeviceName") String plcDeviceName);

    @Select("SELECT COUNT(*) FROM iot_admin.plc_device WHERE plc_device_name = #{plcDeviceName}")
    int countByDeviceName(String plcDeviceName);

    @Update("UPDATE iot_admin.plc_device SET last_communication = now() WHERE plc_device_name = #{plcDeviceName}")
    int updateLastCommunicationTime(@Param("plcDeviceName") String plcDeviceName);

    @Update("<script>" +
            "UPDATE iot_admin.plc_device SET " +
            "  connection_status = CASE " +
            "    WHEN last_communication > NOW() - INTERVAL '${minutes} MINUTE' THEN true " +
            "    ELSE false " +
            "  END" +
            "</script>")
    int updateConnectionStatusByLastCommunication(@Param("minutes") int minutes);
}