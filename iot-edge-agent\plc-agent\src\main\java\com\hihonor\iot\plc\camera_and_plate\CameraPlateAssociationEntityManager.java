/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Slf4j
@Service
public class CameraPlateAssociationEntityManager {
    @Autowired
    private CameraPlateService cameraPlateService;
    @Autowired
    ApplicationContext applicationContext;

    private Map<String, CameraPlateAssociationEntity> associationEntityMap = new ConcurrentHashMap<>();

    /**
     * 创建并初始化一个新的CameraPlateAssociationEntity实体。
     * 根据提供的相机设备名称和过板台设备名称尝试创建和初始化实体。
     * 如果实体成功初始化，则将其添加到管理映射中。
     *
     * @param cameraDeviceName 相机设备的名称
     * @param plateDeviceName  过板台设备的名称
     * @return boolean 初始化是否成功
     */
    public boolean createAndInitializeEntity(String cameraDeviceName, String plateDeviceName,int trackNumber) {
        CameraPlateAssociationEntity entity = applicationContext.getBean(CameraPlateAssociationEntity.class);
        boolean initialized = entity.initialize(cameraDeviceName, plateDeviceName,trackNumber);
        if (initialized) {
            String key = generateKey(cameraDeviceName,plateDeviceName);
            associationEntityMap.put(key, entity);
            log.info("新实体创建并初始化成功，相机: {}，过板台: {}", cameraDeviceName, plateDeviceName);
            return true;
        } else {
            log.warn("实体创建或初始化失败，相机: {}，过板台: {}", cameraDeviceName, plateDeviceName);
            return false;
        }
    }

    public  void reStrart()
    {
        for(CameraPlateAssociationEntity entity : associationEntityMap.values())
        {
            try {
                entity.stop();
                entity.getCamera().reConnect();
                entity.start();
            }
            catch (Exception ex)
            {
                log.error("重启相机失败",ex);
            }

        }
        log.info("全部重连接相机成功");

    }

    /**
     * 根据相机和过板台的名称生成映射中使用的唯一键。
     *
     * @param cameraName 相机设备的名称
     *
     * @return 组合后的键，格式为"相机名称_过板台名称"
     */
    private String generateKey(String cameraName,String plateName) {
        return cameraName+"_" + plateName;
    }

    /**
     * 初始化并管理CameraPlateAssociationEntity实例。
     * 从CameraPlateService获取所有关联数据，为每个关联创建一个实体，并尝试初始化。
     * 如果初始化成功，则将实体存储在映射中，并启动相关任务。记录初始化的成功或失败状态。
     */
    public void initializeEntities() {
        List<CameraPlateAssociation> associations = cameraPlateService.getCameraPlateAssociations();
        for (CameraPlateAssociation association : associations) {
            boolean initialized = createAndInitializeEntity(association.getCameraDeviceName(), association.getPlateDeviceName(),association.getTrackNumber());
            if (initialized) {
                String key = generateKey(association.getCameraDeviceName(), association.getPlateDeviceName());
                startPlateTask(association.getCameraDeviceName(), association.getPlateDeviceName());
                log.info("Entity initialized and added to map: {}", key);
            } else {
                log.warn("Failed to initialize real_store_info for Camera: {} and Plate: {}", association.getCameraDeviceName(), association.getPlateDeviceName());
            }
        }
    }



    /**
     * 根据相机和过板台的名称移除并清理特定的CameraPlateAssociationEntity实体。
     * 确保在移除实体前停止实体的业务逻辑。记录移除操作的状态。
     *
     * @param cameraName 相机设备的名称
     * @param plateName  过板台设备的名称
     */
    public void removeEntity(String cameraName, String plateName) {
        String key = generateKey(cameraName,plateName);
        CameraPlateAssociationEntity entity = associationEntityMap.remove(key);
        if (entity != null) {
            entity.stop(); // 确保实体停止运行
            log.info("Entity removed and stopped: {}", key);
        } else {
            log.warn("Attempted to remove non-existent real_store_info: {}", key);
        }
    }



    /**
     * 根据相机名称和过板台名称获取对应的CameraPlateAssociationEntity实体。
     *
     * @param cameraName 相机设备的名称
     * @return 对应的CameraPlateAssociationEntity实体，如果不存在则返回null。
     */
    public CameraPlateAssociationEntity getEntity(String cameraName,String plateName) {
        String key = generateKey(cameraName,plateName);
        CameraPlateAssociationEntity entity = associationEntityMap.get(key);
        if (entity == null) {
            log.warn("No real_store_info found for Camera: {} and Plate: {}", cameraName);
        } else {
            log.info("Entity retrieved for Camera: {} and Plate: {}", cameraName);
        }
        return entity;
    }

    /**
     * 启动指定过板台的任务。
     *
     * @param cameraName 相机设备的名称
     * @param plateName  过板台设备的名称
     * @return true 如果任务成功启动，false 如果实体不存在或任务启动失败。
     */
    public boolean startPlateTask(String cameraName, String plateName) {
        CameraPlateAssociationEntity entity = getEntity(cameraName,plateName);
        if (entity == null) {
            log.error("Cannot start task: No real_store_info found for Camera: {} and Plate: {}", cameraName, plateName);
            return false;
        }
        try {
            entity.start();  // 这是启动任务的方法
            log.info("Task started for Camera: {} and Plate: {}", cameraName, plateName);
            return true;
        } catch (Exception e) {
            log.error("Failed to start task for Camera: {} and Plate: {}. Error: {}", cameraName, plateName, e.getMessage());
            return false;
        }
    }



    /**
     * 监听CameraPlateAssociationCreateEvent事件，并调用createAndInitializeEntity方法。
     *
     * @param event CameraPlateAssociationCreateEvent事件
     */
    @EventListener
    public void handleCameraPlateAssociationCreateEvent(CameraPlateAssociationCreateEvent event) {
        CameraPlateAssociation association = event.getAssociation();
        log.info("Received CameraPlateAssociationCreateEvent: Camera Device Name={}, Plate Device Name={}",
                association.getCameraDeviceName(), association.getPlateDeviceName());

        // 调用createAndInitializeEntity方法
        if(createAndInitializeEntity(association.getCameraDeviceName(), association.getPlateDeviceName(),association.getTrackNumber()))
        {
            startPlateTask(association.getCameraDeviceName(), association.getPlateDeviceName());
        }
    }

    @EventListener
    public void handleCameraPlateAssociationDeleteEvent(CameraPlateAssociationDeleteEvent event) {
        CameraPlateAssociation cameraPlateAssociation = event.getAssociation();
        // 假设过板台名称也通过某种方式获得，这里简化为直接使用相机名称


        log.info("Received CameraPlateAssociationDeleteEvent: Camera Device Name={}", cameraPlateAssociation.getCameraDeviceName());

        // 调用removeEntity方法移除实体
        removeEntity(cameraPlateAssociation.getCameraDeviceName(),cameraPlateAssociation.getPlateDeviceName());

    }


}
