/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase;

import java.time.LocalDateTime;
import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Data
@TableName("iot_admin.plc_device")
public class PlcDevice {
    @TableId(type = IdType.AUTO)
    private Long plcDeviceId;

    @NotBlank(message = "设备名称不能为空或空字符串")
    private String plcDeviceName;

    @NotBlank(message = "IP地址不能为空或空字符串")
    private String ipAddress;

    @NotNull(message = "端口不能为空")
    private Integer port;

    private Boolean connectionStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date lastCommunication;

    @NotBlank(message = "设备类型不能为空或空字符串")
    private String plcDeviceType;

    private String plcTag;
    private String description;
}