/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.zk;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.trafficswitch.service.ClusterNodeService;

import lombok.extern.slf4j.Slf4j;

/**
 * 领导权阻塞管理器
 * 提供阻塞方法等待获取领导权，当失去领导权时可终止进程
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Component
@Slf4j
public class LeadershipBlockingManager implements LeadershipListener {

    @Autowired
    private ZkElectionManager zkElectionManager;

    @Autowired
    private ClusterNodeService clusterNodeService;

    @Value("${leadership.shutdown-on-lost:true}")
    private boolean shutdownOnLost = true;

    @Value("${leadership.exit-code:1}")
    private int exitCode = 1;

    private CountDownLatch leaderLatch = new CountDownLatch(1);
    private volatile boolean hasLeadership = false;

    /**
     * 初始化并注册领导权监听器
     */
    @PostConstruct
    public void init() {
         log.info("初始化领导权阻塞管理器...");
         zkElectionManager.addLeadershipListener(this);

         // 如果当前已经是主节点，则设置hasLeadership并释放锁
         if (zkElectionManager.isLeader()) {
         hasLeadership = true;
         leaderLatch.countDown();
         log.info("当前节点已经是主节点，无需等待");
         }
    }

    /**
     * 清理资源
     */
    @PreDestroy
    public void destroy() {
        zkElectionManager.removeLeadershipListener(this);
    }

    /**
     * 当获取领导权时触发
     */
    @Override
    public void onLeadershipAcquired() {
        log.info("获得领导权，释放阻塞。");
        hasLeadership = true;


        // New logic to trigger traffic switch
        log.info("当前节点已成为领导者，尝试切换流量以将本节点设为主要节点。");

        String localNodeId = zkElectionManager.getLocalNodeId();
        if (localNodeId == null || localNodeId.isEmpty()) {
            log.error("无法获取本地节点ID (IP:Port) 从 ZkElectionManager，无法执行流量切换。请确保 ZkElectionManager 正确初始化并设置了 localNodeId。");
            return;
        }

        log.info("本地节点ID为: {}", localNodeId);
        String[] parts = localNodeId.split(":");
        if (parts.length != 2) {
            log.error("本地节点ID '{}' 格式不正确 (期望 'ip:port')，无法解析IP和端口进行流量切换。", localNodeId);
            return;
        }

        String currentHostIp = parts[0];
        int currentHostPort;
        try {
            currentHostPort = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            log.error("无法将本地节点ID '{}' 中的端口部分 '{}' 解析为整数，无法执行流量切换。", localNodeId, parts[1], e);
            return;
        }

        log.info("准备调用流量切换服务，将 {}:{} 设置为主要节点。", currentHostIp, currentHostPort);
        try {
            boolean switchSuccess = clusterNodeService.setPrimaryNodeAndUpdateWeights(currentHostIp, currentHostPort);
            if (switchSuccess) {
                log.info("流量切换成功：已将节点 {}:{} 设置为主要（权重为1），其他节点权重已更新或按预期设置。", currentHostIp, currentHostPort);
            } else {
                log.warn("流量切换操作未完全成功 (setPrimaryNodeAndUpdateWeights 返回 false)。" +
                        " 这可能意味着API调用失败，或目标节点 {}:{} 未在集群列表中找到（导致其权重未被明确设为1）。" +
                        " 请检查 ClusterNodeService 日志获取详细信息。", currentHostIp, currentHostPort);
            }
        } catch (Exception e) {
            // Catch any unexpected exceptions from the service call itself
            log.error("调用 clusterNodeService.setPrimaryNodeAndUpdateWeights 时发生意外异常。", e);
        }

        leaderLatch.countDown();
    }

    /**
     * 当失去领导权时触发
     */
    @Override
    public void onLeadershipLost() {
        log.warn("失去领导权，准备终止进程");
        hasLeadership = false;

        if (shutdownOnLost) {
            // 创建一个新的线程来执行终止，以避免在监听器回调中直接终止
            Thread shutdownThread = new Thread(() -> {
                try {
                    log.warn("失去领导权，{} 秒后将终止进程 (退出码: {})...", 3, exitCode);
                    // 等待一段时间，以便日志能够完全输出
                    Thread.sleep(3000);
                    log.warn("正在终止进程...");
                    System.exit(exitCode);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
            shutdownThread.setDaemon(true);
            shutdownThread.start();
        }
    }

    /**
     * 阻塞等待直到获得领导权
     *
     * @throws InterruptedException 如果等待过程被中断
     */
    public void waitForLeadership() throws InterruptedException {
        if (!hasLeadership) {
            log.info("等待获得领导权...");
            leaderLatch.await();
        }
        log.info("当前节点拥有领导权，继续执行");
    }

    /**
     * 阻塞等待直到获得领导权，带超时时间
     *
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return 是否成功获得领导权
     * @throws InterruptedException 如果等待过程被中断
     */
    public boolean waitForLeadership(long timeout, TimeUnit unit) throws InterruptedException {
        if (!hasLeadership) {
            log.info("等待获得领导权，最多等待 {} {}...", timeout, unit.name().toLowerCase());
            boolean acquired = leaderLatch.await(timeout, unit);

            if (acquired) {
                log.info("当前节点获得领导权，继续执行");
                return true;
            } else {
                log.warn("等待领导权超时");
                return false;
            }
        }

        log.info("当前节点已拥有领导权，继续执行");
        return true;
    }

    /**
     * 检查当前节点是否拥有领导权
     *
     * @return 是否拥有领导权
     */
    public boolean hasLeadership() {
        return hasLeadership;
    }

    /**
     * 重置内部状态，用于重新等待领导权
     * 注意：仅在特殊场景下使用，正常情况不需要调用
     */
    public void reset() {
        if (!hasLeadership) {
            leaderLatch = new CountDownLatch(1);
        }
    }
}