@startuml
package "相机模块" {
    class "扫描设备通信模块" as ScannerClient {
        + 连接管理
        + 命令发送与响应接收
        + 心跳检测
        + 条码读取
        + LON命令发送
        + 连接状态检查
        + 资源释放
    }

    class "相机设备控制模块" as Camera {
        + 初始化
        + 连接管理
        + 条码读取
        + 心跳检测
        + 状态更新
        + 连接状态检查
        + 日志功能
    }

    class "相机设备管理模块" as CameraManager {
        + 相机管理
        + 设备移除
        + 设备查询
        + 条码读取
        + 事件监听
    }

    class "日志模块" as Logging {
        + 记录操作日志
        + 记录错误日志
    }

    class "数据库" as Database {
        + 存储相机配置
        + 存储设备状态
    }

    class "用户" as User {
        + 管理相机设备
        + 查询设备状态
    }

    ScannerClient --> Camera : 提供通信功能
    CameraManager --> Camera : 管理相机设备
    CameraManager --> Database : 存储相机配置
    CameraManager --> Logging : 记录日志
    User --> CameraManager : 增删查相机设备
    Camera --> Logging : 记录操作日志
}

@enduml