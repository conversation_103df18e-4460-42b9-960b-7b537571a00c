/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate.mewutil;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.StandardSocketOptions;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Slf4j
@Component
@Scope("prototype")
public class MEWClient {
    private SocketChannel socketChannel;
    private ByteBuffer readBuffer = ByteBuffer.allocate(1024);
    private ByteBuffer writeBuffer = ByteBuffer.allocate(1024);

    private volatile boolean connected = false;
    private String host;
    private int port;
    private String thingName;

    public boolean getConnected() {
        // 简单的方式来验证连接，例如发送一个小的数据包
        // 具体实现根据实际情况而定
        return connected && socketChannel!=null&& socketChannel.isOpen()&&socketChannel.isConnected();
    }

    public MEWClient() {

    }


    /**
     * 初始化MEWClient实例。
     *
     * @param host      PLC服务器的IP地址
     * @param port      PLC服务器的端口号
     * @param thingName 用于标识客户端的名称
     */
    public void init(String host, int port, String thingName) {
        int attempt = 0;
        this.thingName = thingName;
        this.host = host;
        this.port = port;

    }

    /**
     * 初始化MEWClient实例。
     *
     * @param host PLC服务器的IP地址
     * @param port PLC服务器的端口号
     */
    public void init(String host, int port) {
        init(host, port, "mewclient");
    }


    /**
     * 尝试连接到服务器。
     *
     * @param timeout 超时时间（毫秒）
     * @return 如果连接成功返回true，否则返回false
     */
    public synchronized boolean connect(int timeout) {
        if (connected && socketChannel != null) {
            // 如果条件满足，认为连接仍然活跃
            return true;
        }

        // 关闭之前的连接
        close();

        try {
            socketChannel = SocketChannel.open();
            socketChannel.configureBlocking(true);
            socketChannel.socket().connect(new InetSocketAddress(host, port), timeout);
            socketChannel.setOption(StandardSocketOptions.SO_KEEPALIVE, true);
            connected = true;
            log.info("thingname:{},ip:{}connect success", thingName, host + port);
            return true;
        } catch (Exception e) {
            //使用英文做日志
            log.error("thingname:{},ip:{}reconnect failed", thingName, host + port);
            socketChannel = null; // 显式地设置为null
            connected = false;
            return false;
        }
    }


    /**
     * 读取PLC的数据
     *
     * @param startAdrss 起始地址
     * @param endAdrss   结束地址
     * @return 结果
     */
    public String readData(String startAdrss, String endAdrss) {
        String res = "";
        if (connect(1000)) {
            List<String> dataList = readDataInner(startAdrss, endAdrss);
            StringBuilder sb = new StringBuilder();
            for (String data : dataList) {
                sb.append(data);
            }
            res = sb.toString();
        }
        return res;
    }

//    public List<String> readData(String startAdrss, String endAdrss) {
//        List<String> res = null;
//        if (connect(1000)) {
//            res = readDataInner(startAdrss, endAdrss);
//        } else {
//            res = new ArrayList<>();
//        }
//        return res;
//    }


    private List<String> readDataInner(String startAdrss, String endAdrss) {
        try {

            List<String> res = new ArrayList<>();
            String command = MEWTOCOL.generateCommandByAddressType(startAdrss, endAdrss);
            log.info("command:{}", command);
            if (command != null && !command.isEmpty()) {

                String response = sendCommandAndReceiveResponse(command, 2000);

                if (response != null) {
                    res = MEWTOCOL.parsePLCResponse(response);
                }
            }
            log.info(" date :{}  thingName :{}  ip:{} readDataInner success", res, thingName, host + port);
            return res;
        } catch (Exception ex) {
            connected = false;
            log.info("thingName :{}  ip:{}readDataInner error:{}", thingName, host + port, ex.getMessage());
            return new ArrayList<>();
        }
    }


//    public boolean sendCommon(String contactAddress, String contactState) {
//
//        if (!connect(1000)) {
//            return false;
//        }
//        try {
//            // 空值检查
//            if (contactAddress == null || contactState == null) {
//               log.info("thingName :{}  ip:{}contactAddress or contactState is null", thingName, host + port);
//                return false;
//            }
//            String common = MEWTOCOL.writeSingleContact(contactAddress, contactState);
//            // 检查生成的命令是否为null
//            if (common == null) {
//
//                log.error("common is null");
//                return false;
//            }
//            String response = sendCommandAndReceiveResponse(common, 1000);
//            log.info(" date :{}  thingName :{}  ip:{} sendCommon success", response,thingName, host + port);
//            // 检查响应是否为null
//            if (response == null) {
//                log.error("response is null");
//                return false;
//            }
//            return MEWTOCOL.parseWriteSingleContactResponse(response);
//        } catch (Exception ex) {
//            connected = false;
//            log.info("thingName :{}  ip:{}readDataInner error:{}", thingName, host + port, ex.getMessage());
//            return false;
//        }
//
//
//    }

    public boolean sendCommon(String address, String... data) {
        if (!connect(1000)) {
            return false;
        }
        try {
            // 空值检查
            if (address == null || data == null || data.length == 0) {
                log.info("thingName: {}  ip: {} address or data is null/empty", thingName, host + ":" + port);
                return false;
            }

            String common;
            boolean isDTAddress = address.startsWith("DT");

            if (isDTAddress) {
                // 使用 DT 类型的编码方法
                List<String> dataList = Arrays.asList(data);
                String endAddress = calculateEndAddress(address, data.length);
                common = MEWTOCOL.encodeWriteRegisterCommand(address, endAddress, dataList);
            } else {
                // 使用原有的 R 类型编码方法，只使用第一个数据
                common = MEWTOCOL.writeSingleContact(address, data[0]);
            }

            // 检查生成的命令是否为null
            if (common == null) {
                log.error("Generated command is null");
                return false;
            }
            log.info("common: {}", common);

            String response = sendCommandAndReceiveResponse(common, 2000);
            log.info("Date: {}  thingName: {}  ip: {} sendCommon success", response, thingName, host + ":" + port);

            // 检查响应是否为null
            if (response == null) {
                log.error("Response is null");
                return false;
            }

            if (isDTAddress) {
                // 使用 DT 类型的解码方法
                return MEWTOCOL.decodeWriteRegisterResponse(response);
            } else {
                // 使用原有的 R 类型解码方法
                return MEWTOCOL.parseWriteSingleContactResponse(response);
            }
        } catch (Exception ex) {
            connected = false;
            log.info("thingName: {}  ip: {} readDataInner error: {}", thingName, host + ":" + port, ex.getMessage());
            return false;
        }
    }

    public static void main(String[] args) {
        MEWClient mewClient = new MEWClient();
        mewClient.init("***********", 9094);
        boolean res = mewClient.sendCommon("DT00001", "500", "715", "9");
        log.info("res:{}", res);
    }


    private String calculateEndAddress(String startAddress, int dataCount) {
        // 假设地址格式为 "DTxxxxx"
        int startNum = Integer.parseInt(startAddress.substring(2));
        int endNum = startNum + dataCount - 1;
        return String.format("DT%05d", endNum);
    }

//    /**
//     * 发送指令到PLC并接收响应，同时包含超时处理。
//     *
//     * @param command 要发送的指令
//     * @param timeout 超时时间（毫秒）
//     * @return PLC的响应字符串
//     * @throws IOException 如果在通信过程中发生I/O错误或超时
//     */
//    private synchronized String sendCommandAndReceiveResponse(String command, int timeout) throws IOException {
//        long startTime = System.currentTimeMillis(); // 记录开始时间
//        // 发送命令
//        writeBuffer.clear();
//        writeBuffer.put(command.getBytes(StandardCharsets.US_ASCII));
//        writeBuffer.flip();
//        while (writeBuffer.hasRemaining()) {
//            if (System.currentTimeMillis() - startTime > timeout) {
//                throw new IOException("发送命令超时");
//            }
//            socketChannel.write(writeBuffer);
//        }
//        // 接收响应
//        readBuffer.clear();
//        StringBuilder response = new StringBuilder();
//
//        while (true) {
//            if (socketChannel.read(readBuffer) > 0) {
//                readBuffer.flip();
//                while (readBuffer.hasRemaining()) {
//                    char character = (char) readBuffer.get();
//                    response.append(character);
//                    if (character == '\r') {
//                        return response.toString();
//                    }
//                }
//                readBuffer.clear();
//            } else if (System.currentTimeMillis() - startTime > timeout) {
//                throw new IOException("接收响应超时");
//            }
//        }
//    }

    /**
     * 发送命令并接收响应，支持重试机制。
     *
     * @param command 发送的命令
     * @param timeout 超时时间（毫秒）
     * @return 设备的响应
     * @throws IOException 如果发送命令或接收响应失败
     */
    private synchronized String sendCommandAndReceiveResponse(String command, int timeout) throws IOException {
        final int maxRetries = 2; // 最大重试次数
        int attempt = 0; // 当前尝试次数

        while (attempt < maxRetries) {
            long startTime = System.currentTimeMillis(); // 记录开始时间
            log.info("尝试发送命令: " + command + "，尝试次数: " + (attempt + 1));

            // 发送命令
            writeBuffer.clear();
            writeBuffer.put(command.getBytes(StandardCharsets.US_ASCII));
            writeBuffer.flip();

            // 发送命令
            try {
                while (writeBuffer.hasRemaining()) {
                    if (System.currentTimeMillis() - startTime > timeout) {
                        log.error("发送命令超时");
                        throw new IOException("发送命令超时");
                    }
                    socketChannel.write(writeBuffer);
                }
                log.info("命令发送成功: " + command);
            } catch (IOException e) {
                attempt++;
                log.error("发送命令失败: " + e.getMessage(), e);
                if (attempt >= maxRetries) {
                    throw new IOException("发送命令失败，已达到最大重试次数", e);
                }
                continue; // 继续重试
            }

            // 接收响应
            readBuffer.clear();
            StringBuilder response = new StringBuilder();

            while (true) {
                if (socketChannel.read(readBuffer) > 0) {
                    readBuffer.flip();
                    while (readBuffer.hasRemaining()) {
                        char character = (char) readBuffer.get();
                        response.append(character);
                        if (character == '\r') {
                            log.info("接收到响应: " + response.toString());
                            return response.toString();
                        }
                    }
                    readBuffer.clear();
                } else if (System.currentTimeMillis() - startTime > timeout) {
                    log.error("接收响应超时");
                    throw new IOException("接收响应超时");
                }
            }
        }

        // 如果到达这里，说明已经重试了最大次数但仍然失败
        throw new IOException("发送命令失败，已达到最大重试次数");
    }




    /**
     * 关闭与服务器的连接。
     *
     * @throws IOException 如果在关闭连接时发生I/O错误
     */

    public void close() {
        if (socketChannel != null) {
            try {
                socketChannel.close();
            } catch (IOException e) {
                log.error("thingname:{}, ip:{} closeConnection failed", thingName, host + ":" + port, e);
            } finally {
                socketChannel = null;
                connected = false;
            }
        }
    }

//    public void close() {
//        try {
//            if (socketChannel != null) {
//                socketChannel.close();
//            }
//            socketChannel = null;
//        } catch (IOException e) {
//            log.error("thingName :{}  ip:{}close error:{}", thingName, host + port, e.getMessage());
//        } finally {
//            connected = false;
//        }
//
//    }

    // 其他必要的方法可以在这里继续添加
}