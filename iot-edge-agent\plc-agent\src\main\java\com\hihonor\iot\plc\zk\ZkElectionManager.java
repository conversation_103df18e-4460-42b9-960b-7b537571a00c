/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.zk;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.recipes.cache.NodeCache;
import org.apache.curator.framework.recipes.leader.LeaderLatch;
import org.apache.curator.framework.recipes.leader.LeaderLatchListener;
import org.apache.curator.framework.recipes.leader.Participant;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.zookeeper.data.Stat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * ZooKeeper选举管理器类
 * 基于Curator的LeaderLatch实现主备选举功能
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Component
@Slf4j
public class ZkElectionManager {

    @Value("${zookeeper.connection-string}")
    private String zkConnectString;

    @Value("${zookeeper.session-timeout:60000}")
    private int sessionTimeout;

    @Value("${zookeeper.connection-timeout:15000}")
    private int connectionTimeout;

    @Value("${zookeeper.retry-base-sleep:1000}")
    private int retryBaseSleep;

    @Value("${zookeeper.retry-max-retries:3}")
    private int retryMaxRetries;

    @Value("${zookeeper.namespace:plc-agent}")
    private String namespace;

    @Value("${server.port}")
    private String serverPort;

    private CuratorFramework client;
    private LeaderLatch leaderLatch;
    private NodeCache leaderNodeCache;

    private String localNodeId;
    private boolean isLeader = false;

    private final List<LeadershipListener> listeners = new CopyOnWriteArrayList<>();

    /**
     * 初始化ZK连接及选举机制
     */
    @PostConstruct
    public void init() {
        try {
            log.info("初始化ZK选举管理器...");

            // 初始化ZK客户端
            client = CuratorFrameworkFactory.builder()
                    .connectString(zkConnectString)
                    .sessionTimeoutMs(sessionTimeout)
                    .connectionTimeoutMs(connectionTimeout)
                    .retryPolicy(new ExponentialBackoffRetry(retryBaseSleep, retryMaxRetries))
                    .namespace(namespace)
                    .build();

            client.start();
            log.info("ZK客户端已启动");

            // 等待连接建立
            if (!client.blockUntilConnected(30, TimeUnit.SECONDS)) {
                throw new IllegalStateException("连接ZooKeeper超时");
            }

            // 获取本机节点ID
            localNodeId = getHostAddress() + ":" + serverPort;
            log.info("本地节点ID: {}", localNodeId);

            // 初始化领导选举
            initLeaderLatch();

            // 初始化领导节点缓存
            initLeaderNodeCache();

            log.info("ZK选举管理器初始化完成");
        } catch (Exception e) {
            log.error("初始化ZK选举管理器失败", e);
            throw new RuntimeException("初始化ZK选举管理器失败", e);
        }
    }

    /**
     * 初始化领导选举
     */
    private void initLeaderLatch() throws Exception {
        String leaderPath = "/leader";

        // 确保路径存在
        Stat stat = client.checkExists().forPath(leaderPath);
        if (stat == null) {
            client.create().creatingParentsIfNeeded().forPath(leaderPath);
            log.info("创建领导选举路径: {}", leaderPath);
        }

        // 创建LeaderLatch
        leaderLatch = new LeaderLatch(client, leaderPath, localNodeId);

        // 添加监听器
        leaderLatch.addListener(new LeaderLatchListener() {
            @Override
            public void isLeader() {
                try {
                    log.info("本节点成为主节点!");
                    isLeader = true;

                    // 更新当前领导者信息
                    updateLeaderInfo();

                    // 通知所有监听器
                    notifyLeadershipAcquired();
                } catch (Exception e) {
                    log.error("更新领导信息失败", e);
                }
            }

            @Override
            public void notLeader() {
                log.info("本节点不再是主节点");
                isLeader = false;

                // 通知所有监听器
                notifyLeadershipLost();
            }
        });

        // 启动选举
        leaderLatch.start();
        log.info("领导选举已启动");
    }

    /**
     * 初始化领导节点缓存
     */
    private void initLeaderNodeCache() throws Exception {
        String leaderInfoPath = "/current-leader";

        // 确保路径存在
        Stat stat = client.checkExists().forPath(leaderInfoPath);
        if (stat == null) {
            client.create().creatingParentsIfNeeded().forPath(leaderInfoPath);
            log.info("创建当前领导信息路径: {}", leaderInfoPath);
        }

        // 创建NodeCache
        leaderNodeCache = new NodeCache(client, leaderInfoPath);
        leaderNodeCache.start(true);

        // 添加监听器
        leaderNodeCache.getListenable().addListener(() -> {
            if (leaderNodeCache.getCurrentData() != null) {
                byte[] data = leaderNodeCache.getCurrentData().getData();
                String leaderId = new String(data, StandardCharsets.UTF_8);
                log.info("当前领导节点: {}", leaderId);
            }
        });
    }

    /**
     * 更新领导信息
     */
    private void updateLeaderInfo() throws Exception {
        String leaderInfoPath = "/current-leader";
        client.setData().forPath(leaderInfoPath, localNodeId.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 通知所有监听器已获取领导权
     */
    private void notifyLeadershipAcquired() {
        for (LeadershipListener listener : listeners) {
            try {
                listener.onLeadershipAcquired();
            } catch (Exception e) {
                log.error("通知领导权获取事件失败", e);
            }
        }
    }

    /**
     * 通知所有监听器已失去领导权
     */
    private void notifyLeadershipLost() {
        for (LeadershipListener listener : listeners) {
            try {
                listener.onLeadershipLost();
            } catch (Exception e) {
                log.error("通知领导权丢失事件失败", e);
            }
        }
    }

    /**
     * 获取本机IP地址
     */
    private String getHostAddress() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                // 过滤掉loopback和非激活的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    // 确保地址不是loopback地址并且是IPv4
                    if (!addr.isLoopbackAddress() && addr instanceof Inet4Address) {
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取主机地址失败", e);
        }
        return "127.0.0.1"; // 默认返回本地回环地址
    }

    /**
     * 释放资源
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭ZK选举管理器...");

        // 关闭领导节点缓存
        if (leaderNodeCache != null) {
            try {
                leaderNodeCache.close();
            } catch (Exception e) {
                log.warn("关闭领导节点缓存失败", e);
            }
        }

        // 关闭领导选举
        if (leaderLatch != null) {
            try {
                leaderLatch.close();
            } catch (Exception e) {
                log.warn("关闭领导选举失败", e);
            }
        }

        // 关闭ZK客户端
        if (client != null) {
            client.close();
        }

        log.info("ZK选举管理器已关闭");
    }

    /**
     * 检查当前节点是否是领导节点
     *
     * @return 是否是领导节点
     */
    public boolean isLeader() {
        return isLeader;
    }

    /**
     * 获取当前领导节点ID
     *
     * @return 领导节点ID
     */
    public String getLeaderId() {
        try {
            if (leaderNodeCache.getCurrentData() != null) {
                byte[] data = leaderNodeCache.getCurrentData().getData();
                return new String(data, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            log.error("获取领导节点ID失败", e);
        }
        return null;
    }

    /**
     * 获取本地节点ID
     *
     * @return 本地节点ID
     */
    public String getLocalNodeId() {
        return localNodeId;
    }

    /**
     * 添加领导权变更监听器
     *
     * @param listener 监听器
     */
    public void addLeadershipListener(LeadershipListener listener) {
        if (listener != null) {
            listeners.add(listener);

            // 如果当前是领导节点，立即通知
            if (isLeader) {
                listener.onLeadershipAcquired();
            }
        }
    }

    /**
     * 移除领导权变更监听器
     *
     * @param listener 监听器
     */
    public void removeLeadershipListener(LeadershipListener listener) {
        if (listener != null) {
            listeners.remove(listener);
        }
    }

    /**
     * 获取所有参与选举的节点列表
     *
     * @return 节点列表
     */
    public List<String> getParticipants() {
        List<String> participants = new ArrayList<>();
        try {
            for (Participant participant : leaderLatch.getParticipants()) {
                participants.add(participant.getId());
            }
        } catch (Exception e) {
            log.error("获取参与节点列表失败", e);
        }
        return participants;
    }
}