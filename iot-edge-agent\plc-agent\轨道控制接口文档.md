# 过板台轨道控制接口规范文档

## 文档概述

- **文档版本**：1.0.0
- **更新日期**：2025-05-15
- **适用产品**：IoT 设备网关
- **文档状态**：正式发布

本文档描述了过板台轨道控制接口规范，提供了对过板台设备轨道进行控制的标准 HTTP API。

## API 基础信息

- **测试 域名**：`http://iot-equip-sit-agw.test.hihonor.com`
- 生产域名：http://iot-gateway.yun.hihonor.com
- **内容格式**：JSON
- **请求头**：所有 API 请求需包含`Content-Type: application/json`
- **认证方式**：暂无，后续可能增加认证机制

## API 列表

### 1. 控制第一轨道禁止通行并复位

控制过板台设备第一轨道禁止通行，并在一秒后自动复位为允许通行状态。

#### 请求信息

- **URL**：`/plc/plate/controlTrack1Prohibition`
- **方法**：POST
- **描述**：向指定过板台设备发送第一轨道禁止通行信号，一秒后自动复位

#### 请求参数

| 名称       | 类型   | 必填 | 说明             | 示例             |
| ---------- | ------ | ---- | ---------------- | ---------------- |
| resourceId | String | 是   | 过板台设备标识符 | "SMT09_HuiLiuLu" |

#### 请求示例

```http
POST /plc/plate/controlTrack1Prohibition HTTP/1.1
Host: iot-gateway.yun.hihonor.com
Content-Type: application/json

{
  "resourceId": "SMT09_HuiLiuLu"
}
```

#### 响应参数

| 名称    | 类型    | 说明         |
| ------- | ------- | ------------ |
| success | Boolean | 操作是否成功 |
| message | String  | 结果描述信息 |

#### 响应示例 - 成功

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "操作成功"
}
```

#### 响应示例 - 失败

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": false,
  "message": "操作失败"
}
```

#### 错误码

| HTTP 状态码 | 错误码           | 说明                                                  |
| ----------- | ---------------- | ----------------------------------------------------- |
| 200         | -                | 请求成功处理，但业务逻辑可能失败，请查看 success 字段 |
| 400         | INVALID_PARAMS   | 请求参数错误或不完整                                  |
| 404         | DEVICE_NOT_FOUND | 指定的设备不存在                                      |
| 500         | INTERNAL_ERROR   | 服务器内部错误                                        |

#### 业务场景

此接口用于控制过板台第一轨道的通行状态，主要应用于：

- 在检测到异常情况时，临时阻止新的 PCB 板进入第一轨道
- 测试过板台轨道控制功能
- 生产线维护期间控制 PCB 板流动
- 实现定制化的生产流程控制逻辑

---

### 2. 控制第二轨道禁止通行并复位

控制过板台设备第二轨道禁止通行，并在一秒后自动复位为允许通行状态。

#### 请求信息

- **URL**：`/plc/plate/controlTrack2Prohibition`
- **方法**：POST
- **描述**：向指定过板台设备发送第二轨道禁止通行信号，一秒后自动复位

#### 请求参数

| 名称       | 类型   | 必填 | 说明             | 示例             |
| ---------- | ------ | ---- | ---------------- | ---------------- |
| resourceId | String | 是   | 过板台设备标识符 | "SMT09_HuiLiuLu" |

#### 请求示例

```http
POST /plc/plate/controlTrack2Prohibition HTTP/1.1
Host: iot-gateway.yun.hihonor.com
Content-Type: application/json

{
  "resourceId": "SMT09_HuiLiuLu"
}
```

#### 响应参数

| 名称    | 类型    | 说明         |
| ------- | ------- | ------------ |
| success | Boolean | 操作是否成功 |
| message | String  | 结果描述信息 |

#### 响应示例 - 成功

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "操作成功"
}
```

#### 响应示例 - 失败

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": false,
  "message": "操作失败"
}
```

#### 错误码

| HTTP 状态码 | 错误码           | 说明                                                  |
| ----------- | ---------------- | ----------------------------------------------------- |
| 200         | -                | 请求成功处理，但业务逻辑可能失败，请查看 success 字段 |
| 400         | INVALID_PARAMS   | 请求参数错误或不完整                                  |
| 404         | DEVICE_NOT_FOUND | 指定的设备不存在                                      |
| 500         | INTERNAL_ERROR   | 服务器内部错误                                        |

#### 业务场景

此接口用于控制过板台第二轨道的通行状态，主要应用于：

- 在检测到异常情况时，临时阻止新的 PCB 板进入第二轨道
- 测试过板台轨道控制功能
- 生产线维护期间控制 PCB 板流动
- 实现定制化的生产流程控制逻辑

## 注意事项

1. 轨道禁止通行信号会在 1 秒后自动复位，无需手动发送复位命令
2. 如果设备不在线或通信失败，接口将返回错误信息
3. 建议在生产环境中谨慎使用这些接口，以免影响正常生产流程
4. 在发送轨道控制命令前，建议先检查设备状态

## 数据类型说明

- **resourceId**：字符串类型，唯一标识过板台设备的 ID
- **success**：布尔类型，表示操作是否成功
- **message**：字符串类型，描述操作结果的详细信息

## 附录

### 常见错误处理

1. 设备不存在：检查 resourceId 是否正确
2. 设备离线：确认设备是否在线并可通信
3. 操作失败：可能是由于设备内部状态或通信问题导致

### 版本历史

- 1.0.0 (2025-05-12): 初始版本
