/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.controller;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Data
public class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;

    private int length = 0;

    public ApiResponse() {
    }

    public ApiResponse(boolean success, String message, T data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    // Getters and setters...
}

