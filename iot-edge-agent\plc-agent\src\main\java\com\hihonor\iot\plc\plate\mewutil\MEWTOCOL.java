/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate.mewutil;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.regex.Pattern;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-10
 */

public class MEWTOCOL {

    private static final Pattern ADDRESS_PATTERN = Pattern.compile("^DT\\d{5}$");

    /**
     * 计算MEWTOCOL通信协议的校验码BCC。
     * 它接受命令字符串，计算字符的ASCII值的异或结果，
     * 并返回作为两个十六进制数字的BCC。
     *
     * @param command 需要计算BCC的命令字符串（不包括<CR>和BCC本身）
     * @return BCC值，作为两个字符的字符串
     */
    public static String calculateBCC(String command) {
        // 初始化BCC值
        int bccValue = 0;

        // 遍历每个字符，将其转换为ASCII值并执行XOR运算
        for (int i = 0; i < command.length(); i++) {
            bccValue ^= command.charAt(i);
        }

        // 将结果转换为两个字符的十六进制字符串
        String bccHex = String.format(Locale.ROOT, "%02X", bccValue);

        // 返回BCC值
        return bccHex;
    }

    /**
     * 构造读取单触点状态的指令。目标站号固定为'01'，触点类型为'R'。
     *
     * @param contactAddress 触点地址，格式为'RXXXX'，'XXXX'是触点编号。
     * @return 构造好的指令字符串。
     */
    public static String readSingleContact(String contactAddress) {
        // 提取触点类型和编号
        char contactType = contactAddress.charAt(0); // 触点类型，例如 'R'
        String contactNumber = String.format(Locale.ROOT, "%04d", Integer.parseInt(contactAddress.substring(1))); // 触点编号，不足四位前面补零

        // 构造命令字符串，不包含校验码BCC和结束符CR
        String command = "%" + "01" + "#RCS" + contactType + contactNumber;

        // 计算BCC
        String bcc = calculateBCC(command);

        // 添加BCC和结束符CR
        String fullCommand = command + bcc + '\r';

        return fullCommand;
    }

    /**
     * 解析PLC的读单触点返回应答字符串，不进行BCC校验。
     * 如果是正常应答，返回触点的状态数据（0或1）；
     * 如果是错误应答或格式不正确，则抛出异常。
     *
     * @param response PLC返回的应答字符串。
     * @return 触点状态数据（"0"或"1"）。
     * @throws IllegalArgumentException 如果应答字符串不正确或是错误应答。
     */
    public static String parseReadSingleContactResponse(String response) throws IllegalArgumentException {
        // 去除可能的回车符
        response = response.trim();

        // 检查应答的开头标志和格式
        if (!response.startsWith("%") || response.length() < 8) {
            throw new IllegalArgumentException("Response format is incorrect or too short.");
        }

        // 检查是否为正常应答
        if (response.charAt(3) == '$') {
            // 提取触点数据，假设触点数据位于第7个字符位置
            String contactData = String.valueOf(response.charAt(6)); // ON对应"1"，OFF对应"0"
            if (contactData.equals("0") || contactData.equals("1")) {
                return contactData;
            } else {
                throw new IllegalArgumentException("Contact data is not valid.");
            }
        } else if (response.charAt(3) == '!') {
            throw new IllegalArgumentException("Error response received.");
        } else {
            throw new IllegalArgumentException("Response format is unrecognized.");
        }
    }

    /**
     * 构造写入单触点状态的指令。目标站号固定为'01'，触点类型为'R'。
     * 如果触点编号不足四位，将自动补齐至四位。
     *
     * @param contactAddress 触点地址，格式为'RXXXX'，'XXXX'是触点编号。
     * @param contactState   要写入的触点状态，'0'对应OFF，'1'对应ON。
     * @return 构造好的指令字符串。
     * @throws IllegalArgumentException 如果触点地址或状态不正确。
     */
    public static String writeSingleContact(String contactAddress, String contactState) {
        // 验证触点状态是否正确
        if (!contactState.equals("0") && !contactState.equals("1")) {
            return null;
        }

        // 提取触点类型和编号
        char contactType = contactAddress.charAt(0); // 触点类型，例如 'R'
        String contactNumber = String.format(Locale.ROOT, "%04d", Integer.parseInt(contactAddress.substring(1))); // 触点编号，不足四位前面补零

        // 构造命令字符串，不包含校验码BCC和结束符CR
        String command = "%" + "01" + "#WCS" + contactType + contactNumber + contactState;

        // 计算BCC
        String bcc = calculateBCC(command);

        // 添加BCC和结束符CR
        String fullCommand = command + bcc + '\r';

        return fullCommand;
    }

    /**
     * 编码写入寄存器值的指令
     *
     * @param startAddress 起始地址（格式：DT后跟5个数字）
     * @param endAddress   结束地址（格式：DT后跟5个数字）
     * @param data         要写入的数据列表（每个元素都是字符串形式的数字）
     * @return 编码后的指令字符串
     * @throws IllegalArgumentException 如果地址格式不正确
     */
    public static String encodeWriteRegisterCommand(String startAddress, String endAddress, List<String> data)
            throws IllegalArgumentException {
        // 验证地址格式
        if (!validateAddress(startAddress)) {
            return null;
        }
        if (!validateAddress(endAddress)) {
            return null;
        }

        StringBuilder command = new StringBuilder("%");

        // 添加固定的目标站号
        command.append("01");

        // 添加分隔符和指令代码
        command.append("#WDD");

        // 添加起始和结束地址（去掉DT前缀）
        command.append(startAddress.substring(2));
        command.append(endAddress.substring(2));

        // 添加数据
        for (String value : data) {
            command.append(String.format("%04d", Integer.parseInt(value)));
        }

        // 计算并添加BCC
        String bcc = calculateBCC(command.toString());
        command.append(bcc);

        // 添加结束符 - 固定使用\r (十六进制0D)
        command.append('\r');

        return command.toString();
    }

    private static boolean validateAddress(String address) {
        return ADDRESS_PATTERN.matcher(address).matches();

    }

    /**
     * 解码写入寄存器操作的应答
     *
     * @param response 应答字符串
     * @return 如果是正常应答返回true，如果是错误应答或格式不正确返回false
     */
    public static boolean decodeWriteRegisterResponse(String response) {
        // 检查应答长度
        if (response == null || response.length() != 9) {
            return false;
        }

        // 检查开头标志
        if (response.charAt(0) != '%') {
            return false;
        }

        // 检查源站号
        if (!response.startsWith("01", 1)) {
            return false;
        }

        // 检查应答标志
        char responseFlag = response.charAt(3);
        if (responseFlag != '$' && responseFlag != '!') {
            return false;
        }

        // 检查指令名称
        if (!response.startsWith("WD", 4)) {
            return false;
        }

        // 检查结束符
        if (response.charAt(8) != '\r') {
            return false;
        }

        // 验证BCC
        String contentToCheck = response.substring(0, 6);
        String expectedBCC = calculateBCC(contentToCheck);
        String actualBCC = response.substring(6, 8);
        if (!expectedBCC.equals(actualBCC)) {
            return false;
        }

        // 如果所有检查都通过，返回是否为正常应答
        return responseFlag == '$';
    }

    /**
     * 解析PLC的写单触点返回应答字符串，不进行BCC校验。
     * 如果是正常应答，返回True；如果是错误应答或格式不正确，返回False。
     *
     * @param response PLC返回的应答字符串。
     * @return True表示写入成功，False表示写入失败或错误。
     */
    public static boolean parseWriteSingleContactResponse(String response) {
        // 去除可能的回车符
        response = response.strip();

        // 检查应答的开头标志和格式
        if (!response.startsWith("%") || response.length() < 6) {
            return false;
        }

        // 检查是否为正常应答
        if (response.charAt(3) == '$' && response.substring(4, 6).equals("WC")) {
            // 正常应答返回True
            return true;
        } else if (response.charAt(3) == '!') {
            // 错误应答返回False
            return false;
        } else {
            return false;
        }
    }

    /**
     * 构造读取数据寄存器值的指令。目标站号固定为'01'，数据代码为'D'。
     * 如果寄存器地址不足五位，将自动补齐至五位。
     * 如果只传入起始地址，则认为起始和结束地址相同。
     *
     * @param startAddress 寄存器起始地址，格式为'DTXXXX'，'XXXX'是寄存器编号。
     * @param endAddress   寄存器结束地址，如果未提供，则与起始地址相同。
     * @return 构造好的指令字符串。
     */
    public static String readDataRegister(String startAddress, String endAddress) {
        // 数据代码固定为'D'
        String registerType = "D";

        // 起始编号，不足五位前面补零
        String startNumber = String.format(Locale.ROOT, "%05d", Integer.parseInt(startAddress.substring(2)));

        // 结束编号，不足五位前面补零
        String endNumber = endAddress != null
                ? String.format(Locale.ROOT, "%05d", Integer.parseInt(endAddress.substring(2)))
                : startNumber;

        // 构造命令字符串，不包含校验码BCC和结束符CR
        String command = "%" + "01" + "#RD" + registerType + startNumber + endNumber;

        // 计算BCC
        String bcc = calculateBCC(command);

        // 添加BCC和结束符CR
        String fullCommand = command + bcc + '\r';

        return fullCommand;
    }

    /**
     * 解析PLC数据寄存器读取命令的响应字符串，并考虑数据的高低位顺序。
     * 将十六进制格式的数据转换为十进制。
     * 如果是正常的响应，返回一个包含寄存器数据（十进制格式）的列表。
     * 如果是错误的响应或格式不正确，返回一个空列表。
     *
     * @param response 来自PLC的响应字符串。
     * @return 包含寄存器数据（十进制格式）的列表，或者一个表示错误的空列表。
     */
    public static List<Integer> parseReadDataRegisterResponse(String response) {
        // 去除响应字符串中的回车字符
        response = response.strip();

        // 检查响应的开始标志和有效响应的最小长度
        if (!response.startsWith("%") || response.length() < 10) {
            return new ArrayList<>();
        }

        // 检查是否为正常响应
        if (response.charAt(3) == '$' && response.substring(4, 6).equals("RD")) {
            String dataStr = response.substring(6, response.length() - 2);

            if (dataStr.length() % 4 != 0) {
                return new ArrayList<>();
            }

            List<Integer> data = new ArrayList<>();
            for (int i = 0; i < dataStr.length(); i += 4) {
                // 按每两个字符反转顺序
                String dataSegment = dataStr.substring(i, i + 4);
                String correctedData = dataSegment.substring(2, 4) + dataSegment.substring(0, 2);
                // 将十六进制格式的数据转换为十进制
                Integer decimalData = Integer.parseInt(correctedData, 16);
                data.add(decimalData);
            }
            return data;
        } else if (response.charAt(3) == '!') {
            return new ArrayList<>();
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据地址类型生成相应的报文指令。
     *
     * @param address    需要读取的地址，格式可以是'RXXXX'或'DTXXXX'。
     * @param endAddress 结束地址，仅在读取数据寄存器时使用。
     * @return 构造好的指令字符串。
     */
    public static String generateCommandByAddressType(String address, String endAddress) {
        if (address.startsWith("R")) {
            // 读取单触点状态
            return readSingleContact(address);
        } else if (address.startsWith("DT")) {
            // 读取数据寄存器值
            return readDataRegister(address, endAddress);
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
        // 调用 generateCommandByAddressType 方法
        String address = "R2000";
        String endAddress = "R2000"; // 假设的结束地址
        String result = generateCommandByAddressType("R2000", "R2000");
        // List<String> result=parsePLCResponse("%01$RD000016");

        // String result = writeSingleContact("R2100", "0");

        // 打印结果

        // try {
        // List<String> data = Arrays.asList("500", "715", "9","8");
        // String encodedCommand = encodeWriteRegisterCommand("DT00001", "DT00004",
        // data);
        // System.out.println(encodedCommand);
        // } catch (IllegalArgumentException e) {
        // System.err.println("Error: " + e.getMessage());
        // }
        System.out.println(result);
        // for(String s :res)
        // {
        // System.out.println(s);
        // }

    }

    /**
     * 解析PLC的应答字符串，自动判断响应类型并调用相应的解析函数。
     * 返回一个包含解析后数据的字符串列表。
     *
     * @param response PLC返回的应答字符串。
     * @return 根据响应类型返回解析后的数据列表。
     */
    public static List<String> parsePLCResponse(String response) {
        List<String> result = new ArrayList<>();

        try {
            // 检查是否为数据寄存器读取的响应
            if (response.contains("RD")) {
                List<Integer> data = parseReadDataRegisterResponse(response);
                for (Integer datum : data) {
                    result.add(datum.toString());
                }
                // 检查是否为读取单触点的响应
            } else if (response.contains("RC")) {
                String contactData = parseReadSingleContactResponse(response);
                result.add(contactData);
            }
        } catch (IllegalArgumentException e) {
            // 如果有任何异常，捕获并返回空列表
            return new ArrayList<>();
        }

        return result;
    }

}
