/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "plc")
@Slf4j
public class PlcConfiguration {

    private List<Point> points;
    private Map<String, Point> pointMap;

    @PostConstruct
    void init() {
        log.info("PlcConfiguration init: {}", points);
        // 使用传统的API初始化pointMap
        pointMap = new HashMap<>();
        for (Point point : points) {
            pointMap.put(point.getName(), point);
        }
    }

    /**
     * 根据点位名称获取点位
     *
     * @param name 点位名称
     * @return 如果找到了对应的点位，则返回该点位；否则返回null
     */
    public Point getPointByName(String name) {
        return pointMap.get(name);
    }
}