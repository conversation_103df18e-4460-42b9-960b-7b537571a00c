/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase;

import org.springframework.context.ApplicationEvent;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-29
 */

public class DeviceUpdateEvent extends ApplicationEvent {
    private final PlcDevice device;

    public DeviceUpdateEvent(Object source, PlcDevice device) {
        super(source);
        this.device = device;
    }

    public PlcDevice getDevice() {
        return device;
    }
}