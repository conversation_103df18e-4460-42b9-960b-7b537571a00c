/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Data
public class BoardingStatusRequest {
    @NotBlank(message = "deviceNo is mandatory")
    private String deviceNo;

    @NotNull(message = "operate is mandatory")
    @Pattern(regexp = "^[123]$", message = "operate must be 1 or 2 OR 3")
    private String operate;
    @NotNull(message = "subNo is mandatory")
    @Pattern(regexp = "^[012]$", message = "operate must be 0 or 1 OR 2")
    private String  subNo;

    private String msg;

    private  String description;
}
