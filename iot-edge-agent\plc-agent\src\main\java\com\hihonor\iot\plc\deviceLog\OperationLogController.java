/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.deviceLog;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.iot.plc.ApiPageResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Slf4j
@RestController
@RequestMapping("/plc/operation-logs")
public class OperationLogController {

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 查询操作日志
     *
     * @param request 查询请求对象
     * @return 操作日志列表
     */
    @PostMapping("/query")
    public ApiPageResponse<List<PlcOperationLog>> queryOperationLogs(@RequestBody @Valid OperationLogQueryRequest request) {
        Page<PlcOperationLog> page = new Page<>(request.getPage(), request.getSize()); // 创建分页对象
        IPage<PlcOperationLog> operationLogsPage = operationLogService.getOperationLogs(
                page,
                request.getDeviceName(), // 添加设备名称作为查询参数
                request.getStartTime(),
                request.getEndTime(),
                request.getMethodName());

        return new ApiPageResponse<>(
                true,
                "查询操作日志成功",
                operationLogsPage.getRecords(),
                operationLogsPage.getRecords().size(),
                operationLogsPage.getSize(),
                operationLogsPage.getTotal(),
                operationLogsPage.getCurrent()
        );
    }

}