/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import java.util.LinkedList;
import java.util.Queue;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.camera.Camera;
import com.hihonor.iot.plc.camera.CameraManager;
import com.hihonor.iot.plc.plate.Plate;
import com.hihonor.iot.plc.plate.PlateManager;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Component
@Scope("prototype")
@Slf4j
public class CameraPlateAssociationEntity {
    @Autowired
    private CameraManager cameraManager;
    @Autowired
    private PlateManager plateManager;

    @Autowired
    BarcodeService barcodeService;
    @Getter
    private Camera camera;
    private Plate plate;
    private volatile boolean running = false;
    private Thread workerThread;

    private int trackNumber;

    /**
     * Initializes the real_store_info with specified camera and plate device names.
     *
     * @param cameraDeviceName the name of the camera device
     * @param plateDeviceName  the name of the plate device
     * @return true if initialization is successful, false otherwise
     */
    public boolean initialize(String cameraDeviceName, String plateDeviceName, int trackNumber) {
        this.camera = cameraManager.getCamera(cameraDeviceName);
        this.plate = plateManager.getPlate(plateDeviceName);
        this.trackNumber = trackNumber;

        if (this.camera == null || this.plate == null) {
            log.error("Initialization failed: Camera or Plate not found.");
            return false;
        }

        log.info("Initialization successful for Camera: {} and Plate: {} and trackNumber :{}", cameraDeviceName, plateDeviceName);
        return true;
    }

    /**
     * Starts the business logic processing in a separate thread.
     */
    public void start() {
        running = true;
        workerThread = new Thread(this::processLogic);
        workerThread.start();
        log.info("Business logic processing started.");
    }


    /**
     * The core logic that runs in a separate thread.
     * This method continuously performs business logic until the real_store_info is stopped.
     */
    private void processLogic() {
        while (running) {
            try {
                log.info("Performing business logic plate:{} ,camera:{}", plate.getThingName(), camera.getThingName());
                if (!camera.getConnected()) {
                    log.error("  Camera:{} is not connected, skipping this cycle.", camera.getThingName());
                    Thread.sleep(2000);
                    continue;
                }
                // 读取条码
                String barcode = camera.readBarcode();
                log.info("Read barcode: {}  cameraName:{}", barcode, camera.getThingName());

                // 如果条码为null或者为空字符串，说明相机掉线，直接进行下一次循环
                if (barcode == null || barcode.isEmpty()) {
//                    log.warn("Barcode is null or empty, skipping this cycle.");
//                    if (plate.getConnected()) {
//                        sendBoardingStatus("1", barcode);
//                        Thread.sleep(500);
//                        sendBoardingStatus("0", barcode);
//                        log.info("Invalid barcode, sent boarding status 0.  cameraName:{}", camera.getThingName());
//                    } else {
//                        log.warn("Plate is not connected, cannot send boarding status. cameraName:{}", camera.getThingName());
//                    }
//                    Thread.sleep(1000);
                    continue;
                }

                // 验证条码
                BarcodeValidationResult result = validateBarcode(barcode);

                // 如果条码验证不通过，发送1
                if (!result.isValid()) {
                    if (plate.getConnected()) {
                        sendBoardingStatus("1", barcode);
//                        if (result.isPlateInstruction()) {
//                            plate.writePointValue(result.getDescription(), "1");
//                        }
                        Thread.sleep(1000);
                        sendBoardingStatus("0", barcode);
                        log.info("Invalid barcode, sent boarding status 0.  cameraName:{}", camera.getThingName());
                    } else {
                        log.warn("Plate is not connected, cannot send boarding status. cameraName:{}", camera.getThingName());
                    }
                } else {
                    // 如果验证通过，发送0
                    if (plate.getConnected()) {
                        //    log.info("Valid barcode, sent boarding status 0.  cameraName:{}", camera.getThingName();
                        sendBoardingOK1(barcode);
                        Thread.sleep(1000);
                        boolean res = sendBoardingOK0(barcode);
                        if (!res) {
                            log.info("Valid barcode, sent boarding status 0 failed .  cameraName:{}", camera.getThingName());
                            sendBoardingStatus("1", barcode);
                            Thread.sleep(500);
                            sendBoardingStatus("0", barcode);
                        }
                    } else {
                        Thread.sleep(1000);
                        log.warn("Plate is not connected, cannot send boarding status.");
                    }
                }

                // 让出线程，不休眠
                Thread.yield();
            } catch (Exception e) {
                log.error("Error occurred in processLogic: ", e);
            }
        }
    }

    /**
     * 发送可以过板信号
     */
    public void sendBoardingOK1(String barcode) {
        if (trackNumber == 1) {
            plate.sendtrack1BoardingOK1(barcode);
            log.info("Valid barcode, sent boarding trace1 OK1 .  plateName:{}", plate.getThingName());
        } else {
            plate.sendtrack2BoardingOK1(barcode);
            log.info("Valid barcode, sent boarding trace2 OK1 . plateName:{}", plate.getThingName());
        }
    }

    /**
     * 对板台OK信号进行复位
     */
    public boolean sendBoardingOK0(String barcode) {
        boolean result;
        if (trackNumber == 1) {
            result = plate.sendtrack1BoardingOK0(barcode);
            log.info("Valid barcode, sent boarding trace1 OK0. plateName:{}", plate.getThingName());
        } else {
            result = plate.sendtrack2BoardingOK0(barcode);
            log.info("Valid barcode, sent boarding trace2 OK0. plateName:{}", plate.getThingName());
        }
        return result;
    }


    /**
     * 发送告警信号，板台不可过板，条码验证不通过或设备连接异常，发1为告警，发0为复位
     *
     * @param status
     */
    private void sendBoardingStatus(String status, String barcode) {
        if (trackNumber == 1) {
            plate.sendtrack1BoardingNG(status, barcode);
            log.info("Invalid barcode, sent boarding trace1 status {}. plateName:{}", status, plate.getThingName());
        } else {
            plate.sendtrack2BoardingNG(status, barcode);
            log.info("Invalid barcode, sent boarding trace2 status {}.plateName:{}", status, plate.getThingName());
        }
    }

    /**
     * Simulates business logic execution.
     * This method should be overridden with actual business logic.
     */
    private void performBusinessLogic() {
        log.info("Performing business logic...  cameraName:{}, plateNamer:{}", camera.getThingName(), plate.getThingName());
    }

    /**
     * 验证条码的方法
     *
     * @param barcode 条码
     * @return 验证结果，true表示通过，false表示不通过
     */
//    private BarcodeValidationResult validateBarcode(String barcode) {
//
//        if(barcode.length()!=16)
//        {
//            log.info("条码长度不对");
//            return  new BarcodeValidationResult(false, "条码长度不对");
//        }
//        // 这里是条码验证的辑，暂时返回true或false
//        // 你可以根据实际需求实现具体的验证逻辑
//        return barcodeService.validateBarcode(ScanRequest.createRequest(camera.getThingName(), barcode)); // 或者 false
//
//    }

    private static final int MAX_HISTORY_SIZE = 3;
    private Queue<String> historyQueue = new LinkedList<>();

//
//    public BarcodeValidationResult validateBarcode(String barcodeInput) {
//        // 分割输入字符串
//        String[] barcodes = barcodeInput.split("\r");
//        log.info("分割后的条码: " + barcodes);
//
//        // 去重集合
//        Set<String> uniqueBarcodes = new HashSet<>();
//
//        // 遍历分割后的条码
//        for (String barcode : barcodes) {
//            // 检查条码长度是否为16位
//            if (barcode.length() != 16) {
//                log.info("条码长度不对: " + barcode);
//                continue;
//            }
//
//            // 检查条码是否在历史记录中
//            if (isBarcodeInHistory(barcode)) {
//                log.info("条码重复: " + barcode);
//                continue;
//            }
//
//            // 添加到去重集合
//            uniqueBarcodes.add(barcode);
//            updateHistory(barcode);
//        }
//
//        // 调用验证服务进行验证
//        for (String barcode : uniqueBarcodes) {
//
//            BarcodeValidationResult result = new BarcodeValidationResult(true,"");   //barcodeService.validateBarcode(ScanRequest.createRequest(camera.getThingName(), barcode));
//            if (result.isValid()) {
//                // 更新历史记录
//
//                return result;
//            }
//        }
//
//        return new BarcodeValidationResult(false, "所有条码验证不通过");
//    }

    public BarcodeValidationResult validateBarcode(String barcodeInput) {
        // 分割输入字符串，按回车符分割
        String[] barcodes = barcodeInput.split("\r");
//        log.info("分割后的条码: " + Arrays.toString(barcodes));
//
//        // 用于存储唯一条码的集合
//        Set<String> uniqueBarcodes = new HashSet<>();
//
//        // 遍历分割后的条码
//        for (String barcode : barcodes) {
//            // 检查条码长度是否为16位
//            if (barcode.length() != 16) {
//                log.info("条码长度不对: " + barcode);
//                continue; // 跳过长度不正确的条码
//            }
//
//            // 检查条码是否在历史记录中
//            if (isBarcodeInHistory(barcode)) {
//                log.info("条码重复: " + barcode);
//                continue; // 跳过重复的条码
//            }
//
//            // 将有效的条码添加到去重集合中
//            uniqueBarcodes.add(barcode);
//            updateHistory(barcode); // 更新历史记录，添加新条码
//        }

        // 初始化一个标志，用于跟踪验证状态
        boolean allValid = true; // 初始值设为true，只有发现无效条码时才设为false
        StringBuilder errorMessages = new StringBuilder(); // 用于收集错误信息

        BarcodeValidationResult result;

// 如果条码数组为空，直接返回验证失败
        if (barcodes.length == 0) {
            return new BarcodeValidationResult(false, "条码数组为空");
        }

// 验证每个唯一的条码
        for (String barcode : barcodes) {
            if (barcode == null || barcode.isEmpty()|| "ERROR".equals(barcode)) {
                allValid = false; // 条码为null或空字符串，验证结果直接为false
                errorMessages.append("条码为空或无效: ").append(barcode).append("\n"); // 收集错误信息
                continue;
            }

            // 模拟验证服务调用（在实际实现中取消注释）
            result = camera.validateBarcode(ScanRequest.createRequest(camera.getThingName(), barcode));

            if (!result.isValid()) {
                allValid = false; // 如果有任意一个条码无效，则将标志设为false
                errorMessages.append("条码验证失败: ").append(barcode).append("\n"); // 收集错误信息
            }
        }

        // 根据整体验证状态返回结果
        if (allValid) {
            return new BarcodeValidationResult(true, "所有条码验证通过");
        } else {
            return new BarcodeValidationResult(false, errorMessages.toString());
        }
    }

    private boolean isBarcodeInHistory(String barcode) {
        return historyQueue.contains(barcode);
    }

    private void updateHistory(String barcode) {
        if (historyQueue.size() >= MAX_HISTORY_SIZE) {
            historyQueue.poll();
        }
        historyQueue.offer(barcode);
    }

    /**
     * Stops the business logic processing and interrupts the thread.
     */
    public void stop() {
        running = false;
        if (workerThread != null) {
            workerThread.interrupt();
        }
        log.info("Business logic processing stopped.");
    }

    /**
     * Ensures that resources are cleaned up before the object is garbage collected.
     * This method stops the business logic thread if it is still running.
     */
    @Override
    protected void finalize() throws Throwable {
        super.finalize();
        stop();
        log.info("CameraPlateAssociationEntity finalized and resources cleaned up.");
    }
}