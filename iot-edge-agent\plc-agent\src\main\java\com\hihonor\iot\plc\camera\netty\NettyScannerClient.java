/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Slf4j
public class NettyScannerClient {
    private EventLoopGroup group;
    private Bootstrap bootstrap;

    private static final int RECONNECT_DELAY = 10; // 重连等待时间（秒）

    private final ScheduledExecutorService executorService; // 线程池
    private final Map<String, ScheduledFuture<?>> scheduledConnectionTasks = new ConcurrentHashMap<>(); // Added field

    private static volatile NettyScannerClient instance;

    // 单例实例
    private static final NettyScannerClient INSTANCE = new NettyScannerClient();

    private NettyScannerClient() {
        group = new NioEventLoopGroup();

        this.executorService = Executors.newScheduledThreadPool(10);
        bootstrap = new Bootstrap();
        bootstrap.group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) throws Exception {
                        // 使用自定义的 LineDecoder 和 LineEncoder
                        ch.pipeline().addLast(new CustomLineDecoder());
                        ch.pipeline().addLast(new CustomLineEncoder());
                        ch.pipeline().addLast(new ConnectionManager()); // 添加 ConnectionManager 处理器
                        ch.pipeline().addLast(new TcpClientHandler()); // 添加 TcpClientHandler 处理器
                    }
                });
    }

    public static NettyScannerClient getInstance() {
        if (instance == null) { // 第一次检查
            synchronized (NettyScannerClient.class) { // 加锁以确保线程安全
                if (instance == null) { // 第二次检查
                    instance = new NettyScannerClient();
                }
            }
        }
        return instance;
    }

    public void connect(String host, int port) {
        String key = host + ":" + port; // 生成唯一的设备标识

        // Cancel any existing task for this key before starting a new one
        ScheduledFuture<?> existingTask = scheduledConnectionTasks.get(key);
        if (existingTask != null && !existingTask.isDone()) {
            existingTask.cancel(false); // false: don't interrupt if running, just prevent future execution
            log.info("在为 {} 启动新任务前，已取消现有的待处理连接/重连任务。", key);
        }

        // 提交连接任务到调度器
        ScheduledFuture<?> future = executorService.schedule(() -> attemptConnection(host, port), 0, TimeUnit.SECONDS);
        scheduledConnectionTasks.put(key, future); // Store the new task
    }

    private void attemptConnection(String host, int port) {
        String key = host + ":" + port; // 生成唯一的设备标识
        log.info("尝试连接到 {}:{}", host, port); // 记录尝试连接的日志

        ChannelFuture futureConnect = bootstrap.connect(host, port); // 尝试连接

        // 等待连接尝试完成
        futureConnect.awaitUninterruptibly(2, TimeUnit.SECONDS);

        if (futureConnect.isSuccess()) {
            log.info("成功连接到 {}:{}", host, port); // 记录成功日志
            // Connection successful, remove this task from map as it won't reschedule.
            scheduledConnectionTasks.remove(key); // Clean up after successful connection
        } else {
            log.error("连接失败到 {}:{}", host, port); // 记录失败日志
            // If connection attempt itself was cancelled (e.g., by stopConnectionAttempts),
            // don't reschedule.
            ScheduledFuture<?> currentTask = scheduledConnectionTasks.get(key);
            if (currentTask != null && currentTask.isCancelled()) {
                log.info("{} 的连接任务已被取消，将不会重新调度。", key);
                // Ensure it's removed if cancellation happened during awaitUninterruptibly
                scheduledConnectionTasks.remove(key);
                // Close the channel if it was opened and then connection failed
                if (futureConnect.channel() != null && futureConnect.channel().isOpen()) {
                    futureConnect.channel().close();
                    log.info("已关闭因连接失败或任务取消而打开的通道到 {}:{}", host, port);
                }
                return;
            }

            log.info("将在 {} 秒后重试连接到 {}...", RECONNECT_DELAY, key); // 记录重试信息
            // futureConnect.cancel(true); // Not needed, awaitUninterruptibly means it's
            // done or timed out
            Channel channel = futureConnect.channel(); // 获取当前通道
            if (channel != null && channel.isOpen()) {
                channel.close(); // 手动关闭通道
                log.info("已关闭因连接失败的通道到 {}:{}", host, port); // 记录关闭日志
            }
            // 如果连接失败，将任务重新放入调度器中
            ScheduledFuture<?> retryFuture = executorService.schedule(() -> attemptConnection(host, port),
                    RECONNECT_DELAY, TimeUnit.SECONDS);
            scheduledConnectionTasks.put(key, retryFuture); // Store the retry task, overwriting the one that just
                                                            // failed.
        }
    }

    /**
     * Stops any scheduled connection or reconnection attempts for the given host
     * and port.
     * Also closes the active connection if one exists.
     *
     * @param host The host of the device.
     * @param port The port of the device.
     */
    public void stopConnectionAttempts(String host, int port) {
        String key = host + ":" + port;
        ScheduledFuture<?> future = scheduledConnectionTasks.remove(key);
        if (future != null) {
            if (!future.isDone()) {
                future.cancel(true); // true to interrupt if running or prevent future execution
                log.info("已取消计划的 {}:{} 连接/重连任务。", host, port);
            } else {
                log.info("计划的 {}:{} 连接/重连任务已完成。", host, port);
            }
        } else {
            log.warn("未找到要取消的 {}:{} 计划连接/重连任务。", host, port);
        }
        // It's also important to ensure any active channel is closed.
        // This is typically handled by ConnectionManager.closeChannel(host, port)
        // which should be called by the facade's close method.
        // For safety, we can try to get it from ConnectionManager if direct access is
        // needed,
        // but ideally the caller (e.g. Camera.destroy via Facade) ensures the channel
        // is closed.
        Channel activeChannel = ConnectionManager.getChannel(host, port);
        if (activeChannel != null && activeChannel.isOpen()) {
            log.info("作为 stopConnectionAttempts 的一部分，正在关闭到 {}:{} 的活动通道。", host, port);
            activeChannel.close();
        }

    }

    public void shutdown() {
        log.info("正在关闭 NettyScannerClient...");
        // First, cancel all scheduled tasks and clear the map
        for (Map.Entry<String, ScheduledFuture<?>> entry : scheduledConnectionTasks.entrySet()) {
            ScheduledFuture<?> task = entry.getValue();
            if (task != null && !task.isDone()) {
                task.cancel(true);
                log.info("在关闭过程中取消了 {} 的待处理任务。", entry.getKey());
            }
        }
        scheduledConnectionTasks.clear();
        log.info("所有待处理的连接任务已取消并清除。");

        if (group != null) {
            group.shutdownGracefully().awaitUninterruptibly();
            log.info("Netty EventLoopGroup 已关闭。");
        }
        if (executorService != null) {
            executorService.shutdown(); // Disable new tasks from being submitted
            try {
                // Wait a while for existing tasks to terminate
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow(); // Cancel currently executing tasks
                    log.warn("执行器服务未在规定时间内终止，强制关闭。");
                    // Wait a while for tasks to respond to being cancelled
                    if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                        log.error("执行器服务在强制关闭后仍未终止。");
                    }
                }
            } catch (InterruptedException ie) {
                // (Re-)Cancel if current thread also interrupted
                executorService.shutdownNow();
                // Preserve interrupt status
                Thread.currentThread().interrupt();
            }
            log.info("ScheduledExecutorService 已关闭。");
        }
        log.info("NettyScannerClient 关闭完成。");
    }

    public void reconnect(String ip, int port) {
        log.info("收到 {}:{} 的重连请求。", ip, port);
        // ConnectionManager.close(ip,port);
        connect(ip, port);
    }

    public static void main(String[] args) {
        // 获取单例实例
        NettyScannerClient client = NettyScannerClient.getInstance();

        // 尝试连接到指定的服务器
        client.connect("***********", 9004); // 替换为实际的 IP 和端口

        // 保持程序运行，直到手动终止
        Runtime.getRuntime().addShutdownHook(new Thread(client::shutdown)); // 确保优雅关闭客户端

        // 无限循环保持主线程运行
        while (true) {
            try {
                Thread.sleep(1000); // 每秒检查一次（可以根据需要调整）
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                break; // 退出循环
            }
        }

        client.shutdown(); // 在程序结束时优雅关闭客户端
    }
}
