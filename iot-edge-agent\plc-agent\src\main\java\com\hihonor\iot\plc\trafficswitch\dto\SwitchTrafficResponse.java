/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.trafficswitch.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 切换流量（更新节点权重）API的响应体。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SwitchTrafficResponse {

    /**
     * 上游服务的ID，应与请求中的ID一致。
     */
    private String id;

    /**
     * 操作的任务ID。
     */
    private String jobId;
}