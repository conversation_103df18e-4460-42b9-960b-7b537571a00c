/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
public class StringSplitter {

    public static void main(String[] args) {
        String input = "02AVXNXX49780174";
        String[] result = splitString(input);

        // 打印结果
        for (String str : result) {
            System.out.println(str);
        }
    }

    /**
     * 分割输入字符串，使用\r作为分隔符
     * @param input 输入的字符串
     * @return 分割后的字符串数组
     */
    public static String[] splitString(String input) {
        // 使用\r作为分隔符分割字符串
        return input.split("\r");
    }
}
