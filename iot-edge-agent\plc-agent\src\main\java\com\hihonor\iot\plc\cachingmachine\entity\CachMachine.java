/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.entity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.cachingmachine.AbstractPLCEntity;
import com.hihonor.iot.plc.thing.ThingTemplate;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Slf4j
@Component
@Scope("prototype")
@ThingTemplate(value = "PLCTemplate", tag = "plc:plate1127_1")
public class CachMachine extends AbstractPLCEntity {

    private List<String> propertyNames = List.of("MEW_plate_1trackStatus",
            "MEW_plate_1track1LayerBoardStatus",
            "MEW_plate_1track1LayerBoardTime",
            "MEW_plate_1track1LayerBarcode-1",
            "MEW_plate_1track1LayerBarcode-2",
            "MEW_plate_1track1LayerBarcode-3",
            "MEW_plate_1track1LayerBarcode-4",
            "MEW_plate_1track1LayerBarcode-5",
            "MEW_plate_1track1LayerBarcode-6",
            "MEW_plate_1track2LayerBoardTime",
            "MEW_plate_1track2LayerBarcode-1",
            "MEW_plate_1track2LayerBarcode-2",
            "MEW_plate_1track2LayerBarcode-4",
            "MEW_plate_1track2LayerBarcode-5",
            "MEW_plate_1track2LayerBarcode-6",
            "MEW_plate_1track4LayerBoardTime",
            "MEW_plate_1track4LayerBarcode-1",
            "MEW_plate_1track4LayerBarcode-2",
            "MEW_plate_1track4LayerBarcode-3",
            "MEW_plate_1track4LayerBarcode-4",
            "MEW_plate_1track4LayerBarcode-5",
            "MEW_plate_1track13LayerBoardTime",
            "MEW_plate_1track13LayerBarcode-1",
            "MEW_plate_1track13LayerBarcode-2",
            "MEW_plate_1track13LayerBarcode-3",
            "MEW_plate_1track13LayerBarcode-4",
            "MEW_plate_1track13LayerBarcode-5",
            "MEW_plate_1track15LayerBarcode-14",
            "MEW_plate_1track15LayerBarcode-15",
            "MEW_plate_1track15LayerBarcode-16",
            "MEW_plate_1track15LayerBarcode-17",
            "MEW_plate_1track15LayerBarcode-18",
            "MEW_plate_1track15LayerBarcode-19",
            "MEW_plate_1track15LayerBarcode-20",
            "MEW_plate_1track16LayerBoardStatus",
            "MEW_plate_1track16LayerBoardTime",
            "MEW_plate_1track16LayerBarcode-1",
            "MEW_plate_1track16LayerBarcode-2",
            "MEW_plate_1track20LayerBoardStatus",
            "MEW_plate_1track20LayerBoardTime",
            "MEW_plate_1track20LayerBarcode-1",
            "MEW_plate_1track20LayerBarcode-2",
            "MEW_plate_1track20LayerBarcode-3",
            "MEW_plate_1track20LayerBarcode-4",
            "MEW_plate_2track4LayerBoardStatus",
            "MEW_plate_2track4LayerBoardTime",
            "MEW_plate_2track4LayerBarcode-1",
            "MEW_plate_2track4LayerBarcode-2",
            "MEW_plate_2track4LayerBarcode-3",
            "MEW_plate_2track4LayerBarcode-4",
            "MEW_plate_2track4LayerBarcode-5",
            "MEW_plate_2track4LayerBarcode-6",
            "MEW_plate_2track4LayerBarcode-7",
            "MEW_plate_2track4LayerBarcode-8",
            "MEW_plate_2track4LayerBarcode-9",
            "MEW_plate_2track4LayerBarcode-10",
            "MEW_plate_2track20LayerBarcode-6",
            "MEW_plate_2track20LayerBarcode-7",
            "MEW_plate_2track20LayerBarcode-8",
            "MEW_plate_2track20LayerBarcode-9",
            "MEW_plate_2track20LayerBarcode-10",
            "MEW_plate_2track20LayerBarcode-11",
            "MEW_plate_2track20LayerBarcode-12",
            "MEW_plate_2track20LayerBarcode-13",
            "MEW_plate_2track20LayerBarcode-14",
            "MEW_plate_2track20LayerBarcode-15",
            "MEW_plate_2track20LayerBarcode-16",
            "MEW_plate_2track20LayerBarcode-17",
            "MEW_plate_2track20LayerBarcode-18",
            "MEW_plate_2track20LayerBarcode-19",
            "MEW_plate_2track20LayerBarcode-20");

    @Override
    protected List<String> getAllPropertyNames() {
        return propertyNames;
    }

    @Override
    protected void initPropertyNames() {


    }

    @Override
    protected Map<String, String> processMap(Map<String, String> input) {
        // 用于存储中间结果的Map
        Map<String, String[]> intermediateResult = new HashMap<>();

        // 定义正则表达式匹配特定格式的键
        Pattern pattern = Pattern.compile("MEW_plate_(\\d+track\\d+LayerBarcode)-(\\d+)");

        // 遍历输入的Map
        for (Map.Entry<String, String> entry : input.entrySet()) {
            String key = entry.getKey();
            Integer value = Integer.parseInt(entry.getValue());
            Matcher matcher = pattern.matcher(key);

            // 如果键匹配特定格式
            if (matcher.find()) {
                // 提取newKey和k值
                String newKey = matcher.group(1);
                int k = Integer.parseInt(matcher.group(2));

                // 获取对应的数组，如果不存在，则创建一个新数组
                String[] valuesArray = intermediateResult.computeIfAbsent(newKey, x -> new String[20]);

                // 在正确的位置存储value
                if (value.intValue() == 0) {
                    valuesArray[k - 1] = "";
                } else {
                    valuesArray[k - 1] = Character.toString((char) value.intValue());
                }
            } else {
                // 对于不匹配的键，简单地删除前缀并存储
                String newKey = key.substring(10);
                intermediateResult.put(newKey, new String[]{String.valueOf(value.intValue())});
            }
        }

        // 将中间结果转换为最终结果
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, String[]> entry : intermediateResult.entrySet()) {
            result.put(entry.getKey(), String.join("", entry.getValue()));
        }

        return result;
    }

    @Override
    public void checkAndSendStatusChange(Map<String, String> oldMap, Map<String, String> newMap) {
        // 获取旧状态和新状态
        String oldStatus2 = oldMap.getOrDefault("2trackStatus", null);
        String newStatus2 = newMap.getOrDefault("2trackStatus", null);

        String oldStatus1 = oldMap.getOrDefault("1trackStatus", null);
        String newStatus1 = newMap.getOrDefault("1trackStatus", null);

        // 比较2trackStatus
        if (!Objects.equals(oldStatus2, newStatus2)) {
            // 状态改变，调用接口
            log.info("2trackStatus changed from {} to {}", oldStatus2, newStatus2);
        }
        // 比较1trackStatus
        if (!Objects.equals(oldStatus1, newStatus1)) {
            // 状态改变，调用接口
            log.info("1trackStatus changed from {} to {}", oldStatus1, newStatus1);
        }
    }

    @Override
    public void checkAndSendAdvancedAlarm(Map<String, String> oldMap, Map<String, String> newMap) {
        checkAndSendAlarmForAttribute(oldMap, newMap, "2trackStatus", "3", "2");
        checkAndSendAlarmForAttribute(oldMap, newMap, "1trackStatus", "3");

    }

    @Override
    protected String getFullName() {

        return "MEW_plate_";
    }

}
