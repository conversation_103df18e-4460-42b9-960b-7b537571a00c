@startuml
start

:初始化连接;
:主控制系统向摄像头发送初始化连接指令;
:主控制系统向板台发送初始化连接指令;

repeat
    :开始循环处理;

    :检查摄像头连接状态;
    :摄像头已连接;

    if (摄像头已连接?) then (yes)
        :读取条码;
        :主控制系统向摄像头发送读取条码的指令;
        :摄像头返回条码数据;

        if (条码数据为NULL?) then (yes)
            :跳过本次循环，重新读取条码;
        else (no)
            :验证条码;
            :主控制系统向条码验证服务发送验证条码的请求;
            :条码验证服务返回验证结果;

            if (验证通过?) then (yes)
                :检查板台连接状态;
                if (板台已连接?) then (yes)
                    :发送允许通过信号 (OK1);
                    :发送重置信号 (OK0);
                else (no)
                    :记录错误日志;
                endif
            else (no)
                :检查板台连接状态;
                if (板台已连接?) then (yes)
                    :发送告警信号 (1);
                    :发送复位信号 (0);
                else (no)
                    :记录错误日志;
                endif
            endif
        endif
    else (no)
        :记录错误日志;
    endif

    :等待/让出CPU时间;
repeat while (未收到停止指令)

:停止处理;
:主控制系统断开与摄像头的连接;
:主控制系统断开与板台的连接;

stop
@enduml