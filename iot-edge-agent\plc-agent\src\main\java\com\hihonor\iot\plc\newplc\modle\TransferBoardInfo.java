/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.modle;


import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
/**
 * 过板台信息实体类
 */
@Data
@TableName("transfer_board_info")
@Valid
public class TransferBoardInfo {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备名称
     */
    @TableField("name")
    @NotBlank(message = "设备名称不能为空")
    private String name;

    /**
     * 设备IP加端口号
     */
    @TableField("ip_port")
    @NotBlank(message = "设备IP和端口号不能为空")
    @Pattern(regexp = "^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}:\\d{1,5}$", message = "设备IP和端口号格式不正确")
    private String ipPort;

    /**
     * 设备描述
     */
    @TableField("description")
    private String description;
}