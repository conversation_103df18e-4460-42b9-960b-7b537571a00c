/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import java.util.Iterator;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ThreadFactory;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

/**
 * 条码缓存管理类
 * 用于管理和检测条码的重复性,防止在短时间内重复处理相同的条码
 * 采用单例模式实现,为每个IP维护一个独立的条码队列
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Slf4j
public class BarcodeCache {
    // 重复检测的时间窗口,5秒内不允许重复
    private static final long DUPLICATE_CHECK_WINDOW = 10000;
    // 每个IP的缓存队列最大容量限制
    private static final int MAX_CACHE_SIZE = 15;
    // 线程池名称前缀
    private static final String THREAD_NAME_PREFIX = "barcode-cleanup-";

    // 修改数据结构存储时间戳
    private static class BarcodeRecord {
        final String barcode;
        final long timestamp;

        BarcodeRecord(String barcode) {
            this.barcode = Objects.requireNonNull(barcode, "条码不能为空");
            this.timestamp = System.currentTimeMillis();
        }
    }

    // 使用并发队列
    private final Map<String, Queue<BarcodeRecord>> ipBarcodeMap = new ConcurrentHashMap<>();
    // 清理过期记录的定时执行器
    private final ScheduledExecutorService cleanupExecutor;
    private volatile ScheduledFuture<?> cleanupTask;

    // 使用双重检查锁
    private static volatile BarcodeCache instance;
    private volatile boolean isShutdown = false;

    /**
     * 私有构造函数,启动清理任务
     */
    private BarcodeCache() {
        // 使用自定义ThreadFactory来设置线程名称
        ThreadFactory threadFactory = r -> {
            Thread thread = new Thread(r);
            thread.setName(THREAD_NAME_PREFIX + thread.getId());
            thread.setDaemon(true); // 设置为守护线程
            return thread;
        };
        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(threadFactory);
        startCleanupTask();
        log.info("条码缓存管理器初始化完成，清理任务已启动");
    }

    /**
     * 获取BarcodeCache单例实例
     *
     * @return BarcodeCache实例
     * @throws IllegalStateException 如果实例已关闭
     */
    public static BarcodeCache getInstance() {
        if (instance == null) {
            synchronized (BarcodeCache.class) {
                if (instance == null) {
                    instance = new BarcodeCache();
                    log.info("创建新的条码缓存管理器实例");
                }
            }
        }
        if (instance.isShutdown) {
            throw new IllegalStateException("条码缓存管理器已关闭");
        }
        return instance;
    }

    /**
     * 启动定期清理任务
     * 每隔DUPLICATE_CHECK_WINDOW时间清理一次队列
     */
    private void startCleanupTask() {
        try {
            cleanupTask = cleanupExecutor.scheduleAtFixedRate(
                    this::cleanupBarcodes,
                    DUPLICATE_CHECK_WINDOW / 2,
                    DUPLICATE_CHECK_WINDOW / 2,
                    TimeUnit.MILLISECONDS);
            log.info("启动清理任务，间隔时间：{} 毫秒", DUPLICATE_CHECK_WINDOW / 2);
        } catch (Exception e) {
            log.error("启动清理任务失败", e);
           // throw new IllegalStateException("启动清理任务失败", e);
        }
    }

    /**
     * 清理所有队列中的过期记录
     */
    private void cleanupBarcodes() {
        try {
            long now = System.currentTimeMillis();
            int totalRemoved = 0;
            for (Map.Entry<String, Queue<BarcodeRecord>> entry : ipBarcodeMap.entrySet()) {
                String ip = entry.getKey();
                Queue<BarcodeRecord> queue = entry.getValue();
                synchronized (queue) {
                    int sizeBefore = queue.size();
                    queue.removeIf(record -> now - record.timestamp > DUPLICATE_CHECK_WINDOW);
                    int removed = sizeBefore - queue.size();
                    totalRemoved += removed;
                    if (removed > 0) {
                        log.info("IP {} 清理过期记录 {} 条", ip, removed);
                    }
                }
            }
            if (totalRemoved > 0) {
                log.info("本次清理总计移除 {} 条过期记录", totalRemoved);
            }
        } catch (Exception e) {
            log.error("执行清理任务时发生异常", e);
        }
    }

    /**
     * 检查指定IP的条码是否重复
     *
     * @param ip      设备IP地址
     * @param barcode 待检查的条码
     * @return true表示重复, false表示不重复
     * @throws IllegalArgumentException 如果IP或条码为空
     */
    public boolean isDuplicateBarcode(String ip, String barcode) {
        if (ip == null || ip.trim().isEmpty()) {
            throw new IllegalArgumentException("IP地址不能为空");
        }
        if (barcode == null || barcode.trim().isEmpty()) {
            throw new IllegalArgumentException("条码不能为空");
        }

        Queue<BarcodeRecord> queue = ipBarcodeMap.computeIfAbsent(ip,
                k -> {
                    log.info("为IP {} 创建新的条码队列", ip);
                    return new ConcurrentLinkedQueue<>();
                });

        long now = System.currentTimeMillis();

        synchronized (queue) {
            try {
                // 清理过期记录
                int sizeBefore = queue.size();
                Iterator<BarcodeRecord> it = queue.iterator();
                while (it.hasNext()) {
                    if (now - it.next().timestamp > DUPLICATE_CHECK_WINDOW) {
                        it.remove();
                    }
                }
                int removed = sizeBefore - queue.size();
                if (removed > 0) {
                    log.info("IP {} 在重复检查时清理 {} 条过期记录", ip, removed);
                }

                // 检查重复
                boolean exists = queue.stream()
                        .anyMatch(r -> r.barcode.equals(barcode.trim()));

                if (!exists) {
                    queue.offer(new BarcodeRecord(barcode.trim()));
                    log.info("IP {} 添加新条码 {}", ip, barcode);
                    
                    // 控制队列大小
                    while (queue.size() > MAX_CACHE_SIZE) {
                        BarcodeRecord removedBarcode = queue.poll();
                        if (removedBarcode != null) {
                            log.info("由于超出缓存限制，移除IP {} 的最早条码 {}", ip, removedBarcode.barcode);
                        }
                    }
                } else {
                    log.info("检测到IP {} 的重复条码 {}", ip, barcode);
                }
                return exists;
            } catch (Exception e) {
                log.error("处理条码 {} 时发生异常，IP: {}", barcode, ip, e);
                return false;
            }
        }
    }

    /**
     * 移除指定IP的所有条码记录
     *
     * @param ip 要移除的设备IP地址
     * @throws IllegalArgumentException 如果IP为空
     */
    public void removeDevice(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            throw new IllegalArgumentException("IP地址不能为空");
        }
        Queue<BarcodeRecord> removed = ipBarcodeMap.remove(ip);
        if (removed != null) {
            log.info("移除IP {} 的条码缓存，共 {} 条记录", ip, removed.size());
        }
    }

    /**
     * 关闭缓存管理器
     * 取消清理任务并关闭执行器
     */
    public synchronized void shutdown() {
        if (!isShutdown) {
            isShutdown = true;
            try {
                if (cleanupTask != null) {
                    cleanupTask.cancel(true);
                    log.info("清理任务已取消");
                }
                cleanupExecutor.shutdown();
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                    log.warn("强制关闭清理任务执行器");
                }
                ipBarcodeMap.clear();
                log.info("条码缓存管理器已完全关闭");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("关闭缓存管理器时被中断", e);
            }
        }
    }
}