@startuml
scale 2.0
skinparam dpi 300
skinparam backgroundColor white
skinparam defaultFontSize 14
skinparam actorStyle awesome
skinparam sequenceMessageAlign direction
skinparam maxMessageSize 300
skinparam responseMessageBelowArrow true
skinparam sequenceGroupBodyBackgroundColor transparent

title 过站管控系统流程图 \n(高可用性要求99.95%，端到端响应<1s)

box "设备层" #LightYellow
participant "单板" as PCB
participant "相机" as Camera
participant "板台\n(默认关闭)" as Platform
end box

box "系统层" #LightBlue
participant "IOT平台" as IOT #White
participant "MES系统" as MES #White
end box

note over Camera, MES: 系统间通信采用同步响应模式(Request-Reply)\nMES接口调用需进行动态Token验证

== 设备状态监控(持续进行) ==
IOT -> Camera: 设备状态检测
IOT -> Platform: 设备状态检测
note right: 实时监控设备\n在线及工作状态

== 单板过站流程(1s内完成) ==
PCB -> Camera: 单板到达扫码位置
activate Camera
Camera -> Camera: 条码格式校验
Camera -> IOT: 上报条码数据
deactivate Camera

activate IOT
IOT -> IOT: 数据完整性校验
IOT -> MES: 发送条码验证请求
note right: 动态Token验证

alt 条码验证通过
    MES --> IOT: 返回验证成功
    IOT -> Platform: 发送开启信号(OK1)
    activate Platform
    Platform -> PCB: 临时开启，放行单板
    PCB -> Platform: 单板通过
    Platform -> Platform: 自动恢复关闭状态(OK0)
    Platform -> IOT: 执行结果反馈
    deactivate Platform
else 条码验证失败
    MES --> IOT: 返回验证失败
    IOT -> Platform: 保持关闭并发送报警
    note over IOT: 记录异常并通知管理员
end
deactivate IOT

== 状态记录与监控 ==
IOT -> IOT: 记录完整业务链路日志
note right: 包含时间戳、状态变更\n异常信息等完整记录

@enduml