/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.trafficswitch.dto;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 上游服务节点信息，对应API响应中的 "upstreams" 数组元素
 */
@Data
@NoArgsConstructor
public class UpstreamNode {

    /**
     * 上游ID
     */
    private String id;

    /**
     * 上游名称
     */
    private String name; // 根据用户提供的响应示例，添加name字段

    /**
     * 节点列表
     */
    private List<NodeInfo> nodes;

    // 根据用户提供的响应示例，可以添加其他相关字段，例如：
    // private String upstreamResource;
    // private String nodeType;
    // private String status;
    // 目前仅包含ID, name 和 nodes 以满足核心需求和示例解析。
}