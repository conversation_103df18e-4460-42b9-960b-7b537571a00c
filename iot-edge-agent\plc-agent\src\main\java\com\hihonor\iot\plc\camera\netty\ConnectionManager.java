/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Slf4j
@ChannelHandler.Sharable
public class ConnectionManager extends ChannelInboundHandlerAdapter {

    public static final Map<String, NioSocketChannel> connectionMap = new ConcurrentHashMap<>();
    private static final Map<String, DeviceStatusListener> listeners = new ConcurrentHashMap<>();

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        NioSocketChannel channel = (NioSocketChannel) ctx.channel();
        String key = getKey(channel);
        connectionMap.put(key, channel);
        log.info("设备已连接: {}", channel.remoteAddress());
        notifyDeviceConnected(channel);
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.debug("channelInactive 被调用: {}", ctx.channel().remoteAddress());
        NioSocketChannel channel = (NioSocketChannel) ctx.channel();
        String key = getKey(channel);
        connectionMap.remove(key);
        log.info("设备已断开: {}", channel.remoteAddress());
        notifyDeviceDisconnected(channel);
        // channel.close();
        super.channelInactive(ctx);
    }

    public static boolean isDeviceOnline(NioSocketChannel channel) {
        String key = getKey(channel);
        return connectionMap.containsKey(key);
    }

    public static boolean isDeviceOnline(String ipPort) {
        return connectionMap.containsKey(ipPort);
    }

    public static boolean sendMessage(Channel channel, String message) {
        if (channel != null && channel.isActive()) {
            channel.writeAndFlush(message);
            return true;
        }
        return false;
    }

    public static boolean sendMessage(String ipPort, String message) {
        NioSocketChannel channel = connectionMap.get(ipPort);
        if (channel != null && channel.isActive()) {
            channel.writeAndFlush(message);
            return true;
        }
        return false;
    }

    public static void addDeviceStatusListener(String ipPort, DeviceStatusListener listener) {
        if (ipPort != null && listener != null) {
            listeners.put(ipPort, listener);
        } else {
            log.warn("尝试添加空的监听器或 ipPort");
        }
    }

    public static void removeDeviceStatusListener(String ipPort) {
        if (ipPort != null) {
            listeners.remove(ipPort);
        } else {
            log.warn("尝试使用空的 ipPort 移除监听器");
        }
    }

    public static void close(String ip, int port) {
        String key = ip + ":" + port;
        NioSocketChannel channel = connectionMap.get(key); // 获取对应的 Channel
        if (channel != null) {
            channel.close(); // 关闭 Channel
            connectionMap.remove(key); // 从 connectionMap 中移除
            log.info("设备 {} 的连接已关闭。", key);
        } else {
            log.info("未找到设备 {} 的活动连接。", key);
        }
    }

    /**
     * Retrieves the active channel for a given host and port.
     *
     * @param host The host IP address.
     * @param port The port number.
     * @return The NioSocketChannel if found and active, otherwise null.
     */
    public static Channel getChannel(String host, int port) {
        String key = host + ":" + port;
        NioSocketChannel channel = connectionMap.get(key);
        // Return the channel only if it exists and is active
        if (channel != null && channel.isActive()) {
            return channel;
        }
        return null; // Return null if not found or not active
    }

    private static String getKey(NioSocketChannel channel) {
        return channel.remoteAddress().getHostString() + ":" + channel.remoteAddress().getPort();
    }

    public static void notifyDeviceConnected(NioSocketChannel channel) {
        String key = getKey(channel);
        DeviceStatusListener listener = listeners.get(key);
        if (listener != null) {
            listener.onDeviceConnected();
        }
    }

    public static void notifyDeviceDisconnected(NioSocketChannel channel) {
        String key = getKey(channel);
        DeviceStatusListener listener = listeners.get(key);
        if (listener != null) {
            listener.onDeviceDisconnected();
        }
    }

}