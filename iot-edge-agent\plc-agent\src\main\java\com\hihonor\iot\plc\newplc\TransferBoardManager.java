/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesResponse;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesResponse;
import com.hihonor.iot.plc.newplc.modle.DeviceStatus;
import com.hihonor.iot.plc.newplc.modle.PageInfo;
import com.hihonor.iot.plc.newplc.modle.TransferBoardSignal;
import com.hihonor.iot.plc.newplc.modle.TransferBoardStatus;
import com.hihonor.iot.plc.newplc.modle.TransferBoardInfo;
import com.hihonor.iot.plc.newplc.repo.TransferBoardInfoService;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@Slf4j
@Component
public class TransferBoardManager {

    @Autowired
    private TransferBoardInfoService transferBoardInfoService;

    private Map<String, TransferBoard> transferBoardMap = new HashMap<>();


    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    TaskRegistrar taskRegistrar;


    /**
     * 初始化,从数据库加载过板台信息并创建过板台对象
     */
    public void init() {
        try {
            log.info("开始初始化过板台...");
            taskRegistrar.init();
            log.info("taskRegistrar初始化完成");
            // 从数据库查询所有的过板台信息
            List<TransferBoardInfo> transferBoardInfoList = transferBoardInfoService.getAllTransferBoardInfos();
            // 遍历过板台信息列表
            for (TransferBoardInfo info : transferBoardInfoList) {
                // 创建过板台对象
                TransferBoard transferBoard = applicationContext.getBean(TransferBoard.class);
                transferBoard.init(info.getIpPort().split(":")[0], Integer.parseInt(info.getIpPort().split(":")[1]), info.getName(),transferBoard);
                // 将过板台对象放入Map中
                transferBoardMap.put(info.getName(), transferBoard);
                log.info("创建过板台: {} - {}", info.getName(), info.getIpPort());
            }
        }
        catch (Exception ex)
        {
            log.error("初始化TransferBoardManager失败,请重启程序: {}", ex.getMessage());
        }

    }


    /**
     * 批量读取过板台属性值
     *
     * @param request 批量读取过板台属性请求对象
     * @return 批量读取过板台属性响应对象
     */
    public BatchReadTransferBoardPropertiesResponse batchReadTransferBoardProperties(BatchReadTransferBoardPropertiesRequest request) {
        // 从请求对象中获取要读取的过板台属性列表
        List<BatchReadTransferBoardPropertiesRequest.TransferBoardProperty> transferBoardProperties = request.getTransferBoardProperties();

        // 创建批量读取过板台属性响应对象
        BatchReadTransferBoardPropertiesResponse response = new BatchReadTransferBoardPropertiesResponse();
        // 创建一个列表,用于存储每个过板台属性的读取结果
        List<BatchReadTransferBoardPropertiesResponse.TransferBoardPropertyResult> results = new ArrayList<>();

        // 遍历要读取的过板台属性列表
        for (BatchReadTransferBoardPropertiesRequest.TransferBoardProperty transferBoardProperty : transferBoardProperties) {
            // 获取过板台名称
            String name = transferBoardProperty.getName();
            // 获取要读取的属性名称列表
            List<String> propertyNames = transferBoardProperty.getPropertyNames();

            // 从过板台映射中获取对应名称的过板台对象
            TransferBoard transferBoard = transferBoardMap.get(name);
            if (transferBoard != null) {
                // 如果过板台存在,创建一个映射用于存储属性名称和对应的值
                Map<String, String> propertyValues = new HashMap<>();
                // 遍历要读取的属性名称列表
                for (String propertyName : propertyNames) {
                    // 调用过板台对象的方法,根据属性名称读取属性值
                    String propertyValue = transferBoard.readValuesByPointName(propertyName);
                    // 将属性名称和对应的值存入映射
                    propertyValues.put(propertyName, propertyValue);
                }
                // 创建一个过板台属性读取结果对象,包含过板台名称、读取成功标志、成功消息和属性值映射,并将其添加到结果列表中
                results.add(new BatchReadTransferBoardPropertiesResponse.TransferBoardPropertyResult(name, true, "属性读取成功", propertyValues));
            } else {
                // 如果过板台不存在,创建一个过板台属性读取结果对象,包含过板台名称、读取失败标志和失败消息,并将其添加到结果列表中
                results.add(new BatchReadTransferBoardPropertiesResponse.TransferBoardPropertyResult(name, false, "过板台不存在", null));
            }
        }

        // 将过板台属性读取结果列表设置到响应对象中
        response.setTransferBoardPropertyResults(results);
        // 返回批量读取过板台属性响应对象
        return response;
    }


    /**
     * 批量写入过板台属性值
     *
     * @param request 批量写入过板台属性请求对象
     * @return 批量写入过板台属性响应对象
     */
    public BatchWriteTransferBoardPropertiesResponse batchWriteTransferBoardProperties(BatchWriteTransferBoardPropertiesRequest request) {
        // 从请求对象中获取要写入的过板台属性列表
        List<BatchWriteTransferBoardPropertiesRequest.TransferBoardWriteProperty> writeProperties = request.getTransferBoardWriteProperties();
        // 创建批量写入过板台属性响应对象
        BatchWriteTransferBoardPropertiesResponse response = new BatchWriteTransferBoardPropertiesResponse();
        // 创建一个列表,用于存储每个过板台属性的写入结果
        List<BatchWriteTransferBoardPropertiesResponse.TransferBoardWriteResult> results = new ArrayList<>();

        // 遍历要写入的过板台属性列表
        for (BatchWriteTransferBoardPropertiesRequest.TransferBoardWriteProperty writeProperty : writeProperties) {
            // 获取过板台名称
            String name = writeProperty.getName();
            // 获取要写入的属性映射
            Map<String, String> propertiesToWrite = writeProperty.getPropertiesToWrite();
            // 从过板台映射中获取对应名称的过板台对象
            TransferBoard transferBoard = transferBoardMap.get(name);
            // 创建一个标志,用于表示所有属性是否都写入成功
            boolean overallSuccess = true;
            // 创建一个StringBuilder,用于构建写入结果的消息
            StringBuilder messageBuilder = new StringBuilder();

            if (transferBoard != null) {
                // 如果过板台存在,遍历要写入的属性映射
                for (Map.Entry<String, String> entry : propertiesToWrite.entrySet()) {
                    // 获取属性名称和对应的值
                    String pointName = entry.getKey();
                    String value = entry.getValue();
                    // 调用过板台对象的方法,根据属性名称写入属性值
                    boolean success = transferBoard.writeValueByPointName(pointName, value);
                    // 更新总体写入成功标志
                    overallSuccess &= success;
                    // 将每个属性的写入结果追加到消息中
                    messageBuilder.append(String.format("Point: %s, Value: %s, Success: %s; ", pointName, value, success));
                }
                // 创建一个过板台属性写入结果对象,包含过板台名称、总体写入成功标志和写入结果消息,并将其添加到结果列表中
                results.add(new BatchWriteTransferBoardPropertiesResponse.TransferBoardWriteResult(name, overallSuccess, messageBuilder.toString()));
            } else {
                // 如果过板台不存在,记录警告日志
                log.warn("TransferBoard not found: {}", name);
                // 创建一个过板台属性写入结果对象,包含过板台名称、写入失败标志和失败消息,并将其添加到结果列表中
                results.add(new BatchWriteTransferBoardPropertiesResponse.TransferBoardWriteResult(name, false, "TransferBoard not found"));
            }
        }

        // 将过板台属性写入结果列表设置到响应对象中
        response.setTransferBoardWriteResults(results);
        // 返回批量写入过板台属性响应对象
        return response;
    }


    /**
     * 根据过板台名称获取过板台对象
     *
     * @param name 过板台名称
     * @return 过板台对象
     */
    public TransferBoard getTransferBoard(String name) {
        return transferBoardMap.get(name);
    }


    /**
     * 添加过板台
     *
     * @param transferBoardInfo 要添加的过板台信息对象
     */
    public void addTransferBoard(TransferBoardInfo transferBoardInfo) {
        // 根据过板台名称判断是否已存在
        TransferBoard existingTransferBoard = transferBoardMap.get(transferBoardInfo.getName());
        if (existingTransferBoard != null) {
            // 如果已存在,判断IP和端口是否相等
            if (existingTransferBoard.getIp().equals(transferBoardInfo.getIpPort().split(":")[0]) &&
                    existingTransferBoard.getPort() == Integer.parseInt(transferBoardInfo.getIpPort().split(":")[1])) {
                // IP和端口相等,直接返回
                log.info("过板台已存在且配置未发生变化: {}", transferBoardInfo.getName());
                return;
            } else {
                // IP或端口发生变化,更新数据库并重新初始化现有实例
                transferBoardInfoService.saveOrUpdateTransferBoardInfo(transferBoardInfo);

                // 重新初始化现有实例
                existingTransferBoard.reInit(transferBoardInfo.getIpPort().split(":")[0], Integer.parseInt(transferBoardInfo.getIpPort().split(":")[1]), transferBoardInfo.getName());
                log.info("过板台已重新初始化: {}", transferBoardInfo.getName());

                return;
            }
        }
        // 过板台不存在,插入数据库
        transferBoardInfoService.saveOrUpdateTransferBoardInfo(transferBoardInfo);
        log.info("过板台信息已插入数据库: {}", transferBoardInfo.getName());
        // 创建过板台对象并初始化
        TransferBoard transferBoard = applicationContext.getBean(TransferBoard.class);
        transferBoard.init(transferBoardInfo.getIpPort().split(":")[0], Integer.parseInt(transferBoardInfo.getIpPort().split(":")[1]), transferBoardInfo.getName(),transferBoard);
        // 将过板台对象放入Map中
        transferBoardMap.put(transferBoardInfo.getName(), transferBoard);
        log.info("过板台已创建并初始化: {}", transferBoardInfo.getName());
    }



    public void batchAddTransferBoard(List<TransferBoardInfo> transferBoardInfoList) {
        for (TransferBoardInfo transferBoardInfo : transferBoardInfoList) {
            addTransferBoard(transferBoardInfo);
        }
    }

    /**
     * 移除过板台
     *
     * @param name 要移除的过板台名称
     */
    public void removeTransferBoard(String name) {
        // 从Map中移除过板台
        TransferBoard removedTransferBoard = transferBoardMap.remove(name);
        if (removedTransferBoard != null) {
            // 如果成功移除,则同时从数据库中删除相应的过板台信息
            transferBoardInfoService.deleteTransferBoardInfoByName(name);
            log.info("过板台已移除,数据库信息已删除: {}", name);
            removedTransferBoard.destroy();

        } else {
            log.warn("要移除的过板台不存在: {}", name);
        }
    }

    /**
     * 批量删除过板台
     *
     * @param names 要删除的过板台名称列表
     */
    public void batchRemoveTransferBoard(List<String> names) {
        for (String name : names) {
            removeTransferBoard(name);
        }
    }



    /**
     * 根据过板台名称获取过板台状态信息(模糊查询)
     *
     * @param name 过板台名称
     * @return 过板台状态信息对象列表
     */
    public List<TransferBoardStatus> getTransferBoardStatus(String name) {
        List<TransferBoardStatus> statusList = new ArrayList<>();
        for (Map.Entry<String, TransferBoard> entry : transferBoardMap.entrySet()) {
            String transferBoardName = entry.getKey();
            if (transferBoardName.contains(name)) {
                TransferBoard transferBoard = entry.getValue();
                statusList.add(transferBoard.getStatus());
            }
        }
        return statusList;
    }

    /**
     * 获取过板台状态信息(模糊查询、分页)
     *
     * @param name 过板台名称
     * @param page 当前页码
     * @param size 每页大小
     * @return 过板台状态信息对象分页列表
     */
    public PageInfo<TransferBoardStatus> getTransferBoardStatus(String name, int page, int size) {
        // 查询所有过板台状态信息列表
        List<TransferBoardStatus> allStatus = new ArrayList<>();
        List<TransferBoard> allTransferBoards = getAllTransferBoards();

        // 遍历所有过板台,获取其状态信息
        for (TransferBoard transferBoard : allTransferBoards) {
            TransferBoardStatus status = transferBoard.getStatus();
            if (status != null) {
                allStatus.add(status);
            }
        }

        // 进行模糊匹配
        List<TransferBoardStatus> filteredStatus = new ArrayList<>();
        for (TransferBoardStatus status : allStatus) {
            // 判断过板台名称是否包含指定的名称
            if (status.getName() != null && status.getName().contains(name)) {
                filteredStatus.add(status);
            }
        }

        // 进行分页
        int total = filteredStatus.size();
        int fromIndex = (page - 1) * size;
        int toIndex = Math.min(fromIndex + size, total);

        List<TransferBoardStatus> pageStatus;
        if (fromIndex >= 0 && fromIndex < total) {
            pageStatus = filteredStatus.subList(fromIndex, toIndex);
        } else {
            pageStatus = new ArrayList<>();
        }

        // 创建PageInfo对象
        return new PageInfo<>(pageStatus, total);
    }


    /**
     * 获取所有的过板台对象
     *
     * @return 过板台对象列表
     */
    public List<TransferBoard> getAllTransferBoards() {
        return transferBoardMap.values().stream().collect(Collectors.toList());
    }


    /**
     * 根据过板台名称获取进板信号
     *
     * @param name 过板台名称
     * @return 进板信号值
     */
    public String getLoadingSignal(String name) {
        TransferBoard transferBoard = transferBoardMap.get(name);
        if (transferBoard != null) {
            String signal = transferBoard.getLoadingSignal();
            log.info("获取过板台 {} 的进板信号: {}", name, signal);
            return signal;
        } else {
            log.warn("过板台不存在: {}", name);
            return null;
        }
    }

    /**
     * 根据过板台名称获取设备状态
     *
     * @param name 过板台名称
     * @return 设备状态值
     */
    public String getDeviceStatus(String name) {
        TransferBoard transferBoard = transferBoardMap.get(name);
        if (transferBoard != null) {
            String status = transferBoard.getDeviceStatus();
            log.info("获取过板台 {} 的设备状态: {}", name, status);
            return status;
        } else {
            log.warn("过板台不存在: {}", name);
            return null;
        }
    }

    /**
     * 批量获取过板台进板信号(模糊查询)
     *
     * @param name 过板台名称(模糊查询)
     * @return 过板台进板信号列表
     */
    public List<TransferBoardSignal> getBatchLoadingSignal(String name) {
        return transferBoardMap.values().stream()
                .filter(transferBoard -> transferBoard.getThingName().contains(name))
                .map(transferBoard -> {
                    String signal = transferBoard.getLoadingSignal();
                    return new TransferBoardSignal(transferBoard.getThingName(), signal);
                })
                .collect(Collectors.toList());
    }



    /**
     * 批量获取过板台设备状态(模糊查询)
     *
     * @param name 过板台名称(模糊查询)
     * @return 过板台设备状态列表
     */
    public List<DeviceStatus> getBatchDeviceStatus(String name) {
        return transferBoardMap.values().stream()
                .filter(transferBoard -> transferBoard.getThingName().contains(name))
                .map(transferBoard -> {
                    String status = transferBoard.getDeviceStatus();
                    return new DeviceStatus(transferBoard.getThingName(), status);
                })
                .collect(Collectors.toList());
    }


    /**
     * 批量下发过板信号
     *
     * @param signalMap 过板信号MAP,key为过板台名称,value为要下发的信号值
     * @return 下发结果MAP,key为过板台名称,value为下发结果
     */
    public Map<String, Boolean> batchWriteLoadingSignal(Map<String, String> signalMap) {
        Map<String, Boolean> resultMap = new HashMap<>(signalMap.size());

        for (Map.Entry<String, String> entry : signalMap.entrySet()) {
            String thingName = entry.getKey();
            String signal = entry.getValue();

            TransferBoard transferBoard = transferBoardMap.get(thingName);
            if (transferBoard != null) {
                boolean success = transferBoard.writeLoadingSignal(signal);
                resultMap.put(thingName, success);
                if (success) {
                    log.info("批量下发过板信号成功,过板台: {},信号值: {}", thingName, signal);
                } else {
                    log.error("批量下发过板信号失败,过板台: {},信号值: {}", thingName, signal);
                }
            } else {
                log.warn("批量下发过板信号失败,过板台不存在: {}", thingName);
                resultMap.put(thingName, false);
            }
        }

        return resultMap;
    }

}