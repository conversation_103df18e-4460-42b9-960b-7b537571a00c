/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Data
@TableName("iot_admin.camera_plate_association_by_name")
public class CameraPlateAssociation {
    @TableId(type = IdType.AUTO)
    private Long associationId;

    @NotBlank(message = "Camera device name is required")
    private String cameraDeviceName;  // 必须

    @NotBlank(message = "Plate device name is required")
    private String plateDeviceName; // 必须

    private String associationDescription;

    @NotNull(message = "Track number is required")
    @Min(value = 1, message = "Track number must be 1 or 2")
    @Max(value = 2, message = "Track number must be 1 or 2")
    private Integer trackNumber; // 新增的字段  // 必须，只能是0，或1

    // Getters and Setters
}