/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import org.springframework.context.ApplicationEvent;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public class CameraPlateAssociationDeleteEvent extends ApplicationEvent {
    private CameraPlateAssociation association;

    public CameraPlateAssociationDeleteEvent(Object source, CameraPlateAssociation association) {
        super(source);
        this.association = association;
    }

    public CameraPlateAssociation getAssociation() {
        return association;
    }
}
