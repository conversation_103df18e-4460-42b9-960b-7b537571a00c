/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.thing.ThingInterface;
import com.hihonor.iot.edge.sdk.entity.EntityThing;
import com.hihonor.iot.plc.Util;
import com.hihonor.iot.plc.m2m.M2MService;
import com.hihonor.iot.plc.m2m.mode.HeartbeatRequest;
import com.hihonor.iot.plc.m2m.mode.M2MResponse;
import com.hihonor.iot.plc.thing.ThingManagement;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Component
@Slf4j
public class HeartbeatTask {
    @Autowired
    ThingManagement thingManagement;
    @Autowired
    M2MService m2MService;

    /**
     * 每隔1分钟执行一次
     */
   // @CustomScheduled(fixedRate = 60000)  // 设置定时任务的执行间隔为5000ms
   // @MasterOnly
    public void performHeartbeatCheck() {
        for (EntityThing entityThing : thingManagement.getEntityThings()) {
            ThingInterface plc = (ThingInterface) entityThing;
            boolean isOnline = plc.getOnlineStatus();
            if (isOnline) {
                HeartbeatRequest heartbeatRequest = new HeartbeatRequest();
                heartbeatRequest.setIotId(entityThing.getName());
                heartbeatRequest.setHeartbeatSpan("60");
                heartbeatRequest.setTimestamp(Util.getcurrentTime());
                M2MResponse m2MResponse = m2MService.sendHeartbeat(heartbeatRequest);
                log.info("{}:heartbeatResponse:{}", entityThing.getName(),m2MResponse.toString());
            }
            log.info("{}:isOnline:{}",entityThing.getName(),isOnline);
        }
        // 在这里编写心跳检测的逻辑
        log.info("Heartbeat check...");
    }
}
