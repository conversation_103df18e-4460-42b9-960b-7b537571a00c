/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera;

import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hihonor.iot.plc.cachingmachine.controller.ApiResponse;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@RestController
@RequestMapping("/plc/camera")
public class CameraController {
    @Autowired
    private  CameraManager cameraManager;


    @PostMapping("/readBarcodes")
    public ApiResponse<Map<String, String>> readBarcodes(@RequestBody @Valid  BarcodeReadRequest request) {
        try {
            Map<String, String> barcodes = cameraManager.readAllBarcodes(request.getDeviceNames());
            ApiResponse<Map<String, String>> response = new ApiResponse<>(true, "条码读取成功", barcodes);
            response.setLength(barcodes.size());
            return response;
        } catch (Exception e) {
            ApiResponse<Map<String, String>> response = new ApiResponse<>(false, "条码读取失败: " + e.getMessage(), null);
            return response;
        }
    }

}
