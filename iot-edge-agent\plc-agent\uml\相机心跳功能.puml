@startuml
actor "Scheduler" as S
participant "HeartbeatService" as HS
participant "Camera" as C
participant "Database" as DB
participant "M2MService" as M2M

S -> HS : 触发心跳检测 (每2分钟)
activate HS

HS -> C : PING检测
alt PING成功
    C --> HS : PING响应
    HS -> HS : 检查端口占用
    alt 端口未被占用
        HS -> DB : 报告设备在线状态
        HS -> M2M : 发送心跳信息
        alt 心跳发送成功
            M2M --> HS : 返回成功
            HS -> HS : 记录心跳成功日志
        else 心跳发送失败
            M2M --> HS : 返回失败
            HS -> HS : 记录心跳失败日志
        end
    else 端口被占用
        HS -> HS : 设置连接状态为离线
    end
else PING失败
    C --> HS : 无响应
    HS -> HS : 设置连接状态为离线
end

HS -> HS : 获取真实连接状态
alt 设备未连接
    HS -> HS : 记录错误日志
    HS -> C : 尝试重新连接
end

deactivate HS
@enduml