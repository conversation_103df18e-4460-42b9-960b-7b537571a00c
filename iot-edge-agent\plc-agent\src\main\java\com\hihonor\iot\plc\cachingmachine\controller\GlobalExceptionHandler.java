/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    /**
     * Validation error handler
     *
     * @param ex xx
     * @return ResponseEntity
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<String>> handleValidationException(MethodArgumentNotValidException ex) {
        ApiResponse<String> response = new ApiResponse<>();
        response.setSuccess(false);  // 设置成功标志为 false
        response.setMessage("Validation error: " + ex.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        response.setData(null);

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
}