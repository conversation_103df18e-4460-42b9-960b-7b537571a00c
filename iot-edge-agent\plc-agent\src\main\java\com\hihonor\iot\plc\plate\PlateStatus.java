/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
public class PlateStatus {
    private String resourceId;
    private List<TrackStatus> trackStatuses = new ArrayList<>();
    private LocalDateTime timestamp = LocalDateTime.now();

    @Override
    public boolean equals(Object o) {
        if (this == o) return true; // 如果是同一个对象
        if (!(o instanceof PlateStatus)) return false; // 如果不是同一类型

        PlateStatus that = (PlateStatus) o; // 类型转换

        // 比较 resourceId
        if (!Objects.equals(resourceId, that.resourceId)) return false;

        // 比较 trackStatuses
        return Objects.equals(trackStatuses, that.trackStatuses);
    }

    @Override
    public int hashCode() {
        return Objects.hash(resourceId, trackStatuses);
    }

    @Data
    public static class TrackStatus {
        private String trackName;
        private Map<String, String> attributes;

        public TrackStatus(String trackName, Map<String, String> attributes) {
            this.trackName = trackName;
            this.attributes = attributes;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof TrackStatus)) return false;

            TrackStatus that = (TrackStatus) o;

            // 比较 trackName 和 attributes
            return Objects.equals(trackName, that.trackName) &&
                    Objects.equals(attributes, that.attributes); // 这里会比较 Map 的内容
        }

        @Override
        public int hashCode() {
            return Objects.hash(trackName, attributes);
        }
    }
}