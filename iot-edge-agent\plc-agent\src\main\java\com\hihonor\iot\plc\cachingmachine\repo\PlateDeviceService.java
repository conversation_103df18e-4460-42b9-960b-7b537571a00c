/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.repo;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hihonor.iot.plc.boot.MasterOnly;

import java.util.concurrent.TimeUnit;


/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Component
public class PlateDeviceService {
    @Autowired
    private PlateDeviceMapper plateDeviceMapper;
    private static final int DAYS_TO_KEEP = 7;

    /**
     * 插入数据
     *
     * @param plateDevice 数据
     */
    @MasterOnly
    public void insert(PlateDevice plateDevice) {
        plateDeviceMapper.insert(plateDevice);
    }

    /**
     * 根据设备ID查询
     *
     * @param deviceId 设备ID
     * @return 设备ID对应的数据
     */
    public List<PlateDevice> findByDeviceId(String deviceId) {
        QueryWrapper<PlateDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", deviceId);
        return plateDeviceMapper.selectList(queryWrapper);
    }

    /**
     * 根据设备ID和时间戳范围查询
     *
     * @param deviceId       设备ID
     * @param startTimestamp 开始时间戳
     * @param endTimestamp   结束时间戳
     * @return 设备ID对应的数据
     */
    public List<PlateDevice> findByDeviceIdAndTimestampRange(String deviceId, Timestamp startTimestamp, Timestamp endTimestamp) {
        QueryWrapper<PlateDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", deviceId).between("timestamp", startTimestamp, endTimestamp);
        return plateDeviceMapper.selectList(queryWrapper);
    }

    /**
     * 删除旧记录
     */
 //   @CustomScheduled(cron = "0 0 0 * * ?") // 每天凌晨执行
    @MasterOnly
    public void deleteOldRecords() {
        // 计算阈值日期
        Date thresholdDate = Date.from(Instant.now().minus(DAYS_TO_KEEP, ChronoUnit.DAYS));

        // 删除旧记录
        int deletedRecords = plateDeviceMapper.deleteOldRecords(thresholdDate);


    }

    /**
     * 删除旧记录
     */
    public void deleteRecordsOlderThanOneHour() {
        // 计算阈值日期：当前时间减去1小时
        Date thresholdDate = new Date(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1));
        // 删除旧记录
        int deletedRecords = plateDeviceMapper.deleteOldRecords(thresholdDate);

    }
}
