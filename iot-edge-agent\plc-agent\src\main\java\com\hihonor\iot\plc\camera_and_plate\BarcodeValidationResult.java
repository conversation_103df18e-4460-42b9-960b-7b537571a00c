/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Data
@AllArgsConstructor
public class BarcodeValidationResult {
    private boolean isValid;
    private String description;


    public boolean isPlateInstruction() {
        if (description == null || description.length() != 7) {
            return false;
        }

        if (description.charAt(0) != 'D') {
            return false;
        }

        for (int i = 2; i < 7; i++) {
            if (!Character.isDigit(description.charAt(i))) {
                return false;
            }
        }

        return true;
    }
}
