/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.entity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.cachingmachine.AbstractPLCEntity;
import com.hihonor.iot.plc.thing.ThingTemplate;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Slf4j
@Component
@ThingTemplate(value = "PLCTemplate", tag = "plc:Laminator1127")
@Scope("prototype")
public class Laminator extends AbstractPLCEntity{
    private List<String> propertyNames = null;

    @Override
    protected List<String> getAllPropertyNames() {
        return propertyNames;
    }

    @Override
    protected void initPropertyNames() {
        propertyNames = new ArrayList<>();
        propertyNames.add("Siemens_Laminator-B5-3F_1_BagExpZ_PosTimeout_Alarm");
        propertyNames.add("Siemens_Laminator-B5-3F_2_BagExpZ_ResetTimeout_Alarm");
        propertyNames.add("Siemens_Laminator-B5-3F_3_SlaveSealing_Z_CommunicationException");
        log.info("propertyNames:{},ThingName :{}", propertyNames, getName());

    }

    @Override
    public Map<String, String> processMap(Map<String, String> input) {
        Map<String, String> processedMap = new HashMap<>();
        String prefix = "Siemens_" + getName() + "_";

        for (Map.Entry<String, String> entry : input.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            try {
                // 如果key以特定的前缀开始，则移除它
                if (key.startsWith(prefix)) {
                    key = key.substring(prefix.length());
                }
                processedMap.put(key, value);
            } catch (Exception e) {
                // 记录任何异常，并继续处理其他的条目s
                log.error("Error processing map entry: key={}, value={}", key, value, e);
            }
        }

        return processedMap;
    }

    @Override
    public void checkAndSendStatusChange(Map<String, String> oldMap, Map<String, String> newMap) {


    }

    /**
     * 检查并发送高级告警
     *
     * @param oldMap oldMap
     * @param newMap newMap
     */
    @Override
    public void checkAndSendAdvancedAlarm(Map<String, String> oldMap, Map<String, String> newMap) {
        return;
    }

    @Override
    protected String getFullName() {
        return "Siemens_" + getName() + "_";
    }
}
