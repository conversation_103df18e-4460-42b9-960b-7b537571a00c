/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.hihonor.iot.plc.PlcApplication;
import com.hihonor.iot.plc.trafficswitch.service.ClusterNodeService;

import lombok.extern.slf4j.Slf4j;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@SpringBootTest(classes = PlcApplication.class)
@Slf4j
public class MyIntegrationTest {

    // @Autowired
    // PlcDeviceService plcDeviceMapper;
    //
    // @Autowired
    // CameraPlateAssociationMapper mapper;
    //
    //
    // @Test
    // public void exampleTest() {
    //// Page<PlcDevice> page = new Page<>(1, 10);
    //// log.info("{}", plcDeviceMapper.searchDevices(page, null, "SMT", "tag1",
    // false).getRecords().toString());
    //// Map<String,Boolean> results =
    // plcDeviceMapper.deleteDevicesByNames(List.of("B5-6F-SMT14","B5-6F-SMT15","d33f"));
    //// log.info("{}",results.toString());
    // // log.info( mapper.getUnboundPlateNamesByTrackAndName(1,"").toString());
    //
    // }
    //
    //
    //
    //
    // // @Test
    //
    // public void testSaveOrUpdateDevices() {
    // // 准备测试数据
    //// List<PlcDevice> devices = new ArrayList<>();
    //// devices.add(new PlcDevice(null, "Device1", "192.168.1.1", 8080, false,
    // null, "Type1", "Tag1", "New device 1"));
    //// devices.add(new PlcDevice(null, "Device2", "192.168.1.2", 8081, false,
    // null, "Type2", "Tag2", "New device 2"));
    //// devices.add(new PlcDevice(null, "Device3", "192.168.1.3", 8082, false,
    // null, "Type3", "Tag3", "New device 3"));
    ////
    //// // 调用批量新增或更新方法
    //// Map<String, Boolean> results =
    // plcDeviceMapper.saveOrUpdateDevices(devices);
    //// log.info("{}",results.toString());
    ////
    //// // 验证结果
    //// assertNotNull(results);
    //// assertEquals(3, results.size());
    //// assertTrue(results.get("Device1"));
    //// assertTrue(results.get("Device2"));
    //// assertTrue(results.get("Device3"));
    //
    // // plcDeviceMapper.reportDeviceOnline("Device1");
    // // plcDeviceMapper.updateConnectionStatusByLastCommunication(1);
    //
    // // 验证数据库中的数据状态
    //// devices.forEach(device -> {
    //// PlcDevice found =
    // plcDeviceMapper.findByDeviceName(device.getPlcDeviceName());
    //// assertNotNull(found);
    //// assertEquals(device.getIpAddress(), found.getIpAddress());
    //// assertEquals(device.getPort(), found.getPort());
    //// assertEquals(device.getConnectionStatus(), found.getConnectionStatus());
    //// });
    //
    // // boolean res=
    // barcodeService.validateBarcode(ScanRequest.createRequest("027606XX46500004"));
    //
    // // log.info("{}",res);
    // }

    @Autowired
    ClusterNodeService clusterNodeService;

//    @Test
//    public void testClusterNodeService() {
//        List<NodeInfo> nodeInfos = clusterNodeService.getClusterNodes();
//        log.info("{}", nodeInfos.toString());
//
//    }
//
//    @Test
//    public void testClusterNodeService2() {
//        boolean res = clusterNodeService.setPrimaryNodeAndUpdateWeights("*************", 8194);
//        log.info("{}", res);
//        List<NodeInfo> nodeInfos = clusterNodeService.getClusterNodes();
//        log.info("{}", nodeInfos.toString());
//    }
}
