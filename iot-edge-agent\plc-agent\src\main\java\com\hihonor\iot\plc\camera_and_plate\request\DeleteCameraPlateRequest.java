/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate.request;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
public class DeleteCameraPlateRequest {
    @NotEmpty(message = "相机设备名列表不能为空")
    private List<String> cameraDeviceNames;

    // Getters and setters
    public List<String> getCameraDeviceNames() {
        return cameraDeviceNames;
    }

    public void setCameraDeviceNames(List<String> cameraDeviceNames) {
        this.cameraDeviceNames = cameraDeviceNames;
    }
}
