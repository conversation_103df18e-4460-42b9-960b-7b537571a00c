/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.controller.request;

import java.sql.Timestamp;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Data
public class GetHistoryValuesRequest {

    @NotBlank(message = "deviceId must not be blank")
    private String iotId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    Timestamp startTimestamp;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    Timestamp endTimestamp;
}
