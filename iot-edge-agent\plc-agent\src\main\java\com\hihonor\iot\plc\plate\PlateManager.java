/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesResponse;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesResponse;
import com.hihonor.iot.plc.plcbase.DeviceType;
import com.hihonor.iot.plc.plcbase.DeviceUpdateEvent;
import com.hihonor.iot.plc.plcbase.PlcDevice;
import com.hihonor.iot.plc.plcbase.PlcDeviceDeleteEvent;
import com.hihonor.iot.plc.plcbase.PlcDeviceService;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Slf4j
@Component
public class PlateManager {

    @Autowired
    PlcDeviceService plcDeviceService;

    private Map<String, Plate> plateMap = new ConcurrentHashMap<String, Plate>();

    // 用于并行初始化设备的线程池
    private ExecutorService deviceInitExecutor;

    // 每个设备初始化的预计最大时间（秒）
    private static final int PER_DEVICE_INIT_TIMEOUT = 2;

    // 默认最小总超时时间（秒）
    private static final int MIN_TOTAL_TIMEOUT = 30;

    // 默认最大总超时时间（秒）
    private static final int MAX_TOTAL_TIMEOUT = 300;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    TaskRegistrar taskRegistrar;

    /**
     * 初始化,从数据库加载过板台信息并创建过板台对象
     * 使用并行方式初始化多个设备，减少总初始化时间
     */
    public void init() {
        try {
            log.info("开始初始化过板台...");
            taskRegistrar.init();
            log.info("taskRegistrar初始化完成");

            // 从数据库查询所有的过板台信息
            List<PlcDevice> plcDeviceList = plcDeviceService
                    .searchDevices(new Page<>(1, 1000), DeviceType.PLATE.getValue(), null, null, null).getRecords();

            if (plcDeviceList.isEmpty()) {
                log.info("未找到过板台设备，初始化完成");
                return;
            }

            // 创建线程池，线程数量为设备数量和可用处理器数量的较小值，避免创建过多线程
            int threadCount = Math.min(plcDeviceList.size(), Runtime.getRuntime().availableProcessors());
            deviceInitExecutor = Executors.newFixedThreadPool(threadCount);
            log.info("创建设备初始化线程池，线程数: {}", threadCount);

            // 并行初始化所有设备
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (PlcDevice info : plcDeviceList) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 创建过板台对象
                        Plate plate = applicationContext.getBean(Plate.class);
                        plateMap.put(info.getPlcDeviceName(), plate);
                        // 初始化过板台对象
                        plate.init(info.getIpAddress(), info.getPort(), info.getPlcDeviceName(), info.getPlcTag(),
                                plate);
                        // 将过板台对象放入Map中
                        log.info("创建过板台: {} - {}", info.getPlcDeviceName(), info.getIpAddress());
                    } catch (Exception e) {
                        log.error("初始化过板台[{}]失败: {}", info.getPlcDeviceName(), e.getMessage(), e);
                    }
                }, deviceInitExecutor);

                futures.add(future);
            }

            // 根据设备数量计算合理的超时时间，确保每个设备都有足够的初始化时间
            // 计算公式：设备数量 * 每个设备的预计超时时间，但不超过最大超时时间
            int calculatedTimeout = Math.min(
                    Math.max(plcDeviceList.size() * PER_DEVICE_INIT_TIMEOUT, MIN_TOTAL_TIMEOUT),
                    MAX_TOTAL_TIMEOUT);

            log.info("设置设备初始化总超时时间: {}秒，设备数量: {}", calculatedTimeout, plcDeviceList.size());

            // 等待所有设备初始化完成或超时
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(calculatedTimeout, TimeUnit.SECONDS); // 使用动态计算的超时时间
                log.info("所有过板台设备初始化完成，共 {} 个设备", plateMap.size());
            } catch (Exception e) {
                // 统计已完成的设备数量
                long completedCount = futures.stream()
                        .filter(CompletableFuture::isDone)
                        .count();

                log.warn("部分过板台设备初始化超时或失败，已完成 {}/{}，已初始化并放入Map的设备 {} 个",
                        completedCount, futures.size(), plateMap.size());
            }

            // 关闭线程池
            shutdownExecutor();

        } catch (Exception ex) {
            log.error("初始化过板台管理器失败: {}", ex.getMessage(), ex);
            // 确保线程池被关闭
            shutdownExecutor();
        }
    }

    /**
     * 关闭设备初始化线程池
     */
    private void shutdownExecutor() {
        if (deviceInitExecutor != null && !deviceInitExecutor.isShutdown()) {
            try {
                deviceInitExecutor.shutdown();
                if (!deviceInitExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    deviceInitExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                deviceInitExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 异步初始化方法，立即返回并在后台初始化设备
     * 适用于需要快速启动应用的场景
     * 
     * @return CompletableFuture<Void> 初始化完成的Future，可用于检查初始化状态
     */
    public CompletableFuture<Void> initAsync() {
        return CompletableFuture.runAsync(() -> {
            log.info("开始异步初始化过板台...");
            try {
                init(); // 调用标准初始化方法
                log.info("异步初始化过板台完成");
            } catch (Exception e) {
                log.error("异步初始化过板台失败: {}", e.getMessage(), e);
                throw e;
            }
        });
    }

    /**
     * 批量读取过板台属性值
     *
     * @param request 批量读取过板台属性请求对象
     * @return 批量读取过板台属性响应对象
     */
    public BatchReadTransferBoardPropertiesResponse batchReadPlateProperties(
            BatchReadTransferBoardPropertiesRequest request) {
        // 从请求对象中获取要读取的过板台属性列表
        List<BatchReadTransferBoardPropertiesRequest.TransferBoardProperty> transferBoardProperties = request
                .getTransferBoardProperties();

        // 创建批量读取过板台属性响应对象
        BatchReadTransferBoardPropertiesResponse response = new BatchReadTransferBoardPropertiesResponse();
        // 创建一个列表,用于存储每个过板台属性的读取结果
        List<BatchReadTransferBoardPropertiesResponse.TransferBoardPropertyResult> results = new ArrayList<>();

        // 遍历要读取的过板台属性列表
        for (BatchReadTransferBoardPropertiesRequest.TransferBoardProperty transferBoardProperty : transferBoardProperties) {
            // 获取过板台名称
            String name = transferBoardProperty.getName();
            // 获取要读取的属性名称列表
            List<String> propertyNames = transferBoardProperty.getPropertyNames();

            // 从过板台映射中获取对应名称的过板台对象
            Plate transferBoard = plateMap.get(name);
            if (transferBoard != null) {
                // 如果过板台存在,创建一个映射用于存储属性名称和对应的值
                Map<String, String> propertyValues = new HashMap<>();
                // 遍历要读取的属性名称列表
                for (String propertyName : propertyNames) {
                    // 调用过板台对象的方法,根据属性名称读取属性值
                    String propertyValue = transferBoard.readValuesByPointName(propertyName);
                    // 将属性名称和对应的值存入映射
                    propertyValues.put(propertyName, propertyValue);
                }
                // 创建一个过板台属性读取结果对象,包含过板台名称、读取成功标志、成功消息和属性值映射,并将其添加到结果列表中
                results.add(new BatchReadTransferBoardPropertiesResponse.TransferBoardPropertyResult(name, true,
                        "属性读取成功", propertyValues));
            } else {
                // 如果过板台不存在,创建一个过板台属性读取结果对象,包含过板台名称、读取失败标志和失败消息,并将其添加到结果列表中
                results.add(new BatchReadTransferBoardPropertiesResponse.TransferBoardPropertyResult(name, false,
                        "过板台不存在", null));
            }
        }

        // 将过板台属性读取结果列表设置到响应对象中
        response.setTransferBoardPropertyResults(results);
        // 返回批量读取过板台属性响应对象
        return response;
    }

    /**
     * 批量写入过板台属性值
     *
     * @param request 批量写入过板台属性请求对象
     * @return 批量写入过板台属性响应对象
     */
    public BatchWriteTransferBoardPropertiesResponse batchWriteTransferBoardProperties(
            BatchWriteTransferBoardPropertiesRequest request) {
        // 从请求对象中获取要写入的过板台属性列表
        List<BatchWriteTransferBoardPropertiesRequest.TransferBoardWriteProperty> writeProperties = request
                .getTransferBoardWriteProperties();
        // 创建批量写入过板台属性响应对象
        BatchWriteTransferBoardPropertiesResponse response = new BatchWriteTransferBoardPropertiesResponse();
        // 创建一个列表,用于存储每个过板台属性的写入结果
        List<BatchWriteTransferBoardPropertiesResponse.TransferBoardWriteResult> results = new ArrayList<>();

        // 遍历要写入的过板台属性列表
        for (BatchWriteTransferBoardPropertiesRequest.TransferBoardWriteProperty writeProperty : writeProperties) {
            // 获取过板台名称
            String name = writeProperty.getName();
            // 获取要写入的属性映射
            Map<String, String> propertiesToWrite = writeProperty.getPropertiesToWrite();
            // 从过板台映射中获取对应名称的过板台对象
            Plate transferBoard = plateMap.get(name);
            // 创建一个标志,用于表示所有属性是否都写入成功
            boolean overallSuccess = true;
            // 创建一个StringBuilder,用于构建写入结果的消息
            StringBuilder messageBuilder = new StringBuilder();

            if (transferBoard != null) {
                // 如果过板台存在,遍历要写入的属性映射
                for (Map.Entry<String, String> entry : propertiesToWrite.entrySet()) {
                    // 获取属性名称和对应的值
                    String pointName = entry.getKey();
                    String value = entry.getValue();
                    // 调用过板台对象的方法,根据属性名称写入属性值
                    boolean success = transferBoard.writeValueByPointName(pointName, value);
                    // 更新总体写入成功标志
                    overallSuccess &= success;
                    // 将每个属性的写入结果追加到消息中
                    messageBuilder
                            .append(String.format("Point: %s, Value: %s, Success: %s; ", pointName, value, success));
                }
                // 创建一个过板台属性写入结果对象,包含过板台名称、总体写入成功标志和写入结果消息,并将其添加到结果列表中
                results.add(new BatchWriteTransferBoardPropertiesResponse.TransferBoardWriteResult(name, overallSuccess,
                        messageBuilder.toString()));
            } else {
                // 如果过板台不存在,记录警告日志
                log.warn("TransferBoard not found: {}", name);
                // 创建一个过板台属性写入结果对象,包含过板台名称、写入失败标志和失败消息,并将其添加到结果列表中
                results.add(new BatchWriteTransferBoardPropertiesResponse.TransferBoardWriteResult(name, false,
                        "TransferBoard not found"));
            }
        }

        // 将过板台属性写入结果列表设置到响应对象中
        response.setTransferBoardWriteResults(results);
        // 返回批量写入过板台属性响应对象
        return response;
    }

    public boolean sendBoardingStatus(String deviceNo, String operate, String trackNo, String commmand) {
        if (plateMap.get(deviceNo) != null) {
            if (operate.equals("1"))
                return plateMap.get(deviceNo).sendTrackSignal(true, Integer.parseInt(trackNo), commmand);
            else
                return plateMap.get(deviceNo).sendTrackSignal(false, Integer.parseInt(trackNo), commmand);
        }
        return false;
    }

    /**
     * 根据过板台名称获取过板台对象
     *
     * @param name 过板台名称
     * @return 过板台对象
     */
    public Plate getPlate(String name) {
        return plateMap.get(name);
    }

    public void addPlateBoard(PlcDevice plateDevice) {
        // 根据过板台名称判断是否已存在
        Plate existingTransferBoard = plateMap.get(plateDevice.getPlcDeviceName());
        if (existingTransferBoard != null) {
            // 如果已存在,判断IP和端口是否相等
            if (existingTransferBoard.getIp().equals(plateDevice.getIpAddress()) &&
                    existingTransferBoard.getPort() == plateDevice.getPort()) {
                // IP和端口相等,直接返回
                log.info("过板台已存在且配置未发生变化: {}", plateDevice.getPlcDeviceName());
                return;
            } else {
                // 重新初始化现有实例
                existingTransferBoard.reInit(plateDevice.getIpAddress(), plateDevice.getPort(),
                        plateDevice.getPlcDeviceName());
                log.info("过板台已重新初始化: {}", plateDevice.getPlcDeviceName());
                return;
            }
        }

        // 过板台不存在，使用异步方式初始化
        CompletableFuture.runAsync(() -> {
            try {
                // 创建过板台对象并初始化
                Plate plate = applicationContext.getBean(Plate.class);
                plate.init(plateDevice.getIpAddress(), plateDevice.getPort(), plateDevice.getPlcDeviceName(),
                        plateDevice.getPlcTag(), plate);
                // 将过板台对象放入Map中
                plateMap.put(plateDevice.getPlcDeviceName(), plate);
                log.info("过板台已创建并初始化: {}", plate.getThingName());
            } catch (Exception e) {
                log.error("创建并初始化过板台[{}]失败: {}", plateDevice.getPlcDeviceName(), e.getMessage(), e);
            }
        });
    }

    @EventListener
    public void onPlcDeviceUpdateEvent(DeviceUpdateEvent event) {
        PlcDevice device = event.getDevice();
        if (!device.getPlcDeviceType().equals("plate")) {
            return;
        }
        // 调用管理器的addOrUpdatePlcDevice方法来处理设备更新
        addPlateBoard(device);
    }

    @EventListener
    public void onDeviceDeletedEvent(PlcDeviceDeleteEvent event) {
        PlcDevice device = event.getDevice();
        if (!device.getPlcDeviceType().equals("plate")) {
            return;
        }

        // 处理设备删除事件，例如记录日志、通知其他系统等
        removePlate(device.getPlcDeviceName());
        log.info("Device deleted: " + device.getPlcDeviceName());

        // 实际的业务逻辑...
    }

    /**
     * 移除过板台
     *
     * @param name 要移除的过板台名称
     */
    public void removePlate(String name) {
        // 从Map中移除过板台
        Plate removedTransferBoard = plateMap.remove(name);
        if (removedTransferBoard != null) {
            // 如果成功移除,则同时从数据库中删除相应的过板台信息

            log.info("过板台已移除,数据库信息已删除: {}", name);
            removedTransferBoard.destroy();

        } else {
            log.warn("要移除的过板台不存在: {}", name);
        }
    }

    /**
     * 批量删除过板台
     *
     * @param names 要删除的过板台名称列表
     */
    public void batchRemoveTransferBoard(List<String> names) {
        for (String name : names) {
            removePlate(name);
        }
    }

    /**
     * 获取所有的过板台对象
     *
     * @return 过板台对象列表
     */
    public List<Plate> getAllPlates() {
        List<Plate> plates = new ArrayList<>();
        for (Plate plate : plateMap.values()) {
            plates.add(plate);
        }
        return plates;
    }

    /**
     * 获取所有已连接的过板台设备
     * 
     * @return 已连接的过板台设备列表
     */
    public List<Plate> getConnectedPlates() {
        return plateMap.values().stream()
                .filter(Plate::getConnected)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有未连接的过板台设备
     * 
     * @return 未连接的过板台设备列表
     */
    public List<Plate> getDisconnectedPlates() {
        return plateMap.values().stream()
                .filter(plate -> !plate.getConnected())
                .collect(Collectors.toList());
    }

    /**
     * 检查指定名称的过板台是否已连接
     * 
     * @param plateName 过板台名称
     * @return 如果过板台存在且已连接则返回true，否则返回false
     */
    public boolean isPlateConnected(String plateName) {
        Plate plate = plateMap.get(plateName);
        return plate != null && plate.getConnected();
    }

    /**
     * 发送writeRTypePointValue命令到PLC
     *
     * @param plateName 过板台名称
     * @param address   地址
     * @param value     要写入的值
     * @return 是否发送成功
     */
    public boolean writePointValue(String plateName, String address, String... value) {
        Plate plate = plateMap.get(plateName);
        if (plate == null) {
            log.error("plateName:{} not found", plateName);
            return false;
        }

        return plate.writePointValue(address, value);
    }

    /**
     * 读取指定地址范围的数据
     *
     * @param plateName    过板台名称
     * @param startAddress 起始地址
     * @param endAddress   结束地址
     * @return 读取到的数据，如果读取失败则返回空字符串
     */
    public String readDataFromPlc(String plateName, String startAddress, String endAddress) {
        Plate plate = plateMap.get(plateName);
        if (plate == null) {
            log.error("plateName:{} not found", plateName);
            return "";
        }

        return plate.readDataFromPlc(startAddress, endAddress);
    }

    /**
     * 重新连接所有断开连接的过板台设备
     * 
     * @return 成功重连的设备数量
     */
    public int reconnectDisconnectedPlates() {
        List<Plate> disconnectedPlates = getDisconnectedPlates();
        if (disconnectedPlates.isEmpty()) {
            log.info("没有需要重连的过板台设备");
            return 0;
        }

        log.info("开始重连断开的过板台设备，共 {} 个", disconnectedPlates.size());

        // 创建线程池并行重连设备
        ExecutorService reconnectExecutor = Executors.newFixedThreadPool(
                Math.min(disconnectedPlates.size(), Runtime.getRuntime().availableProcessors()));

        try {
            List<CompletableFuture<Boolean>> futures = new ArrayList<>();

            for (Plate plate : disconnectedPlates) {
                CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("尝试重连过板台: {} - {}", plate.getThingName(), plate.getIp());
                        plate.reInit(plate.getIp(), plate.getPort(), plate.getThingName());
                        boolean connected = plate.getConnected();
                        if (connected) {
                            log.info("过板台重连成功: {}", plate.getThingName());
                        } else {
                            log.warn("过板台重连失败: {}", plate.getThingName());
                        }
                        return connected;
                    } catch (Exception e) {
                        log.error("过板台重连异常: {} - {}", plate.getThingName(), e.getMessage(), e);
                        return false;
                    }
                }, reconnectExecutor);

                futures.add(future);
            }

            // 等待所有重连操作完成并统计成功数量
            int successCount = 0;
            for (CompletableFuture<Boolean> future : futures) {
                try {
                    if (future.get(10, TimeUnit.SECONDS)) { // 设置10秒超时
                        successCount++;
                    }
                } catch (Exception e) {
                    // 忽略单个设备的超时异常
                }
            }

            log.info("过板台重连完成，成功: {}, 失败: {}", successCount, disconnectedPlates.size() - successCount);
            return successCount;

        } finally {
            // 关闭线程池
            try {
                reconnectExecutor.shutdown();
                if (!reconnectExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    reconnectExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                reconnectExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 初始化但不等待所有设备完成
     * 此方法适用于需要快速启动系统的场景，设备初始化在后台进行
     */
    public void initWithoutWaiting() {
        try {
            log.info("开始后台初始化过板台（不等待完成）...");
            taskRegistrar.init();
            log.info("taskRegistrar初始化完成");

            // 从数据库查询所有的过板台信息
            List<PlcDevice> plcDeviceList = plcDeviceService
                    .searchDevices(new Page<>(1, 1000), DeviceType.PLATE.getValue(), null, null, null).getRecords();

            if (plcDeviceList.isEmpty()) {
                log.info("未找到过板台设备，初始化完成");
                return;
            }

            // 创建线程池，线程数量为设备数量和可用处理器数量的较小值，避免创建过多线程
            int threadCount = Math.min(plcDeviceList.size(), Runtime.getRuntime().availableProcessors());
            deviceInitExecutor = Executors.newFixedThreadPool(threadCount);
            log.info("创建设备初始化线程池，线程数: {}", threadCount);

            // 创建守护线程监控进度
            Thread monitorThread = new Thread(() -> {
                try {
                    int totalDevices = plcDeviceList.size();
                    // 每10秒记录一次进度
                    while (!Thread.currentThread().isInterrupted() && !deviceInitExecutor.isShutdown()) {
                        int currentCount = plateMap.size();
                        log.info("过板台设备初始化进度: {}/{} ({}%)",
                                currentCount, totalDevices,
                                totalDevices > 0 ? (currentCount * 100 / totalDevices) : 0);

                        if (currentCount >= totalDevices) {
                            log.info("所有过板台设备初始化完成");
                            break;
                        }

                        Thread.sleep(10000); // 10秒检查一次
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    // 确保线程池被关闭
                    shutdownExecutor();
                }
            });
            monitorThread.setDaemon(true);
            monitorThread.setName("PlateInitMonitor");
            monitorThread.start();

            // 并行初始化所有设备
            for (PlcDevice info : plcDeviceList) {
                CompletableFuture.runAsync(() -> {
                    try {
                        // 创建过板台对象
                        Plate plate = applicationContext.getBean(Plate.class);
                        // 初始化过板台对象
                        plate.init(info.getIpAddress(), info.getPort(), info.getPlcDeviceName(), info.getPlcTag(),
                                plate);
                        // 将过板台对象放入Map中
                        plateMap.put(info.getPlcDeviceName(), plate);
                        log.info("创建过板台: {} - {}", info.getPlcDeviceName(), info.getIpAddress());
                    } catch (Exception e) {
                        log.error("初始化过板台[{}]失败: {}", info.getPlcDeviceName(), e.getMessage(), e);
                    }
                }, deviceInitExecutor);
            }

            log.info("过板台初始化任务已提交，初始化过程在后台继续进行");

        } catch (Exception ex) {
            log.error("提交过板台初始化任务失败: {}", ex.getMessage(), ex);
            // 确保线程池被关闭
            shutdownExecutor();
        }
    }

}
