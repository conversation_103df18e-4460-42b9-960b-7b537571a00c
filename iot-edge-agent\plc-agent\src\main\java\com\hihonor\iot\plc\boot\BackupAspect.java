/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Aspect
@Component
@DependsOn("clusterManager")
@Slf4j
public class BackupAspect {


    @Autowired
    ClusterManager clusterManager;

    /**
     * 在主机（Leader）上执行目标方法
     *
     * @param joinPoint joinPoint
     * @return Object
     * @throws Throwable Throwable
     */
    @Around("@annotation(com.hihonor.iot.plc.boot.BackupOnly)")
    public Object checkBackupBeforeExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        if (clusterManager.isLeader()) {
            log.warn("Current node is the Leader. Method {} will not be executed on Leader.", joinPoint.getSignature());
            return null;  // 直接返回，不执行目标方法
        }

        // 在备机（Follower）上执行目标方法
        return joinPoint.proceed();
    }
}