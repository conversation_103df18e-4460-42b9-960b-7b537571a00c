/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.deviceLog;

import java.time.LocalDateTime;
import java.util.Date;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Data
public class OperationLogQueryRequest {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private String methodName;

    @Min(value = 1, message = "Page number should be at least 1")
    private int page = 1;  // 默认页码为1

    @Min(value = 1, message = "Page size should be at least 1")
    private int size = 10; // 默认每页显示10条记录

    private String deviceName; // 新增设备名称字段
}
