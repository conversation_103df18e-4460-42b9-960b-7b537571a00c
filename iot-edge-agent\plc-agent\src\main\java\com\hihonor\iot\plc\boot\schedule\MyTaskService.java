/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot.schedule;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Component
@Slf4j
@Scope("prototype")
public class MyTaskService {

    @Autowired
    TaskRegistrar taskRegistrar;
    String name = "";


    // @PostConstruct

    /**
     * 初始化
     */
    void init() {
        taskRegistrar.registerTasksForObject(this);
        name = UUID.randomUUID().toString();
    }


// @CustomScheduled(fixedRate = 5000)

    /**
     * 每隔5秒执行一次
     */
    public void t1() {
        log.info("{}：{}  invoke", name, "t1");
        // 你的任务逻辑
    }

// @CustomScheduled(fixedRate = 5000)

    /**
     * 每隔1分钟执行一次
     */
    public void t2() {
        log.info("{}：{}  invoke", name, "t2");
        // 你的任务逻辑
    }

}