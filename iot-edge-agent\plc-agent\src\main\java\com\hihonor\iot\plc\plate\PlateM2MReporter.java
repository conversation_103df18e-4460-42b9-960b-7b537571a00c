/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.m2m.M2MService;
import com.hihonor.iot.plc.m2m.mode.StatusChangeRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Component
@Slf4j
public class PlateM2MReporter {

    @Autowired
    private M2MService m2mService;

    @Autowired
    private PlateManager plateManager;

    @Autowired
    private PlateStatusConfig plateStatusConfig;

    private ScheduledExecutorService scheduler;

    private Thread heartbeatThread;

    // 用于缓存每个板台的最新状态
    private Map<String, PlateStatus> plateStatusCache = new HashMap<>();

    @PostConstruct
    public void init() {
        startScheduler();
    }

    public void startScheduler() {
        startHeartbeatThread(); // 启动心跳报告线程

        // 启动监控线程
        new Thread(() -> {
            while (true) { // 无限循环监控
                if (heartbeatThread == null || !heartbeatThread.isAlive()) {
                    log.warn("心跳线程已停止，正在重启..."); // 记录警告日志
                    startHeartbeatThread(); // 重启心跳线程
                }
                try {
                    Thread.sleep(600000); // 每10秒检查一次
                } catch (InterruptedException e) {
                    log.error("监控线程被中断", e); // 记录错误日志
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    break; // 如果监控线程被中断，退出循环
                }
            }
        }).start();
    }

    private void startHeartbeatThread() {
        heartbeatThread = new Thread(() -> {
            while (true) { // 无限循环执行心跳报告
              //  log.info("心跳线程正在执行"); // 记录信息日志
                reportHeartbeat(); // 执行心跳报告
                try {
                    Thread.sleep(5000); // 休眠5秒
                } catch (InterruptedException e) {
                    log.error("心跳线程被中断", e); // 记录错误日志
                    // 如果接收到中断信号，则退出线程
                    Thread.currentThread().interrupt();
                    break; // 退出心跳线程
                }
            }
        });
        heartbeatThread.start(); // 启动心跳线程
        log.info("心跳线程已启动"); // 记录信息日志
    }


    public void reportHeartbeat() {
        try {
            List<Plate> plates = plateManager.getAllPlates();
           // log.info("Reporting heartbeat and status change for {} plates.,names", plates.size());
            for (Plate plate : plates) {
               // log.info("Reporting heartbeat and status change for plate: {}", plate.getThingName());
                if (plate.getConnected()) {
                    String iotId = plate.getThingName();
                    String heartbeatSpan = "60"; // 假设心跳间隔为60秒

                    m2mService.sendHeartbeat(iotId + "_1", heartbeatSpan);
//                    if (success) {
//                        log.info("Heartbeat reported successfully for plate: {}", iotId + "_1");
//                    } else {
//                        log.error("Failed to report heartbeat for plate: {}", iotId + "_1");
//                    }

                    m2mService.sendHeartbeat(iotId + "_2", heartbeatSpan);
//                    if (success) {
//                        log.info("Heartbeat reported successfully for plate: {}", iotId + "_2");
//                    } else {
//                        log.error("Failed to report heartbeat for plate: {}", iotId + "_2");
//                    }

                    m2mService.sendHeartbeat(iotId, heartbeatSpan);
//                    if (success) {
//                        log.info("Heartbeat reported successfully for plate: {}", iotId);
//                    } else {
//                        log.error("Failed to report heartbeat for plate: {}", iotId);
//                    }

                    // 获取当前板台状态
                    PlateStatus currentPlateStatus = plateStatusConfig.readDeviceAttributes(plate);

                    // 从缓存中获取旧的状态
                    PlateStatus oldPlateStatus = plateStatusCache.get(iotId);

                    // 比较当前状态与旧状态，如果不同则上报
                    if (oldPlateStatus == null || !oldPlateStatus.equals(currentPlateStatus)) {
                        List<StatusChangeRequest> request = StatusChangeRequest.createStatusChangeRequestFromPlateStatus(currentPlateStatus);
                        for (StatusChangeRequest statusChangeRequest : request) {
                            m2mService.sendStatusChange(statusChangeRequest);
                        }
                        // 更新缓存中的状态
                        plateStatusCache.put(iotId, currentPlateStatus);
                      //  log.info("State changed for plate: {}. New status reported.", iotId);
                    } else {
                     //   log.info("No state change for plate: {}. Status remains the same.", iotId);
                    }
                }
            }
        } catch (Exception e) {
           // log.error("Failed to report heartbeat or status change.", e);
        }
    }
}
//    @Scheduled(fixedRate = 60000) // 每分钟执行一次
//    public void reportStatusChange() {
//        StatusChangeRequest request = new StatusChangeRequest();
//        request.setIotId(plateManager.getIotId());
//        request.setStatus(plateManager.getPlateStatus());
//
//        M2MResponse response = m2mService.sendStatusChange(request);
//        if ("true".equalsIgnoreCase(response.getSuccess())) {
//            System.out.println("Status change reported successfully.");
//        } else {
//            System.out.println("Failed to report status change.");
//        }
//    }

