/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate.BarcodeValidatorTest;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
class ScanRequest {
    private String thingName;
    private String barcode;

    private ScanRequest(String thingName, String barcode) {
        this.thingName = thingName;
        this.barcode = barcode;
    }

    public static ScanRequest createRequest(String thingName, String barcode) {
        return new ScanRequest(thingName, barcode);
    }

    public String getThingName() {
        return thingName;
    }

    public String getBarcode() {
        return barcode;
    }
}
