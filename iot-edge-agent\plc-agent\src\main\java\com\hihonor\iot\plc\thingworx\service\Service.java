/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.thingworx.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.common.manager.EdgeManager;
import com.hihonor.iot.plc.thing.ThingManagement;
import com.hihonor.iot.plc.thingworx.ThingworxService;
import com.thingworx.communications.client.ConnectedThingClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Slf4j
@Component
public class Service {
    /**
     * thingworxService
     */
    @Autowired
    protected ThingworxService thingworxService;
    /**
     * thingManagement
     */
    @Autowired
    protected ThingManagement thingManagement;
    /**
     * edgeManager
     */
    @Autowired
    protected EdgeManager edgeManager;
    /**
     * client
     */
    @Autowired
    protected ConnectedThingClient client;


}
