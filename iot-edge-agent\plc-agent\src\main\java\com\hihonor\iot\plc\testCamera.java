/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
public class testCamera {

    private static final String HOST = "************";  // 扫描枪的IP地址
    private static final int PORT = 9004;  // 扫描枪的端口号
    private static final String LON_COMMAND = "LON2432\r";  // LON命令,以CR结尾

    public static String sendLonCommand() {
        try (SocketChannel socketChannel = SocketChannel.open()) {
            // 连接到扫描枪
            socketChannel.connect(new InetSocketAddress(HOST, PORT));

            // 发送LON命令
            ByteBuffer buffer = ByteBuffer.wrap(LON_COMMAND.getBytes(StandardCharsets.US_ASCII));
            socketChannel.write(buffer);

            // 接收扫描枪返回的条码
            buffer.clear();
            StringBuilder response = new StringBuilder();
            while (true) {
                int bytesRead = socketChannel.read(buffer);
                if (bytesRead > 0) {
                    buffer.flip();
                    byte[] data = new byte[buffer.remaining()];
                    buffer.get(data);
                    response.append(new String(data, StandardCharsets.US_ASCII));
                    if (response.toString().endsWith("\r")) {
                        break;
                    }
                    buffer.clear();
                } else if (bytesRead == -1) {
                    break;
                }
            }

            String barcode = response.toString().trim();
            if (!barcode.isEmpty()) {
                return barcode;
            } else {
                return null;
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String barcode = sendLonCommand();
        if (barcode != null) {
            System.out.println("扫描枪返回的条码: " + barcode);
        } else {
            System.out.println("未接收到扫描枪返回的条码");
        }
    }
}
