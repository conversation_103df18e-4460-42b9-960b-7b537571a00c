/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.hihonor.iot.plc.plcbase.DeviceType;
import com.hihonor.iot.plc.plcbase.DeviceUpdateEvent;
import com.hihonor.iot.plc.plcbase.PlcDevice;
import com.hihonor.iot.plc.plcbase.PlcDeviceDeleteEvent;
import com.hihonor.iot.plc.plcbase.PlcDeviceService;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Component
@Slf4j
public class CameraManager {

    @Autowired
    PlcDeviceService plcDeviceService;

    private Map<String, Camera> cameraMap = new ConcurrentHashMap<String, Camera>();

    // 用于并行初始化设备的线程池
    private ExecutorService deviceInitExecutor;

    // 每个设备初始化的预计最大时间（秒）
    private static final int PER_DEVICE_INIT_TIMEOUT = 2;

    // 默认最小总超时时间（秒）
    private static final int MIN_TOTAL_TIMEOUT = 30;

    // 默认最大总超时时间（秒）
    private static final int MAX_TOTAL_TIMEOUT = 300;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    TaskRegistrar taskRegistrar;

    public void reConnectAllCamera() {
        for (Camera camera : cameraMap.values()) {
            camera.reConnect();

        }
    }

    /**
     * 重新连接所有断开连接的相机设备
     * 
     * @return 成功重连的设备数量
     */
    public int reconnectDisconnectedCameras() {
        List<Camera> disconnectedCameras = getDisconnectedCameras();
        if (disconnectedCameras.isEmpty()) {
            log.info("没有需要重连的相机设备");
            return 0;
        }

        log.info("开始重连断开的相机设备，共 {} 个", disconnectedCameras.size());

        // 创建线程池并行重连设备
        ExecutorService reconnectExecutor = Executors.newFixedThreadPool(
                Math.min(disconnectedCameras.size(), Runtime.getRuntime().availableProcessors()));

        try {
            List<CompletableFuture<Boolean>> futures = new ArrayList<>();

            for (Camera camera : disconnectedCameras) {
                CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("尝试重连相机: {} - {}", camera.getThingName(), camera.getIp());
                        camera.reConnect();
                        boolean connected = camera.getConnected();
                        if (connected) {
                            log.info("相机重连成功: {}", camera.getThingName());
                        } else {
                            log.warn("相机重连失败: {}", camera.getThingName());
                        }
                        return connected;
                    } catch (Exception e) {
                        log.error("相机重连异常: {} - {}", camera.getThingName(), e.getMessage(), e);
                        return false;
                    }
                }, reconnectExecutor);

                futures.add(future);
            }

            // 等待所有重连操作完成并统计成功数量
            int successCount = 0;
            for (CompletableFuture<Boolean> future : futures) {
                try {
                    if (future.get(10, TimeUnit.SECONDS)) { // 设置10秒超时
                        successCount++;
                    }
                } catch (Exception e) {
                    // 忽略单个设备的超时异常
                }
            }

            log.info("相机重连完成，成功: {}, 失败: {}", successCount, disconnectedCameras.size() - successCount);
            return successCount;

        } finally {
            // 关闭线程池
            try {
                reconnectExecutor.shutdown();
                if (!reconnectExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    reconnectExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                reconnectExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 初始化,从数据库加载相机信息并创建相机对象
     * 使用并行方式初始化多个设备，减少总初始化时间
     */
    public void init() {
        try {
            log.info("开始初始化相机...");

            // 从数据库查询所有的相机信息
            List<PlcDevice> plcDeviceList = plcDeviceService
                    .searchDevices(new Page<>(1, 1000), DeviceType.CAMERA.getValue(), null, null, null).getRecords();

            if (plcDeviceList.isEmpty()) {
                log.info("未找到相机设备，初始化完成");
                return;
            }

            // 创建线程池，线程数量为设备数量和可用处理器数量的较小值，避免创建过多线程
            int threadCount = Math.min(plcDeviceList.size(), Runtime.getRuntime().availableProcessors());
            deviceInitExecutor = Executors.newFixedThreadPool(threadCount);
            log.info("创建设备初始化线程池，线程数: {}", threadCount);

            // 并行初始化所有设备
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (PlcDevice info : plcDeviceList) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 创建相机对象
                        Camera camera = applicationContext.getBean(Camera.class);
                        // 初始化相机对象
                        // 将相机对象放入Map中
                        cameraMap.put(info.getPlcDeviceName(), camera);
                        camera.init(info.getIpAddress(), info.getPort(), info.getPlcDeviceName(), camera);
                        log.info("创建相机: {} - {}", info.getPlcDeviceName(), info.getIpAddress());
                    } catch (Exception e) {
                        log.error("初始化相机[{}]失败: {}", info.getPlcDeviceName(), e.getMessage(), e);
                    }
                }, deviceInitExecutor);

                futures.add(future);
            }

            // 根据设备数量计算合理的超时时间，确保每个设备都有足够的初始化时间
            // 计算公式：设备数量 * 每个设备的预计超时时间，但不超过最大超时时间
            int calculatedTimeout = Math.min(
                    Math.max(plcDeviceList.size() * PER_DEVICE_INIT_TIMEOUT, MIN_TOTAL_TIMEOUT),
                    MAX_TOTAL_TIMEOUT);

            log.info("设置设备初始化总超时时间: {}秒，设备数量: {}", calculatedTimeout, plcDeviceList.size());

            // 等待所有设备初始化完成或超时
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(calculatedTimeout, TimeUnit.SECONDS); // 使用动态计算的超时时间
                log.info("所有相机设备初始化完成，共 {} 个设备", cameraMap.size());
            } catch (Exception e) {
                // 统计已完成的设备数量
                long completedCount = futures.stream()
                        .filter(CompletableFuture::isDone)
                        .count();

                log.warn("部分相机设备初始化超时或失败，已完成 {}/{}，已初始化并放入Map的设备 {} 个",
                        completedCount, futures.size(), cameraMap.size());
            }

            // 关闭线程池
            shutdownExecutor();

            // 添加关闭钩子，确保程序退出时释放资源
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("系统关闭，清理相机资源...");
                getAllCamera().forEach(Camera::destroy);
            }));

        } catch (Exception ex) {
            log.error("初始化相机管理器失败: {}", ex.getMessage(), ex);
            // 确保线程池被关闭
            shutdownExecutor();
        }
    }

    /**
     * 初始化但不等待所有设备完成
     * 此方法适用于需要快速启动系统的场景，设备初始化在后台进行
     */
    public void initWithoutWaiting() {
        try {
            log.info("开始后台初始化相机（不等待完成）...");

            // 从数据库查询所有的相机信息
            List<PlcDevice> plcDeviceList = plcDeviceService
                    .searchDevices(new Page<>(1, 1000), DeviceType.CAMERA.getValue(), null, null, null).getRecords();

            if (plcDeviceList.isEmpty()) {
                log.info("未找到相机设备，初始化完成");
                return;
            }

            // 创建线程池，线程数量为设备数量和可用处理器数量的较小值，避免创建过多线程
            int threadCount = Math.min(plcDeviceList.size(), Runtime.getRuntime().availableProcessors());
            deviceInitExecutor = Executors.newFixedThreadPool(threadCount);
            log.info("创建设备初始化线程池，线程数: {}", threadCount);

            // 创建守护线程监控进度
            Thread monitorThread = new Thread(() -> {
                try {
                    int totalDevices = plcDeviceList.size();
                    // 每10秒记录一次进度
                    while (!Thread.currentThread().isInterrupted() && !deviceInitExecutor.isShutdown()) {
                        int currentCount = cameraMap.size();
                        log.info("相机设备初始化进度: {}/{} ({}%)",
                                currentCount, totalDevices,
                                totalDevices > 0 ? (currentCount * 100 / totalDevices) : 0);

                        if (currentCount >= totalDevices) {
                            log.info("所有相机设备初始化完成");
                            break;
                        }

                        Thread.sleep(10000); // 10秒检查一次
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    // 确保线程池被关闭
                    shutdownExecutor();
                }
            });
            monitorThread.setDaemon(true);
            monitorThread.setName("CameraInitMonitor");
            monitorThread.start();

            // 并行初始化所有设备
            for (PlcDevice info : plcDeviceList) {
                CompletableFuture.runAsync(() -> {
                    try {
                        // 创建相机对象
                        Camera camera = applicationContext.getBean(Camera.class);
                        // 初始化相机对象
                        camera.init(info.getIpAddress(), info.getPort(), info.getPlcDeviceName(), camera);
                        // 将相机对象放入Map中
                        cameraMap.put(info.getPlcDeviceName(), camera);
                        log.info("创建相机: {} - {}", info.getPlcDeviceName(), info.getIpAddress());
                    } catch (Exception e) {
                        log.error("初始化相机[{}]失败: {}", info.getPlcDeviceName(), e.getMessage(), e);
                    }
                }, deviceInitExecutor);
            }

            // 添加关闭钩子，确保程序退出时释放资源
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("系统关闭，清理相机资源...");
                getAllCamera().forEach(Camera::destroy);
            }));

            log.info("相机初始化任务已提交，初始化过程在后台继续进行");

        } catch (Exception ex) {
            log.error("提交相机初始化任务失败: {}", ex.getMessage(), ex);
            // 确保线程池被关闭
            shutdownExecutor();
        }
    }

    /**
     * 关闭设备初始化线程池
     */
    private void shutdownExecutor() {
        if (deviceInitExecutor != null && !deviceInitExecutor.isShutdown()) {
            try {
                deviceInitExecutor.shutdown();
                if (!deviceInitExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    deviceInitExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                deviceInitExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 异步初始化方法，立即返回并在后台初始化设备
     * 适用于需要快速启动应用的场景
     * 
     * @return CompletableFuture<Void> 初始化完成的Future，可用于检查初始化状态
     */
    public CompletableFuture<Void> initAsync() {
        return CompletableFuture.runAsync(() -> {
            log.info("开始异步初始化相机...");
            try {
                init(); // 调用标准初始化方法
                log.info("异步初始化相机完成");
            } catch (Exception e) {
                log.error("异步初始化相机失败: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 根据指定的设备名称列表批量读取条码，并标记不在线的设备。
     *
     * @param deviceNames 需要读取条码的设备名称列表。
     * @return 返回一个Map，包含设备名称和对应的条码或状态（如果设备不在线，则为"OFFLINE"）。
     */
    public Map<String, String> readAllBarcodes(List<String> deviceNames) {
        Map<String, String> barcodes = new HashMap<>();
        for (String name : deviceNames) {
            Camera camera = cameraMap.get(name);
            if (camera != null) {
                String barcode = camera.readBarcode();
                if (barcode != null) {
                    barcodes.put(name, barcode);
                    log.info("相机 {} 读取到条码: {}", name, barcode);
                } else {
                    barcodes.put(name, "OFFLINE");
                    log.warn("相机 {} 未读取到条码，可能不在线", name);
                }
            } else {
                barcodes.put(name, "OFFLINE");
                log.warn("相机 {} 未找到或不在线", name);
            }
        }

        return barcodes;
    }

    /**
     * 根据过板台名称获取过板台对象
     *
     * @param name 过板台名称
     * @return 过板台对象
     */
    public Camera getCamera(String name) {
        return cameraMap.get(name);
    }

    public void addCamera(PlcDevice plateDevice) {
        // 根据相机名称判断是否已存在
        Camera existingCamera = cameraMap.get(plateDevice.getPlcDeviceName());
        if (existingCamera != null) {
            // 如果已存在,判断IP和端口是否相等
            if (existingCamera.getIp().equals(plateDevice.getIpAddress()) &&
                    existingCamera.getPort() == plateDevice.getPort()) {
                // IP和端口相等,直接返回
                log.info("相机已存在且配置未发生变化: {}", plateDevice.getPlcDeviceName());
                return;
            } else {
                // 重新初始化现有实例
                existingCamera.reInit(plateDevice.getIpAddress(), plateDevice.getPort(),
                        plateDevice.getPlcDeviceName());
                log.info("相机已重新初始化: {}", plateDevice.getPlcDeviceName());
                return;
            }
        }

        // 相机不存在，使用异步方式初始化
        CompletableFuture.runAsync(() -> {
            try {
                // 创建相机对象并初始化
                Camera camera = applicationContext.getBean(Camera.class);
                camera.init(plateDevice.getIpAddress(), plateDevice.getPort(), plateDevice.getPlcDeviceName(), camera);
                // 将相机对象放入Map中
                cameraMap.put(plateDevice.getPlcDeviceName(), camera);
                log.info("相机已创建并初始化: {}", camera.getThingName());
            } catch (Exception e) {
                log.error("创建并初始化相机[{}]失败: {}", plateDevice.getPlcDeviceName(), e.getMessage(), e);
            }
        });
    }

    @EventListener
    public void onPlcDeviceUpdateEvent(DeviceUpdateEvent event) {
        PlcDevice device = event.getDevice();
        if (!device.getPlcDeviceType().equals(DeviceType.CAMERA.getValue())) {
            return;
        }
        // 调用管理器的addOrUpdatePlcDevice方法来处理设备更新
        addCamera(device);
    }

    @EventListener
    public void onDeviceDeletedEvent(PlcDeviceDeleteEvent event) {
        PlcDevice device = event.getDevice();
        if (!device.getPlcDeviceType().equals(DeviceType.CAMERA.getValue())) {
            return;
        }
        // 处理设备删除事件，例如记录日志、通知其他系统等
        removeCamera(device.getPlcDeviceName());
        log.info("设备已删除: {}", device.getPlcDeviceName());
        // 实际的业务逻辑...
    }

    /**
     * 移除相机
     *
     * @param name 要移除的相机名称
     */
    public void removeCamera(String name) {
        // 从Map中移除相机
        Camera removedCamera = cameraMap.remove(name);
        if (removedCamera != null) {
            // 如果成功移除,则同时从数据库中删除相应的相机信息
            log.info("相机已移除,数据库信息已删除: {}", name);
            removedCamera.destroy();
        } else {
            log.warn("要移除的相机不存在: {}", name);
        }
    }

    /**
     * 批量删除相机
     *
     * @param names 要删除的相机名称列表
     */
    public void batchRemoveCamera(List<String> names) {
        for (String name : names) {
            removeCamera(name);
        }
    }

    /**
     * 获取所有的相机对象
     *
     * @return 相机对象列表
     */
    public List<Camera> getAllCamera() {
        return cameraMap.values().stream().collect(Collectors.toList());
    }

    /**
     * 获取所有已连接的相机设备
     * 
     * @return 已连接的相机设备列表
     */
    public List<Camera> getConnectedCameras() {
        return cameraMap.values().stream()
                .filter(Camera::getConnected)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有未连接的相机设备
     * 
     * @return 未连接的相机设备列表
     */
    public List<Camera> getDisconnectedCameras() {
        return cameraMap.values().stream()
                .filter(camera -> !camera.getConnected())
                .collect(Collectors.toList());
    }

    /**
     * 检查指定名称的相机是否已连接
     * 
     * @param cameraName 相机名称
     * @return 如果相机存在且已连接则返回true，否则返回false
     */
    public boolean isCameraConnected(String cameraName) {
        Camera camera = cameraMap.get(cameraName);
        return camera != null && camera.getConnected();
    }
}
