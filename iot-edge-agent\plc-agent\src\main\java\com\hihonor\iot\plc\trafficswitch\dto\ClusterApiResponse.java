/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.trafficswitch.dto;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API响应的顶层结构
 */
@Data
@NoArgsConstructor
public class ClusterApiResponse {

    /**
     * 上游服务列表
     */
    private List<UpstreamNode> upstreams;

    /**
     * 总数
     */
    private int total;
}