/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.m2m.mode;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HeartbeatRequest {
    private String iotId;
    private String equipmentSn;
    private String heartbeatSpan;
    private String timestamp;
}
