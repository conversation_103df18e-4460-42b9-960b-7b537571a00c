spring:
  http:
    encoding:
      charset: UTF-8
  boot:
    admin:
      context-path: /admin
      monitor:
        threshold: 0.5
        status-interval: 30
  server:
    servlet:
      multipart:
        enabled: true
        max-file-size: 100MB
    port: 8080



    #zookeeper:
    #connection-string: *************:2181,************:8181,*************:2181
  #thingWorx:
  #  template: "PLC-MEWTOCOL-COM"

  #logging:
  #  level:
  #    org.mybatis: DEBUG
  #    com.baomidou.mybatisplus: DEBUG

  #boot:
  #    masterIp : 127.0.0.1:8091
  #    role: backup

plc:
  points:
    - name: track1State
      address: DT88
    - name: track1BoardingStatus
      address: R2000
    - name: track1BoardingOK
      address: R2001

    - name: track2State
      address: DT89
    - name: track2BoardingStatus
      address: R2100
    - name: track2BoardingOK
      address: R2101

    - name: track1Floor1StorageStatus
      address: DT32501
    - name: track1Floor1StorageTime
      address: DT32551

    - name: track2Floor1StorageTime
      address: DT32551
    - name: track1Floor1Barcode
      address: DT23000-DT23019
    - name: track2Floor1Barcode
      address: DT24000-DT24019



plate-status:
  brands:
    - brand: "永信达||NUTEK"  # Brand name in English || 分割号表示支持这2个品牌
      tracks:
        - track: "1"
          attributes:
            - attribute: "deviceStatus"
              adress:
                - "DT88"
              statusMapping:
                - status: "RUN"
                  conditions:
                    - { point: "DT88", value: "2" }  # Automatic run
                - status: "IDLE"
                  logicalOperator: "OR"
                  conditions:
                    - { point: "DT88", value: "0" }  # Idle state
                    - { point: "DT88", value: "1" }  # Manual run
                - status: "DOWN"
                  conditions:
                    - { point: "DT88", value: "3" }  # Equipment failur
        - track: "2"
          attributes:
            - attribute: "deviceStatus"
              adress:
                - "DT89"
              statusMapping:
                - status: "RUN"
                  conditions:
                    - { point: "DT89", value: "2" }
                - status: "IDLE"
                  logicalOperator: "OR"
                  conditions:
                    - { point: "DT89", value: "0" }
                    - { point: "DT89", value: "1" }
                - status: "DOWN"
                  conditions:
                    - { point: "DT89", value: "3" }
    - brand: "国昊"  # Brand name in English
      tracks:
        - track: "1"
          attributes:
            - attribute: "deviceStatus"
              adress:
                - "DT3100"
              statusMapping:
                - status: "RUN"
                  conditions:
                    - { point: "DT3100", value: "2" }  # Automatic run
                - status: "IDLE"
                  logicalOperator: "OR"
                  conditions:
                    - { point: "DT3100", value: "1" }  # Idle state
                    - { point: "DT3100", value: "4" }
                - status: "DOWN"
                  conditions:
                    - { point: "DT3100", value: "3" }  # Equipment failure
            - attribute: "controlStatus"
              adress:
                - "R2000"
              statusMapping:
                - status: "ALLOW"
                  conditions:
                    - { point: "R2000", value: "0" }
                - status: "BLOCK"
                  conditions:
                    - { point: "R2000" ,value: "1" }
        - track: "2"
          attributes:
            - attribute: "deviceStatus"
              adress:
                - "DT3101"
              statusMapping:
                - status: "RUN"
                  conditions:
                    - { point: "DT3101", value: "2" }
                - status: "IDLE"
                  conditions:
                    - { point: "DT3101", value: "1" }
                - status: "DOWN"
                  conditions:
                    - { point: "DT3101", value: "3" }
                - status: "AGING"
                  conditions:
                    - { point: "DT3101", value: "4" }
    - brand: "TEKNEK"
      tracks:
        - track: "1"
          attributes:
            - attribute: "deviceStatus"
              adress:
                - "R100"
                - "R16"
              statusMapping:
                - status: "RUN"
                  conditions:
                    - { point: "R100", value: "1" }
                    - { point: "R16", value: "0" }
                - status: "IDLE"
                  logicalOperator: "AND"
                  conditions:
                    - { point: "R100", value: "0" }
                    - { point: "R16", value: "0" }
                - status: "DOWN"
                  conditions:
                    - { point: "R16", value: "1" }
        - track: "2"
          attributes:
            - attribute: "deviceStatus"
              adress:
                - "R17"
                - "R200"
              statusMapping:
                - status: "RUN"
                  logicalOperator: "AND"
                  conditions:
                    - { point: "R200", value: "1" }
                    - { point: "R17", value: "0" }
                - status: "IDLE"
                  logicalOperator: "AND"
                  conditions:
                    - { point: "R200", value: "0" }
                    - { point: "R17", value: "0" }
                - status: "DOWN"
                  conditions:
                    - { point: "R17", value: "1" }
    - brand: "Langxing"
      tracks:
        - track: "1"
          attributes:
            - attribute: "deviceStatus"
              adress:
                - "R1668"
                - "R1663"
                - "R1669"
              statusMapping:
                - status: "RUN"
                  logicalOperator: "AND"
                  conditions:
                    - { point: "R1668",value: "1" }
                    - { point: "R1663",value: "0" }
                - status: "IDLE"
                  logicalOperator: "AND"
                  conditions:
                    - { point: "R1669",value: "1" }
                    - { point: "R1663",value: "0" }
                - status: "DOWN"
                  conditions:
                    - { point: "R1663",value: "1" }

