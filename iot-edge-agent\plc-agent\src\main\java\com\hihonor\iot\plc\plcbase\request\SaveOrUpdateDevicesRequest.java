/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase.request;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import com.hihonor.iot.plc.plcbase.PlcDevice;

import lombok.Data;

@Data
public class SaveOrUpdateDevicesRequest {
    @NotEmpty(message = "设备列表不能为空且长度必须大于0")
    @Valid // 确保列表中的每个对象都进行校验
    private List<PlcDevice> devices;

    // Getters and Setters
}
