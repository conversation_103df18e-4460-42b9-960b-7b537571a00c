-- 创建表
-- 创建表
CREATE TABLE iot_admin.plc_device (
    plc_device_id SERIAL PRIMARY KEY,
    plc_device_name VARCHAR(255) NOT NULL,
    ip_address VARCHAR(15) NOT NULL,
    port INT NOT NULL,
    connection_status BOOLEAN NOT NULL DEFAULT false,
    last_communication TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    plc_device_type VARCHAR(255) NOT NULL,
    plc_tag VARCHAR(255),
    description TEXT,
    CONSTRAINT plc_device_name_unique UNIQUE (plc_device_name)
);
INSERT INTO iot_admin.plc_device (
        plc_device_name,
        ip_address,
        port,
        connection_status,
        last_communication,
        plc_device_type,
        plc_tag,
        description
    )
VALUES (
        'A2402000115-C1',
        '***********',
        9004,
        false,
        '2024-06-24 20:46:09.548211',
        'camera',
        'aoi_camera1trace',
        NULL
    ),
    (
        'A2403002026',
        '***********',
        9094,
        false,
        '2024-06-25 10:04:08.201329',
        'plate',
        'pcb_plate',
        NULL
    ),
    (
        'A2403002026-C2',
        '***********',
        9004,
        false,
        '2024-06-25 10:04:08.321852',
        'camera',
        'pcb_camera2trace',
        NULL
    ),
    (
        'A2403002026-C1',
        '***********',
        9004,
        true,
        '2024-06-25 10:06:08.279716',
        'camera',
        'pcb_camera1trace',
        NULL
    ),
    (
        'A2402000115',
        '***********',
        9094,
        true,
        '2024-06-25 10:12:08.247613',
        'plate',
        'aoi_plate',
        NULL
    ),
    (
        'A2402000115-C2',
        '***********',
        9004,
        true,
        '2024-06-25 10:12:09.606244',
        'camera',
        'aoi_camera2trace',
        NULL
    );
-- 创建索引
CREATE INDEX idx_plc_device_type ON iot_admin.plc_device (plc_device_type);
CREATE INDEX idx_plc_device_name ON iot_admin.plc_device (plc_device_name);
CREATE INDEX idx_plc_tag ON iot_admin.plc_device (plc_tag);
CREATE INDEX idx_plc_device_type_name_tag ON iot_admin.plc_device (plc_device_type, plc_device_name, plc_tag);
INSERT INTO iot_admin.plc_device (
        plc_device_name,
        ip_address,
        port,
        connection_status,
        plc_device_type,
        plc_tag,
        description
    )
VALUES (
        'B5-3F-SMT02',
        '***********',
        9094,
        false,
        'plate',
        'tag1',
        ''
    ),
    (
        'B5-3F-SMT03',
        '***********',
        9094,
        false,
        'plate',
        'tag1',
        ''
    ),
    (
        'B5-3F-SMT04',
        '***********',
        9094,
        false,
        'plate',
        'tag1',
        ''
    ),
    (
        'B5-4F-SMT05',
        '***********',
        9094,
        false,
        'plate',
        'tag1',
        ''
    ),
    (
        'B5-4F-SMT06',
        '***********',
        9094,
        false,
        'plate',
        'tag2',
        ''
    ),
    (
        'B5-4F-SMT08',
        '***********',
        9094,
        false,
        'plate',
        'tag2',
        ''
    ),
    (
        'B5-4F-SMT12',
        '***********',
        9094,
        false,
        'plate',
        'tag2',
        ''
    ),
    (
        'B5-6F-SMT13',
        '***********',
        9094,
        false,
        'plate',
        'tag2',
        ''
    ),
    (
        'cameraDevcice',
        '************',
        9004,
        false,
        'camera',
        'tag1',
        ''
    );
-- 创建基于设备名称的相机设备和过板台设备的关联表
CREATE TABLE iot_admin.camera_plate_association_by_name (
    association_id SERIAL PRIMARY KEY,
    camera_device_name VARCHAR(255) NOT NULL,
    plate_device_name VARCHAR(255) NOT NULL,
    association_description TEXT,
    CONSTRAINT fk_camera_device_name FOREIGN KEY(camera_device_name) REFERENCES iot_admin.plc_device(plc_device_name) ON DELETE CASCADE,
    CONSTRAINT fk_plate_device_name FOREIGN KEY(plate_device_name) REFERENCES iot_admin.plc_device(plc_device_name) ON DELETE CASCADE,
    CONSTRAINT camera_plate_name_unique UNIQUE (camera_device_name, plate_device_name),
    CONSTRAINT unique_camera_device_name UNIQUE (camera_device_name)
);
INSERT INTO iot_admin.camera_plate_association_by_name (
        camera_device_name,
        plate_device_name,
        association_description,
        track_number
    )
VALUES ('A2403002026-C1', 'A2403002026', NULL, 1),
    ('A2403002026-C2', 'A2403002026', NULL, 2),
    ('A2402000115-C1', 'A2402000115', NULL, 1),
    ('A2402000115-C2', 'A2402000115', NULL, 2);
ALTER TABLE iot_admin.camera_plate_association_by_name
ADD COLUMN track_number INT;
-- 为camera_device_name列添加索引
CREATE INDEX idx_camera_device_name ON iot_admin.camera_plate_association_by_name (camera_device_name);
-- 为plate_device_name列添加索引
CREATE INDEX idx_plate_device_name ON iot_admin.camera_plate_association_by_name (plate_device_name);
-- 创建主表
CREATE TABLE iot_admin.plc_operation_log (
    id SERIAL NOT NULL,
    device_type VARCHAR(50),
    device_name VARCHAR(255),
    online_status BOOLEAN,
    method_name VARCHAR(255),
    method_params JSONB,
    user_id VARCHAR(255),
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_time INTEGER,
    return_value JSONB,
    exception_message TEXT
) PARTITION BY RANGE (operation_time);
ALTER TABLE iot_admin.plc_operation_log
ALTER COLUMN device_type DROP NOT NULL,
    ALTER COLUMN device_name DROP NOT NULL,
    ALTER COLUMN online_status DROP NOT NULL,
    ALTER COLUMN method_name DROP NOT NULL,
    ALTER COLUMN method_params DROP NOT NULL,
    ALTER COLUMN user_id DROP NOT NULL,
    ALTER COLUMN operation_time DROP NOT NULL,
    ALTER COLUMN execution_time DROP NOT NULL,
    ALTER COLUMN return_value DROP NOT NULL,
    ALTER COLUMN exception_message DROP NOT NULL;
-- 创建单独的索引
CREATE INDEX idx_operation_log_device_name ON iot_admin.plc_operation_log (device_name);
CREATE INDEX idx_operation_log_method_name ON iot_admin.plc_operation_log (method_name);
CREATE INDEX idx_operation_log_operation_time ON iot_admin.plc_operation_log (operation_time);
-- 创建联合索引
-- 覆盖设备名查询
CREATE INDEX idx_operation_log_device_name_method ON iot_admin.plc_operation_log (device_name, method_name);
-- 覆盖设备名和方法名查询
CREATE INDEX idx_operation_log_device_name_method_time ON iot_admin.plc_operation_log (device_name, method_name, operation_time);
CREATE OR REPLACE FUNCTION iot_admin.create_plc_operation_log_partition() RETURNS void AS $$
DECLARE last_partition_name text;
last_partition_date date;
new_partition_date date;
new_partition_name text;
BEGIN -- 获取最后一个分区的名称
SELECT c.relname INTO last_partition_name
FROM pg_inherits i
    JOIN pg_class c ON i.inhrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE i.inhparent = 'iot_admin.plc_operation_log'::regclass
    AND n.nspname = 'iot_admin'
ORDER BY c.relname DESC
LIMIT 1;
-- 确定新分区的日期
IF last_partition_name IS NULL THEN new_partition_date := CURRENT_DATE;
ELSE last_partition_date := to_date(substr(last_partition_name, 19), 'YYYY_MM_DD');
new_partition_date := last_partition_date + INTERVAL '1 day';
END IF;
-- 生成新分区的名称
new_partition_name := 'plc_operation_log_' || to_char(new_partition_date, 'YYYY_MM_DD');
-- 创建新分区
EXECUTE format(
    'CREATE TABLE iot_admin.%I PARTITION OF iot_admin.plc_operation_log FOR VALUES FROM (%L) TO (%L)',
    new_partition_name,
    new_partition_date,
    new_partition_date + INTERVAL '1 day'
);
-- 为新分区创建与主表相同的索引
EXECUTE format(
    'CREATE INDEX IF NOT EXISTS %I ON iot_admin.%I (device_name)',
    'idx_' || new_partition_name || '_device_name',
    new_partition_name
);
EXECUTE format(
    'CREATE INDEX IF NOT EXISTS %I ON iot_admin.%I (method_name)',
    'idx_' || new_partition_name || '_method_name',
    new_partition_name
);
EXECUTE format(
    'CREATE INDEX IF NOT EXISTS %I ON iot_admin.%I (operation_time)',
    'idx_' || new_partition_name || '_operation_time',
    new_partition_name
);
EXECUTE format(
    'CREATE INDEX IF NOT EXISTS %I ON iot_admin.%I (device_name, method_name)',
    'idx_' || new_partition_name || '_device_name_method',
    new_partition_name
);
EXECUTE format(
    'CREATE INDEX IF NOT EXISTS %I ON iot_admin.%I (device_name, method_name, operation_time)',
    'idx_' || new_partition_name || '_device_name_method_time',
    new_partition_name
);
RAISE NOTICE '分区 % 已创建',
new_partition_name;
END;
$$ LANGUAGE plpgsql;
CREATE OR REPLACE FUNCTION iot_admin.drop_plc_operation_log_partition(IN partition_date DATE) RETURNS void AS $$
DECLARE partition_name TEXT;
BEGIN partition_name := 'plc_operation_log_' || to_char(partition_date, 'YYYY_MM_DD');
-- 检查分区是否存在
IF EXISTS (
    SELECT 1
    FROM information_schema.tables
    WHERE table_schema = 'iot_admin'
        AND table_name = partition_name
) THEN -- 删除分区
EXECUTE format('DROP TABLE iot_admin.%I', partition_name);
RAISE NOTICE '分区 % 已删除',
partition_name;
ELSE RAISE NOTICE '分区 % 不存在,跳过删除',
partition_name;
END IF;
END;
$$ LANGUAGE plpgsql;
SELECT iot_admin.drop_plc_operation_log_partition('2024-05-28');
SELECT iot_admin.create_plc_operation_log_partition();
TRUNCATE TABLE iot_admin.camera_plate_association_by_name RESTART IDENTITY;
TRUNCATE TABLE iot_admin.plc_device,
iot_admin.camera_plate_association_by_name RESTART IDENTITY;
TRUNCATE TABLE iot_admin.camera_plate_association_by_name RESTART IDENTITY;
SELECT pd.*
FROM iot_admin.plc_device pd
WHERE pd.plc_device_type = 'camera'
    AND pd.plc_device_name NOT IN (
        SELECT cpa.camera_device_name
        FROM iot_admin.camera_plate_association_by_name cpa
    );
SELECT pd.plc_device_name
FROM iot_admin.plc_device pd
WHERE pd.plc_device_type = 'plate'
    AND pd.plc_device_name NOT IN (
        SELECT cpa.plate_device_name
        FROM iot_admin.camera_plate_association_by_name cpa
        WHERE cpa.track_number = 1
    );
select *
from iot_admin.plc_operation_log
where device_name = 'A2402000115-C1'
    and operation_time > ' 2024-07-04 00:00:00'