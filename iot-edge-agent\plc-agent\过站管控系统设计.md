# 系统总体设计

```mermaid
graph TD
    A[主控制系统模块] --> B(摄像头模块);
    A --> C(板台模块);
    A --> D(条码验证服务模块);
    A --> E(日志模块);
    B --> E;
    C --> E;
```

## 模块说明

### 1. 主控制系统模块 (Main Control System)

主控制系统模块是整个系统的核心，负责协调和管理各个子模块的操作。它定期检查设备状态，处理来自摄像头和板台的反馈，并根据设备的状态执行相应的指令。该模块还负责与日志模块交互，记录操作过程中的重要事件和状态变化，以便于后续分析和维护。

### 2. 摄像头模块 (Camera Module)

摄像头模块负责条码的读取和状态反馈。它与主控制系统进行通信，提供实时的条码数据和连接状态信息。该模块能够检测和报告自身的在线状态，并在接收到指令时执行相应的操作。摄像头模块还依赖日志模块记录其操作和状态变化，以便于后续的故障排查和维护。

### 3. 板台模块 (Plate Module)

板台模块负责接收来自主控制系统的指令，并执行相应的控制操作。它与主控制系统进行通信，反馈自身的状态信息。该模块能够处理允许通过、告警等信号，并根据指令执行相应的动作。此外，板台模块还依赖日志模块记录操作和状态变化，以确保系统的可追溯性和维护便利性。

### 4. 日志模块 (Logging Module)

日志模块负责记录系统的操作日志和设备状态变化。它记录主控制系统、摄像头和板台的操作记录，包括成功和失败的操作、设备的在线/离线状态等。该模块提供日志查询接口，方便用户和维护人员查看设备行为，帮助快速定位和解决系统问题。

## 子模块详细设计

### 1. 控制系统模块详细设计

#### 序列图

```mermaid
sequenceDiagram
    participant 主控制系统
    participant 摄像头
    participant 板台
    participant 条码验证服务

    主控制系统->>摄像头: 初始化连接
    主控制系统->>板台: 初始化连接

    loop 直到收到停止指令
        主控制系统->>摄像头: 检查连接状态请求
        摄像头-->>主控制系统: 返回连接状态

        alt 摄像头已连接
            主控制系统->>摄像头: 读取条码指令
            摄像头-->>主控制系统: 返回条码数据

            alt 条码数据有效
                主控制系统->>条码验证服务: 验证条码请求
                条码验证服务-->>主控制系统: 返回验证结果

                alt 验证通过
                    主控制系统->>板台: 检查连接状态请求
                    板台-->>主控制系统: 返回连接状态
                    alt 板台已连接
                        主控制系统->>板台: 发送允许通过信号(OK1)
                        主控制系统->>板台: 发送重置信号(OK0)
                    else 板台未连接
                        Note right of 主控制系统: 记录错误日志
                    end
                else 验证失败
                    主控制系统->>板台: 检查连接状态请求
                    板台-->>主控制系统: 返回连接状态
                    alt 板台已连接
                        主控制系统->>板台: 发送告警信号(1)
                        主控制系统->>板台: 发送复位信号(0)
                    else 板台未连接
                        Note right of 主控制系统: 记录错误日志
                    end
                end
            else 条码数据为NULL
                 Note right of 主控制系统: 跳过本次循环, 重新读取条码
            end
        else 摄像头未连接
            Note right of 主控制系统: 记录错误日志
        end
        Note over 主控制系统: 等待/让出CPU时间
    end

    Note over 主控制系统: 收到停止指令
    主控制系统->>摄像头: 断开连接
    主控制系统->>板台: 断开连接
```

#### 流程图

```mermaid
flowchart TD
    Start((初始化))
    InitConn["主控向摄像头和板台初始化连接"]
    LoopStart["开始循环处理"]
    CheckCamConn["检查摄像头连接状态"]
    IsCamConnected{"摄像头已连接?"}
    ReadBarcode["读取条码"]
    GetBarcodeData{"获取到条码数据?"}
    IsBarcodeNull{"条码数据为NULL?"}
    ValidateBarcode["请求条码验证"]
    IsValidationOk{"验证通过?"}
    CheckPlateConn1["检查板台连接状态"]
    IsPlateConnected1{"板台已连接?"}
    SendOK1["发送允许通过(OK1)"]
    SendOK0["发送重置(OK0)"]
    LogError1["记录错误日志"]
    CheckPlateConn2["检查板台连接状态"]
    IsPlateConnected2{"板台已连接?"}
    SendAlarm1["发送告警(1)"]
    SendReset0["发送复位(0)"]
    LogError2["记录错误日志"]
    LogError3["记录错误日志"]
    WaitCPU["等待/让出CPU时间"]
    CheckStop{"收到停止指令?"}
    StopProc["停止处理"]
    Disconnect["断开连接"]
    End((结束))

    Start --> InitConn
    InitConn --> LoopStart
    LoopStart --> CheckCamConn
    CheckCamConn --> IsCamConnected
    IsCamConnected -- Yes --> ReadBarcode
    IsCamConnected -- No --> LogError3
    ReadBarcode --> GetBarcodeData
    GetBarcodeData -- Yes --> IsBarcodeNull
    GetBarcodeData -- No --> WaitCPU
    IsBarcodeNull -- No --> ValidateBarcode
    IsBarcodeNull -- Yes --> WaitCPU
    ValidateBarcode --> IsValidationOk
    IsValidationOk -- Yes --> CheckPlateConn1
    IsValidationOk -- No --> CheckPlateConn2
    CheckPlateConn1 --> IsPlateConnected1
    IsPlateConnected1 -- Yes --> SendOK1
    IsPlateConnected1 -- No --> LogError1
    SendOK1 --> SendOK0
    SendOK0 --> WaitCPU
    LogError1 --> WaitCPU
    CheckPlateConn2 --> IsPlateConnected2
    IsPlateConnected2 -- Yes --> SendAlarm1
    IsPlateConnected2 -- No --> LogError2
    SendAlarm1 --> SendReset0
    SendReset0 --> WaitCPU
    LogError2 --> WaitCPU
    LogError3 --> WaitCPU
    WaitCPU --> CheckStop
    CheckStop -- No --> LoopStart
    CheckStop -- Yes --> StopProc
    StopProc --> Disconnect
    Disconnect --> End
```

#### 详细说明

1.  **初始化连接**
    - 主控制系统向摄像头发送初始化连接指令。
    - 主控制系统向板台发送初始化连接指令。
2.  **开始循环处理**
    - 主控制系统进入循环, 直到收到停止指令。
3.  **检查摄像头连接状态**
    - 主控制系统向摄像头发送检查连接状态的请求。
    - 摄像头返回连接状态给主控制系统。
4.  **摄像头已连接处理**
    - 如果摄像头已连接, 主控制系统向摄像头发送读取条码的指令。
    - 摄像头返回条码数据。
5.  **条码数据处理**
    - 主控制系统检查返回的条码数据。
    - **如果条码数据为 NULL**: 跳过本次循环, 重新读取条码。
    - **如果条码有效**: 主控制系统向条码验证服务发送验证条码的请求。
    - 条码验证服务返回验证结果给主控制系统。
6.  **验证结果处理**
    - 主控制系统根据验证结果执行以下操作:
      - **如果验证通过**:
        - 主控制系统检查板台连接状态。
        - **如果板台已连接**: 主控制系统向板台发送允许通过信号 (OK1)，然后发送重置信号 (OK0)。
        - **如果板台未连接**: 主控制系统记录错误日志。
      - **如果验证不通过**:
        - 主控制系统检查板台连接状态。
        - **如果板台已连接**: 主控制系统向板台发送告警信号 (1)，然后发送复位信号 (0)。
        - **如果板台未连接**: 主控制系统记录错误日志。
7.  **摄像头未连接处理**
    - 如果摄像头未连接, 主控制系统记录错误日志。
8.  **等待/让出 CPU 时间**
    - 主控制系统在每次循环结束后, 等待或让出 CPU 时间。
9.  **停止处理**
    - 当收到停止指令时:
      - 主控制系统停止处理。
      - 主控制系统断开与摄像头和板台的连接。

### 2. 摄像头模块详细设计

#### 组件图

```mermaid
flowchart TD
    subgraph 相机模块
        User["用户<br>管理相机设备<br>查询设备状态"]
        CamMgmt["相机设备管理模块<br>相机管理<br>设备移除<br>设备查询<br>条码读取<br>事件监听"]
        ScanComm["扫描设备通信模块<br>连接管理<br>命令发送与响应接收<br>心跳检测<br>条码读取<br>LON命令发送<br>连接状态检查<br>资源释放"]
        CamCtrl["相机设备控制模块<br>初始化<br>连接管理<br>条码读取<br>心跳检测<br>状态更新<br>连接状态检查<br>日志功能"]
        DB["数据库<br>存储相机配置<br>存储设备状态"]
        Logging["日志模块<br>记录操作日志<br>记录错误日志"]

        User -->|"增删查相机设备"| CamMgmt
        CamMgmt -->|"存储相机配置"| DB
        CamMgmt -->|"管理相机设备"| CamCtrl
        CamMgmt -->|"记录日志"| Logging
        CamCtrl -->|"提供通信功能"| ScanComm
        CamCtrl -->|"存储设备状态"| DB
        CamCtrl -->|"记录操作日志"| Logging
    end
```

#### 2.1 扫描设备通信模块

- **功能描述**:
  - **连接管理**：负责与扫描枪设备建立和关闭 TCP 连接，支持连接超时设置。
  - **命令发送与响应接收**：能够向扫描枪发送各种命令（如读取条码、发送心跳等），并接收相应的响应。
  - **心跳检测**：定期向扫描枪发送心跳命令，以检查设备是否在线，并确认设备的可用性。
  - **条码读取**：从扫描枪读取返回的条码数据，确保系统能够实时获取条码信息。
  - **LON 命令发送**：发送特定的 LON 命令到扫描枪并处理设备的响应。
  - **连接状态检查**：检查当前是否已成功连接到扫描枪，方便其他模块进行状态判断。
  - **资源释放**：在不再需要与设备通信时，关闭连接和释放相关资源。

#### 2.2 相机设备控制模块

- **功能描述**:
  - **初始化**：设置相机设备的 IP 地址、端口和其他相关信息，并建立与设备的连接。
  - **连接管理**：尝试连接、重新连接和关闭与相机设备的连接，确保设备始终处于可用状态。
  - **条码读取**：读取相机设备返回的条码数据，确保系统能够获取到实时的条码信息。
  - **心跳检测**：定期发送心跳命令以确认相机设备的在线状态，确保设备的可用性。
  - **状态更新**：监测相机设备的状态，并将状态信息上报到数据库和其他服务（如 M2M 服务）。
  - **连接状态检查**：获取当前的连接状态，方便其他模块进行状态判断。
  - **日志功能**：记录设备操作的日志信息，方便后期排查问题和维护。

#### 2.3 相机设备管理模块

- **功能描述**:
  - **相机管理**：提供添加新的相机设备或重新初始化现有相机设备的功能，支持设备的动态管理。
  - **设备移除**：支持单个或批量移除相机设备，确保设备管理的灵活性。
  - **设备查询**：根据名称获取特定相机设备，或获取所有相机设备的列表，方便用户进行管理。
  - **条码读取**：能够读取所有相机设备返回的条码信息，并记录相关日志。
  - **事件监听**：处理相机设备的更新和删除事件，确保设备管理的实时性和准确性。

### 3. 板台模块详细设计

#### 组件图

```mermaid
flowchart TD
    subgraph 板台模块
        User["用户<br>管理板台设备<br>查询设备状态"]
        PlateMgmt["板台设备管理模块<br>加载板台信息<br>批量读写属性<br>信号发送<br>设备管理<br>数据读写"]
        PlateNetComm["板台网络通信模块<br>初始化实例<br>连接管理<br>数据读取<br>命令发送与响应<br>超时处理<br>连接状态检查"]
        PlateCtrl["板台设备控制模块<br>初始化实例<br>连接管理<br>数据读取<br>命令发送<br>设备状态检查<br>定期状态更新<br>轨道信号管理<br>日志功能"]
        DB["数据库<br>存储板台配置<br>存储设备状态"]
        Logging["日志模块<br>记录操作日志<br>记录错误日志"]

        User -->|"控制设备"| PlateMgmt
        PlateMgmt -->|"读取和写入板台配置"| DB
        PlateMgmt -->|"依赖"| PlateCtrl
        PlateMgmt -->|"记录日志"| Logging
        PlateCtrl -->|"依赖"| PlateNetComm
        PlateCtrl -->|"存储设备状态"| DB
        PlateCtrl -->|"日志功能"| Logging
    end
```

#### 3.1 板台网络通信模块

- **功能描述**: 该模块用于与板台设备进行网络通信，使用 MEWTOCOL 协议。
  - **连接管理**: 初始化实例 (IP, Port, Client Name), 连接尝试 (带超时), 关闭连接。
  - **数据读取**: 读取 PLC 数据, 解析响应。
  - **命令发送与响应接收**: 发送命令 (支持多种地址类型), 超时处理。
  - **连接状态检查**: 检查是否连接成功。

#### 3.2 板台设备控制模块

- **功能描述**: 管理板台设备的网络通信和状态监控 (Spring 原型类)。
  - **连接管理**: 初始化实例 (IP, Port, Device Name), 连接尝试 (带超时), 关闭连接。
  - **数据读取**: 根据点位名称读取数据, 读取地址范围数据。
  - **命令发送与响应接收**: 根据点位名称写入值 (支持 R, DT 类型), 发送命令 (支持多种地址类型)。
  - **设备状态检查**: 检查是否连接成功。
  - **定期状态更新**: 定期更新设备状态并报告心跳 (数据库, M2M 服务)。
  - **轨道信号管理**: 发送轨道进板信号, 发送禁止通过信号。
  - **日志功能**: 记录操作日志。

#### 3.3 板台设备管理模块

- **功能描述**: 管理板台设备，与数据库交互，读取配置并实例化，支持动态增删改。
  - **初始化**: 从数据库加载板台信息并创建对象。
  - **批量属性读取与写入**: 批量读/写属性值。
  - **信号发送**: 发送进板信号, 发送禁止通行信号。
  - **设备管理**: 获取板台对象 (按名称), 添加/更新设备, 移除设备, 批量删除设备。
  - **数据读取和写入**: 读取指定地址范围数据, 发送写入命令。

#### 3.4 模块间关系

- **依赖关系**：板台设备控制模块依赖于板台网络通信模块来实现与 PLC 的通信。板台设备管理模块则依赖于板台设备控制模块来管理设备的状态和操作。

### 4. 日志模块详细设计

#### 类图

```mermaid
classDiagram
    class PlcOperationLog {
        +Long id
        +String deviceType
        +String deviceName
        +Boolean onlineStatus
        +String methodName
        +String methodParams
        +String userId
        +Date operationTime
        +Integer executionTime
        +String returnValue
        +String exceptionMessage
    }

    class Device {
        +String deviceName
        +String deviceType
        +Boolean onlineStatus
        +updateStatus()
    }

    class User {
        +manageLogs()
        +queryLogs(): List~PlcOperationLog~
    }

    class 日志服务 {
        +insertLog(PlcOperationLog log): void
        +queryLogs(String deviceName): List~PlcOperationLog~
        +queryLogsByUserId(String userId): List~PlcOperationLog~
        +queryLogsByDateRange(Date startDate, Date endDate): List~PlcOperationLog~
    }

    class 日志存储 {
        +save(PlcOperationLog log): void
        +findByDeviceName(String deviceName): List~PlcOperationLog~
        +findByUserId(String userId): List~PlcOperationLog~
        +findByDateRange(Date startDate, Date endDate): List~PlcOperationLog~
    }

    class 数据库 {
        +saveLog(PlcOperationLog log): void
        +retrieveLogs(String query): List~PlcOperationLog~
    }

    Device --|> 日志服务 : 提供设备信息
    User --|> 日志服务 : 管理和查询日志
    日志服务 --> 日志存储 : 使用
    日志存储 --> 数据库 : 交互
    日志服务 ..> PlcOperationLog : 创建/使用
    日志存储 ..> PlcOperationLog : 创建/使用
    数据库 ..> PlcOperationLog : 创建/使用

```

#### 4.1 组件说明

- **PlcOperationLog**: 数据类，用于封装单条操作日志的详细信息，如设备类型、名称、操作方法、参数、用户、时间、结果、异常等。
- **设备 (Device)**: 代表一个具体的设备（如摄像头、板台），提供设备的基本信息（名称、类型、在线状态）给日志服务。
- **用户 (User)**: 代表系统的使用者，可以触发日志管理操作（如查询）。
- **日志服务 (Log Service)**: 日志模块的核心业务逻辑层。负责接收来自其他模块（如设备、用户）的日志记录请求，并提供不同维度的日志查询接口（按设备名、用户 ID、日期范围）。它调用日志存储层来持久化和检索日志数据。
- **日志存储 (Log Repository)**: 数据访问层接口，定义了日志数据的持久化操作规范（保存、按设备名查找、按用户 ID 查找、按日期范围查找）。它隔离了业务逻辑和具体的数据库实现。
- **数据库 (Database)**: 具体的数据库实现层，负责将 `PlcOperationLog` 对象存入数据库，并根据查询条件从数据库中检索日志记录。

#### 4.2 交互流程

1.  **日志记录**: 设备模块或用户模块产生操作事件，组装 `PlcOperationLog` 对象，调用 `日志服务` 的 `insertLog` 方法。
2.  `日志服务` 调用 `日志存储` 的 `save` 方法。
3.  `日志存储` 调用 `数据库` 的 `saveLog` 方法，将日志信息持久化到数据库。
4.  **日志查询**: 用户通过界面或 API 触发查询请求，调用 `日志服务` 的某个查询方法（如 `queryLogsByDeviceName`）。
5.  `日志服务` 调用 `日志存储` 对应的查找方法（如 `findByDeviceName`）。
6.  `日志存储` 调用 `数据库` 的 `retrieveLogs` 方法，构造查询语句并从数据库获取日志记录。
7.  查询结果逐层返回给用户。
