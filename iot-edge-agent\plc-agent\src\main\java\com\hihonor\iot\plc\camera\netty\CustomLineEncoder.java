/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import java.nio.charset.StandardCharsets;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;


/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public class CustomLineEncoder extends MessageToByteEncoder<String> {
    @Override
    protected void encode(ChannelHandlerContext ctx, String msg, ByteBuf out) throws Exception {
        // 使用 ASCII 编码，将字符串转换为字节并写入 ByteBuf
        out.writeBytes(msg.getBytes(StandardCharsets.US_ASCII)); // 使用 ASCII 编码
        out.writeByte('\r'); // 添加 CR (Carriage Return)，直接使用字符 '\r'
    }
}
