@startuml
actor User
participant "会议后台系统" as Backend
participant "Windows会议设备" as Device
database "服务端存储" as Storage

User -> Backend: 调用采集日志接口
activate Backend

Backend -> Backend: 检查设备在线状态
alt 设备在线
    Backend -> Device: 发送采集日志命令 (WebSocket)
    activate Device

    Device -> Device: 压缩打包日志文件
    Device -> Backend: 调用上传日志接口
    Backend -> Storage: 存储日志文件
    activate Storage
    Storage --> Backend: 确认存储完成
    deactivate Storage

    Backend --> User: 返回日志文件
    deactivate Device
else 设备离线
    Backend --> User: 返回设备离线信息
end

deactivate Backend
@enduml