/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import com.hihonor.iot.plc.camera_and_plate.CameraPlateAssociation;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */

public class UpsertCameraPlateRequest {
    @NotEmpty(message = "关联信息列表不能为空")
    @Valid
    private List<CameraPlateAssociation> associations;

    // Getters and setters
    public List<CameraPlateAssociation> getAssociations() {
        return associations;
    }

    public void setAssociations(List<CameraPlateAssociation> associations) {
        this.associations = associations;
    }
}

