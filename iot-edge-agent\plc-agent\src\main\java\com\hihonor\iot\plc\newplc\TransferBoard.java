/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.hihonor.iot.plc.deviceLog.Device;
import com.hihonor.iot.plc.deviceLog.PlcLogOperation;
import com.hihonor.iot.plc.plate.mewutil.MEWClient;
import com.hihonor.iot.plc.newplc.modle.TransferBoardStatus;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-29
 */

@Slf4j
@Scope("prototype")
@Component
public class TransferBoard implements Device {

    @Getter
    private String ip;
    @Getter
    private int port;

    private String thingName;
    @Autowired
    TaskRegistrar taskRegistrar;


    /**
     * 设备状态地址
     */
    private static final String DEVICE_STATUS_ADDRESS = "DT88";

    /**
     * 进板信号地址
     */
    private static final String LOADING_SIGNAL_ADDRESS = "R2000";

    @Autowired
    private  PlcConfiguration plcConfiguration;

    private  TransferBoard prox;



    private MEWClient mewClient;

   /**
 * 初始化方法，用于设置IP、端口、物体名称和传输板
 * @param ip IP地址
 * @param port 端口号
 * @param thingName 物体名称
 * @param porx 传输板
 */
public void init(String ip, int port, String thingName, TransferBoard porx) {
    // 将传入的IP赋值给对象的ip属性
    this.ip = ip;
    // 将传入的端口号赋值给对象的port属性
    this.port = port;
    // 将传入的物体名称赋值给对象的thingName属性
    this.thingName = thingName;
    // 创建一个新的MEWClient对象
    mewClient = new MEWClient();
    // 调用MEWClient对象的初始化方法，传入IP、端口和物体名称
    mewClient.init(ip, port, thingName);
    // 将传入的传输板赋值给对象的prox属性
    this.prox = porx;
    // 调用connect方法，建立连接
    connect();
    // 使用taskRegistrar为该对象注册任务
    taskRegistrar.registerTasksForObject(this);
}

    @Override
    public  String getType()
    {
        return "TransferBoard";
    }

    @Override
    public String getThingName() {
        return  thingName;
    }

    public void reInit(String ip, int port, String thingName) {
        mewClient.close();
        this.ip = ip;
        this.port = port;
        this.thingName = thingName;
        mewClient = new MEWClient();
        mewClient.init(ip, port, thingName);
        connect();
    }

    private boolean connect() {

        boolean connected = mewClient.connect(2000);
        if (connected) {
            log.info("过板台 {} 连接成功", thingName);
        } else {
            log.error("过板台 {} 连接失败", thingName);
        }
        return connected;
    }


    /**
     * 根据点位名称读取值
     *
     * @param pointName 点位名称
     * @return 读取到的值的列表，如果读取失败或点位不存在，则返回空列表
     */
    @PlcLogOperation
    public String readValuesByPointName(String pointName) {
        Point point = plcConfiguration.getPointByName(pointName);
        if (point != null) {
            String startAddress = point.getStartAddress();
            String endAddress = point.getEndAddress();

            if (endAddress == null) {
                // 单个地址
                String valueList = mewClient.readData(startAddress, startAddress);
                return valueList != null ? valueList : "";
            } else {
                // 地址范围
               String valueList = mewClient.readData(startAddress, endAddress);
                return valueList != null ? valueList :"";
            }
        } else {
            log.warn("Point not found: {}", pointName);
        }
        return "";
    }

    /**
     * 获取设备状态
     *
     * @return 设备状态值
     */
    @PlcLogOperation
    public String getDeviceStatus() {
        String statusList = mewClient.readData(DEVICE_STATUS_ADDRESS, DEVICE_STATUS_ADDRESS);
        if (statusList != null && !statusList.equals("")) {
            return statusList ;
        }
        return "";
    }

    /**
     * 获取进板信号
     *
     * @return 进板信号值
     */
    @PlcLogOperation
    public String getLoadingSignal() {
      String signalList = mewClient.readData(LOADING_SIGNAL_ADDRESS, LOADING_SIGNAL_ADDRESS);
        if (!signalList.isEmpty()) {
            return signalList;
        }
        return null;
    }

  //  @CustomScheduled(fixedRate =  60 * 1000)
    public void updateStatus() {
        String status = prox.getDeviceStatus();
       String signal= prox.getLoadingSignal();
        if (status != null) {
            log.info("thingName :{}  ip:{} getDeviceStatus:{} getLoadingSignal:{}", thingName, ip + port, status,signal);
        }
    }

    /**
     * 写入进板信号  ,只能写入0或1
     *
     * @param signal 要写入的进板信号值
     * @return 是否写入成功
     */
    @PlcLogOperation
    public boolean writeLoadingSignal(String signal) {
        return mewClient.sendCommon(LOADING_SIGNAL_ADDRESS, signal);
    }

    /**
     * 根据点位名称写入值（只支持R类型地址）
     *
     * @param pointName 点位名称
     * @param value     要写入的值（只能是0或1）
     * @return 是否写入成功
     */
    @PlcLogOperation
    public boolean writeValueByPointName(String pointName, String value) {
        Point point = plcConfiguration.getPointByName(pointName);
        if (point != null) {
            String address = point.getAddress();
            String addressType = point.getAddressType();

            if (point.isAddressRange()) {
                log.warn("Cannot write to address range: {}", pointName);
                return false;
            }

            if ("R".equals(addressType)) {
                // R类型地址，只能写入0或1
                if ("0".equals(value) || "1".equals(value)) {
                    return mewClient.sendCommon(address, value);
                } else {
                    log.warn("Invalid value for R address: {}", value);
                    return false;
                }
            } else {
                log.warn("Writing is only allowed for R addresses, but got: {}", addressType);
                return false;
            }
        } else {
            log.warn("Point not found: {}", pointName);
            return false;
        }
    }


    /**
     * 获取连接状态
     *
     * @return 连接状态
     */
    @Override
    public boolean getConnected() {
        return mewClient.getConnected();
    }

    /**
     * 关闭连接
     */
    public void destroy() {
        taskRegistrar.cancelTask(this, "updateStatus");
        try {
            mewClient.close();
            mewClient = null;
        } catch (Exception e) {
            log.error("thingName :{}  ip:{}closeConnection failed", thingName, ip + port);
        }
    }

    /**
     * 获取过板台状态信息
     *
     * @return 过板台状态信息对象
     */
    public TransferBoardStatus getStatus() {
        TransferBoardStatus status = new TransferBoardStatus();
        status.setOnline(getConnected());
        status.setIp(ip);
        status.setPort(port);
        status.setName(thingName);
        status.setDeviceStatus(getDeviceStatus());
        status.setLoadingSignal(getLoadingSignal());
        return status;
    }

    // 其他接口方法...

}