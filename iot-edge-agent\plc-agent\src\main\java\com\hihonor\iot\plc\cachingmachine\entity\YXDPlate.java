/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.entity;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.thing.ThingTemplate;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Slf4j
@Component
@ThingTemplate(value = "PLCTemplate", tag = "plc:plate1127_YXD")
@Scope("prototype")
public class YXDPlate extends GHPlate {


}
