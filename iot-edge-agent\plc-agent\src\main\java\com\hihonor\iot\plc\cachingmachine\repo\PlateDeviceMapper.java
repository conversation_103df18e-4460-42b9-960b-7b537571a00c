/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.repo;

import java.util.Date;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Mapper
public interface PlateDeviceMapper extends BaseMapper<PlateDevice> {

    @Delete("DELETE FROM iot_admin.plate_device WHERE timestamp < #{thresholdDate}")
    int deleteOldRecords(@Param("thresholdDate") Date thresholdDate);
}

