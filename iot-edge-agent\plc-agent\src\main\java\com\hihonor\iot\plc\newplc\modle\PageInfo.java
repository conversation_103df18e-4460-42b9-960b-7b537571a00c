/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.modle;

import java.util.List;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-30
 */
@Data
public class PageInfo<T> {
    private List<T> list;       // 分页查询结果数据列表
    private int pageNum;        // 当前页码
    private int pageSize;       // 每页大小
    private long total;         // 总记录数
    private int pages;          // 总页数

    public PageInfo(List<T> list, long total) {
        this.list = list;
        this.total = total;
        this.pageNum = 1;
        this.pageSize = list.size();
        this.pages = (int) Math.ceil((double) total / pageSize);
    }

    // 省略 getter 和 setter 方法...
}
