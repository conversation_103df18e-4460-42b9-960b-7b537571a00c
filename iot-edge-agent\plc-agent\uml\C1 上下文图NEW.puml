@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

Person(user, "用户", "与主控制系统交互的用户")

System_Boundary(s1, "主控制系统") {
    System(main_system, "主控制系统", "处理条码信息和控制板台") {
        Container(camera_config, "摄像头设备配置", "JSON", "包含设备名、IP 地址、端口、设备类型")
        Container(plate_config, "板台设备配置", "JSON", "包含设备名、IP 地址、端口、设备类型")
    }
}

System(camera, "摄像头", "用于读取条码数据")
System(plate, "板台", "用于控制生产主板通过")
System(barcode_service, "条码验证服务", "验证条码的有效性")

Rel(user, main_system, "使用")
Rel(main_system, camera, "初始化连接")
Rel(main_system, plate, "初始化连接")
Rel(main_system, barcode_service, "验证条码")
Rel(user, camera_config, "管理摄像头设备配置")
Rel(user, plate_config, "管理板台设备配置")

note right of camera_config
    摄像头设备配置包含：
    - 设备名
    - IP 地址
    - 端口
    - 设备类型（摄像头）
end note

note right of plate_config
    板台设备配置包含：
    - 设备名
    - IP 地址
    - 端口
    - 设备类型（板台）
end note

@enduml