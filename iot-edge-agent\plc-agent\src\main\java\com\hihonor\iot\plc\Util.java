/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public class Util {
    /**
     * listToStringUsingStream
     *
     * @param list list
     * @return String
     */
    public static String listToStringUsingStream(List<Integer> list) {
        return list.stream()
                .map(String::valueOf)
                .collect(Collectors.joining());
    }

    /**
     * getcurrentTime
     *
     * @return String
     */
    public static String getcurrentTime() {
        LocalDateTime currentDateTime = LocalDateTime.now();

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化日期时间
        String formattedDateTime = currentDateTime.format(formatter);
        return formattedDateTime;
    }

    public static boolean isPlateInstruction( String description) {
        if (description == null || description.length() != 5) {
            return false;
        }

        if (description.charAt(0) != 'R') {
            return false;
        }

        for (int i = 1; i < 5; i++) {
            if (!Character.isDigit(description.charAt(i))) {
                return false;
            }
        }

        return true;
    }

}
