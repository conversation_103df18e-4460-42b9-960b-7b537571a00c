/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot.schedule;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Slf4j
@Component
public class TaskRegistrar {

    @Autowired
    @Qualifier("customTaskScheduler")
    private TaskScheduler taskScheduler;

    @Autowired
    private ApplicationContext applicationContext;
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    /**
     * 初始化
     */
    public void init() {
        String[] beanNames = applicationContext.getBeanNamesForType(Object.class);
        for (String beanName : beanNames) {
            try {
                if (applicationContext.isPrototype(beanName)) {
                    continue; // 如果是原型作用域，跳过此Bean
                }
                Object bean = applicationContext.getBean(beanName);

                // 从bean自身获取方法和注解（这样可以获取到接口上的注解）
                checkMethodsForAnnotation(bean.getClass(), bean);

                // 如果是代理对象，获取原始类并检查其上的注解
                if (AopUtils.isAopProxy(bean)) {
                    Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);
                    if (targetClass != bean.getClass()) {
                        checkMethodsForAnnotation(targetClass, bean);
                    }
                }
            } catch (Exception ex) {

                // 使用英文注释
                log.info("An exception occurred while processing the Bean, Bean name: " + beanName);
                // 可以选择是否继续处理其他Bean或是执行其他操作
            }
        }
    }

    private void checkMethodsForAnnotation(Class<?> clazz, Object bean) {
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            CustomScheduled customScheduled = method.getAnnotation(CustomScheduled.class);
            if (customScheduled != null) {
                scheduleTask(method, bean, customScheduled);
            }
        }
    }


    private void scheduleTask(Method method, Object bean, CustomScheduled customScheduled) {
        Runnable task = () -> {
            try {
                method.invoke(bean);
                log.info("Successfully executed the scheduled task");
            } catch (Exception e) {
                log.error("Scheduled task failed", e);
            }
        };

        ScheduledFuture<?> future = null;
        if (!customScheduled.cron().isEmpty()) {
            future = taskScheduler.schedule(task, new CronTrigger(customScheduled.cron()));
        } else if (customScheduled.fixedRate() > 0) {
            future = taskScheduler.scheduleAtFixedRate(task, customScheduled.fixedRate());
        }

        // 将ScheduledFuture保存到Map中，以便以后可以取消任务
        if (future != null) {
            String taskKey = System.identityHashCode(bean) + "#" + method.getName();
            scheduledTasks.put(taskKey, future);
        }
    }

    // 取消任务的方法

    /**
     * 取消任务的方法
     *
     * @param bean       bean
     * @param methodName methodName
     */
    public void cancelTask(Object bean, String methodName) {
        String taskKey = System.identityHashCode(bean) + "#" + methodName;
        ScheduledFuture<?> future = scheduledTasks.get(taskKey);
        if (future != null) {
            future.cancel(true);
            scheduledTasks.remove(taskKey);
            log.info("Successfully cancelled the scheduled task: {}", taskKey);
        } else {
            log.warn("No task found with key: {}", taskKey);
        }
    }

    /**
     * 注册任务
     *
     * @param bean bean
     */
    public void registerTasksForObject(Object bean) {
        // 获取对象的类
        Class<?> clazz = bean.getClass();

        // 遍历所有声明的方法
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            // 检查方法是否有 CustomScheduled 注解
            CustomScheduled customScheduled = method.getAnnotation(CustomScheduled.class);
            if (customScheduled != null) {
                // 安排带有 CustomScheduled 注解的方法作为定时任务
                scheduleTask(method, bean, customScheduled);
                log.info("Scheduled task {} for bean {} successfully registered", method.getName(), clazz.getName());
            }
        }
    }


}
