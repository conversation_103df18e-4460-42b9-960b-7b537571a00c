/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.repo;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hihonor.iot.plc.newplc.modle.TransferBoardInfo;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@Service
public class TransferBoardInfoService extends ServiceImpl<TransferBoardInfoMapper, TransferBoardInfo> {

    /**
     * 保存或更新过板台信息
     *
     * @param transferBoardInfo 过板台信息对象
     * @return 是否保存或更新成功
     */
    public void saveOrUpdateTransferBoardInfo(TransferBoardInfo transferBoardInfo) {
         saveOrUpdate(transferBoardInfo);
    }


    /**
     * 修改过板台信息
     *
     * @param transferBoardInfo 过板台信息对象
     * @return 是否修改成功
     */
    public void  updateTransferBoardInfo(TransferBoardInfo transferBoardInfo) {
         updateById(transferBoardInfo);
    }


    /**
     * 根据名称删除过板台信息
     *
     * @param name 过板台名称
     * @return 是否删除成功
     */
    public void deleteTransferBoardInfoByName(String name) {
        LambdaQueryWrapper<TransferBoardInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferBoardInfo::getName, name);
         remove(queryWrapper);
    }

    /**
     * 根据名称查询过板台信息(模糊查询)
     *
     * @param name 过板台名称
     * @return 过板台信息列表
     */
    public List<TransferBoardInfo> getTransferBoardInfosByName(String name) {
        LambdaQueryWrapper<TransferBoardInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(TransferBoardInfo::getName, name);
        return list(queryWrapper);
    }

    /**
     * 获取所有过板台信息
     *
     * @return 过板台信息列表
     */
    public List<TransferBoardInfo> getAllTransferBoardInfos() {
        return list();
    }
}