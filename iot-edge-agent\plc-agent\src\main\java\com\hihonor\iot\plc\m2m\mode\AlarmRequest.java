/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.m2m.mode;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.hihonor.iot.plc.Util;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlarmRequest {
    private String iotId;
    private String equipmentSn;
    private String alarmSn;
    private String alarmMethod;
    private String alarmLevel;
    private String alarmCode;
    private String alarmModule;
    private String alarmComponent;
    private String alarmUnit;
    private String alarmAction;
    private String alarmProductName;
    private String alarmDesc;
    private String alarmTime;

    /**
     * 构建报警请求
     *
     * @param name  设备名称
     * @param trace 轨道
     * @param zhen  true:报警 false:解除报警
     * @return 报警请求
     */
    public static AlarmRequest build(String name, String trace, boolean zhen) {
        AlarmRequest alarmRequest = new AlarmRequest();
        if (zhen) {
            alarmRequest.setIotId(name);
            alarmRequest.setAlarmComponent(trace);
            alarmRequest.setAlarmTime(Util.getcurrentTime());
            if (trace.equals("")) {
                alarmRequest.setAlarmDesc(name + "报警: AlarmSet");
            } else {
                alarmRequest.setAlarmDesc(name + "-" + trace + "轨道报警: AlarmSet");
            }
            alarmRequest.setAlarmMethod("AlarmSet");
            alarmRequest.setAlarmLevel("error");
            alarmRequest.setAlarmCode("3");

        } else {
            alarmRequest.setIotId(name);
            alarmRequest.setAlarmComponent(trace);
            alarmRequest.setAlarmTime(Util.getcurrentTime());
            if (trace.equals("")) {
                alarmRequest.setAlarmDesc(name + "报警: AlarmSet");
            } else {
                alarmRequest.setAlarmDesc(name + "-" + trace + "轨道报警: AlarmSet");
            }
            alarmRequest.setAlarmMethod("AlarmClear");
            alarmRequest.setAlarmLevel("warning ");
            alarmRequest.setAlarmCode("3");
        }
        return alarmRequest;

    }
    // getters and setters
}