/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Component
@Slf4j
public class ForwardMultipart {

    @Lazy
    @Autowired
    WebClient webClient;
    @Autowired
    ClusterManager clusterManager;

    private static final String HEADER_FORWARD_FLAG = "X-Forwarded-By";

    /**
     * 转发请求到其他节点
     *
     * @param requestHeader 请求头
     * @param files         文件
     * @param endpoint      转发的接口
     */
    public void forwardToRemoteNodes(HttpHeaders requestHeader, List<MultipartFile> files, String endpoint) {

        if (hasForwardHeader(requestHeader)) {
            log.info("Request is already forwarded. Skipping forward.");
            return;
        }
        for (ClusterManager.NodeInfo nodeInfo : clusterManager.NODE_INFO_CONCURRENT_HASH_MAP.values()) {
            // 跳过本地节点
            if (!clusterManager.isLocalNode(nodeInfo.getNodeInfo())) {
                String nodeUrl = "http://" + nodeInfo.getIp() + ":" + nodeInfo.getPort() + endpoint;
                Mono<String> responseMono = forwardMultipartRequest(files, nodeUrl);

                // 订阅响应
                responseMono.subscribe(
                        response -> log.info("Forward response received from {}: {}", nodeUrl, response),
                        error -> log.error("Error forwarding request to {}: {}", nodeUrl, error.getMessage()),
                        () -> log.info("Forwarding to {} complete", nodeUrl)
                );
            }
        }
    }


    private Mono<String> forwardMultipartRequest(List<MultipartFile> files, String forwardUrl) {

        MultiValueMap<String, HttpEntity<?>> body = new LinkedMultiValueMap<>();

        // 处理文件
        for (MultipartFile file : files) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(file.getContentType()));
            headers.setContentLength(file.getSize());
            headers.setContentDispositionFormData(file.getName(), file.getOriginalFilename());

            Resource resource = null;
            try {
                resource = new InputStreamResource(file.getInputStream());
            } catch (IOException e) {
                log.error("Error reading file input stream", e);
            }

            HttpEntity<Resource> fileEntity = new HttpEntity<>(resource, headers);
            body.add("files", fileEntity); // 可根据需要更改键名
        }

        // 设置请求头
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add(HEADER_FORWARD_FLAG, "1");
        log.info("Forwarding request to {} with headers: {}", forwardUrl, requestHeaders);
        // 使用 WebClient 发送转发的请求
        return webClient.post().uri(forwardUrl)
                .headers(httpHeaders -> httpHeaders.addAll(requestHeaders))
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(body))
                .retrieve()
                .bodyToMono(String.class);
    }


    private boolean hasForwardHeader(HttpHeaders headers) {
        return headers != null && headers.containsKey(HEADER_FORWARD_FLAG);
    }


}
