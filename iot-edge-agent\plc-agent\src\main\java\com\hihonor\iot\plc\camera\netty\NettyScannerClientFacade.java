/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import org.springframework.stereotype.Component;

import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Component
@Slf4j
public class NettyScannerClientFacade {

    private NettyScannerClient nettyScannerClient;

    public NettyScannerClientFacade() {
        initNettyScannerClient();
    }

    private void initNettyScannerClient() {
        nettyScannerClient = NettyScannerClient.getInstance();

    }

    public void connect(String host, int port) {
        nettyScannerClient.connect(host, port);
    }

    public boolean sendMessage(String ip, int port, String message) {
        return ConnectionManager.sendMessage(ip + ":" + port, message);
    }

    public String readBarcode(String ip, int port) {
        String key = ip + ":" + port;
        return BarcodeQueueManager.dequeueBarcode(key); // 从队列中读取条码
    }

    public boolean isDeviceOnline(String ip, int port) {
        String key = ip + ":" + port;
        return ConnectionManager.isDeviceOnline(key);
    }

    public void reconnect(String ip, int port) {
        nettyScannerClient.reconnect(ip, port);
    }

    public void close(String ip, int port) {
        String key = ip + ":" + port;
        NioSocketChannel channel = ConnectionManager.connectionMap.get(key); // 获取对应的 Channel
        if (channel != null) {
            channel.close(); // 关闭 Channel
            ConnectionManager.connectionMap.remove(key); // 从 connectionMap 中移除
            log.info("设备 {} 的连接已关闭。", key);
        } else {
            log.info("未找到设备 {} 的活动连接。", key);
        }
    }

    public void addDeviceStatusListener(String ip, int port, DeviceStatusListener listener) {
        ConnectionManager.addDeviceStatusListener(ip + ":" + port, listener);
    }

    public void removeDeviceStatusListener(String ip, int port) {
        ConnectionManager.removeDeviceStatusListener(ip + ":" + port);
    }

    public void shutdown() {
        nettyScannerClient.shutdown();
    }

    public static void main(String[] args) {
        String ip = "127.0.0.1";
        int port = 9004;

        NettyScannerClientFacade facade = new NettyScannerClientFacade();

        facade.addDeviceStatusListener(ip, port, new ReconnectHandler(ip, port));
        facade.connect(ip, port);

        // 获取单例实例
        // NettyScannerClient client = NettyScannerClient.getInstance();
        //
        // // 尝试连接到指定的服务器
        // client.connect(ip,port); // 替换为实际的 IP 和端口

        // 保持程序运行，直到手动终止
        Runtime.getRuntime().addShutdownHook(new Thread(facade::shutdown)); // 确保优雅关闭客户端

        // 无限循环保持主线程运行
        while (true) {
            try {
                // Thread.sleep(1000); // 每秒检查一次（可以根据需要调整）
                String barcode = facade.readBarcode(ip, port);
                log.info("读取到条码: {}", barcode);
            } catch (Exception e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                break; // 退出循环
            }
        }

        facade.shutdown(); // 在程序结束时优雅关闭客户端
    }
}