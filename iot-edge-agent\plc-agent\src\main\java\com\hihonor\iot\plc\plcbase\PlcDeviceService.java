/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.iot.plc.boot.schedule.CustomScheduled;
import com.hihonor.iot.plc.camera_and_plate.CameraPlateAssociation;
import com.hihonor.iot.plc.camera_and_plate.CameraPlateAssociationDeleteEvent;
import com.hihonor.iot.plc.camera_and_plate.CameraPlateAssociationMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
@Slf4j
public class PlcDeviceService {
    @Autowired
    private PlcDeviceMapper plcDeviceMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private CameraPlateAssociationMapper cameraPlateAssociationMapper;

    public IPage<PlcDevice> searchDevices(Page<PlcDevice> page, String type, String name, String tag, Boolean status) {
        if (page == null) {
            page = new Page<>(1, 10);
        }
        return plcDeviceMapper.searchByCriteria(page, type, name, tag, status);
    }

    public List<PlcDevice> searchAllDevices(String type, String name, String tag, Boolean status) {
        LambdaQueryWrapper<PlcDevice> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(type)) {
            queryWrapper.eq(PlcDevice::getPlcDeviceType, type);
        }
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(PlcDevice::getPlcDeviceName, name);
        }
        if (StringUtils.isNotBlank(tag)) {
            queryWrapper.like(PlcDevice::getPlcTag, tag);
        }
        if (status != null) {
            queryWrapper.eq(PlcDevice::getConnectionStatus, status);
        }

        return plcDeviceMapper.selectList(queryWrapper);
    }


    /**
     * 根据设备名称删除PLC设备，并根据设备类型处理相关联的相机。
     * 如果设备是相机，则发布相机删除事件。
     * 如果设备是过板台，则查询并删除所有关联的相机，并为每个相机发布删除事件。
     *
     * @param plcDeviceName 要删除的设备的名称。
     * @return 删除操作是否成功（true: 成功, false: 失败）。
     */
    public boolean deleteDeviceByName(String plcDeviceName) {
        try {
            // 查询设备信息
            List<PlcDevice> plcDevices = searchDevices(null, null, plcDeviceName, null, null).getRecords();
            if (plcDevices == null || plcDevices.isEmpty()) {
                return false;
            }
            PlcDevice device = plcDevices.get(0);
            List<CameraPlateAssociation> publicEventcameraPlateAssociations = new ArrayList<>();

            if ("camera".equals(device.getPlcDeviceType())) {
                List<CameraPlateAssociation> associations = cameraPlateAssociationMapper.selectByCameraDeviceNames(List.of(device.getPlcDeviceName()));
                if (!associations.isEmpty()) {
                    publicEventcameraPlateAssociations.add(associations.get(0));
                }
                // 发布相机删除事件
                log.info("删除相机 " + device.getPlcDeviceName() + " 与过板台的关联");
            } else if ("plate".equals(device.getPlcDeviceType())) {
                // 查询并处理所有关联的相机
                List<CameraPlateAssociation> cameraPlateAssociations = cameraPlateAssociationMapper.getCameraDeviceNameByPlateName(device.getPlcDeviceName());
                publicEventcameraPlateAssociations.addAll(cameraPlateAssociations);
            }

            // 执行删除操作
            int affectedRows = plcDeviceMapper.deleteByDeviceName(plcDeviceName);
            boolean isDeleted = affectedRows > 0;

            if (isDeleted) {
                publicEventcameraPlateAssociations.forEach(cameraPlateAssociation -> {
                    eventPublisher.publishEvent(new CameraPlateAssociationDeleteEvent(this, cameraPlateAssociation));
                    log.info("删除相机 " + cameraPlateAssociation.getCameraDeviceName() + " 与过板台 " + cameraPlateAssociation.getPlateDeviceName() + " 的关联");
                });

                // 发布设备删除事件
                eventPublisher.publishEvent(new PlcDeviceDeleteEvent(this, device));
            }

            return isDeleted;
        } catch (Exception e) {
            // 记录错误日志
            log.error("Failed to delete device by name: " + plcDeviceName, e);
            return false;
        }
    }

    public Map<String, Boolean> deleteDevicesByNames(List<String> names) {
        Map<String, Boolean> results = new HashMap<>();
        for (String name : names) {
            boolean result = deleteDeviceByName(name);
            results.put(name, result);
        }
        return results;
    }

    public boolean saveOrUpdateDevice(PlcDevice device) {
        try {
            boolean result;
            int exists = plcDeviceMapper.countByDeviceName(device.getPlcDeviceName());
            if (exists > 0) {
                // 设备已存在，执行更新操作
                result = plcDeviceMapper.update(device, new QueryWrapper<PlcDevice>()
                        .eq("plc_device_name", device.getPlcDeviceName())) > 0;
            } else {
                // 设备不存在，执行插入操作
                result = plcDeviceMapper.insert(device) > 0;
            }

            if (result) {
                // 发布设备更新事件，不区分插入或更新
                eventPublisher.publishEvent(new DeviceUpdateEvent(this, device));
            }

            return result;
        } catch (Exception e) {
            // 记录错误日志
            log.error("Failed to save or update device: " + device.getPlcDeviceName(), e);
            return false;
        }
    }
    public Map<String, Boolean> saveOrUpdateDevices(List<PlcDevice> devices) {
        Map<String, Boolean> results = new HashMap<>();
        for (PlcDevice device : devices) {
            boolean result = saveOrUpdateDevice(device);
            results.put(device.getPlcDeviceName(), result);
        }
        return results;
    }

    public boolean reportDeviceOnline(String plcDeviceName) {
        int affectedRows = plcDeviceMapper.updateLastCommunicationTime(plcDeviceName);
        return affectedRows > 0;
    }

    public void updateConnectionStatusByLastCommunication(int minutesThreshold) {
        plcDeviceMapper.updateConnectionStatusByLastCommunication(minutesThreshold);
    }

    @CustomScheduled(fixedRate = 30 * 1000)
    public void updateDeviceOnlineStatus() {

        updateConnectionStatusByLastCommunication(1);
        log.info("更新设备在线状态成功");

    }
}
