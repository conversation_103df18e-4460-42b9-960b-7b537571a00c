# 过板台设备控制接口规范文档

## 文档概述

- **文档版本**：1.0.0
- **更新日期**：2024-06-01
- **适用产品**：IoT 设备网关
- **文档状态**：正式发布

本文档描述了过板台设备的控制接口规范，提供了对过板台设备进行控制和数据读取的标准 HTTP API。

## API 基础信息

- **基础 URL**：`http://iot-gateway.yun.hihonor.com`
- **内容格式**：JSON
- **请求头**：所有 API 请求需包含`Content-Type: application/json`
- **认证方式**：暂无，后续可能增加认证机制

## API 列表

### 1. 写入过板台点位值

控制过板台设备特定地址的数值。

#### 请求信息

- **URL**：`/plc/plate/writeValue`
- **方法**：POST
- **描述**：向指定过板台设备的特定地址点位写入一个数值

#### 请求参数

| 名称      | 类型   | 必填 | 说明             | 示例             |
| --------- | ------ | ---- | ---------------- | ---------------- |
| plateName | String | 是   | 过板台设备标识符 | "SMT09_HuiLiuLu" |
| address   | String | 是   | 写入的目标地址   | "R2000"          |
| value     | String | 是   | 要写入的数值     | "1"              |

#### 请求示例

```http
POST /plc/plate/writeValue HTTP/1.1
Host: iot-gateway.yun.hihonor.com
Content-Type: application/json

{
  "plateName": "SMT09_HuiLiuLu",
  "address": "R2000",
  "value": "1"
}
```

#### 响应参数

| 名称    | 类型    | 说明         |
| ------- | ------- | ------------ |
| success | Boolean | 操作是否成功 |
| message | String  | 结果描述信息 |
| data    | Boolean | 操作执行结果 |
| length  | Integer | 数据长度     |

#### 响应示例 - 成功

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "写入成功",
  "data": true,
  "length": 0
}
```

#### 响应示例 - 失败

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": false,
  "message": "写入失败",
  "data": false,
  "length": 0
}
```

#### 错误码

| HTTP 状态码 | 错误码         | 说明                                                  |
| ----------- | -------------- | ----------------------------------------------------- |
| 200         | -              | 请求成功处理，但业务逻辑可能失败，请查看 success 字段 |
| 400         | INVALID_PARAMS | 请求参数错误或不完整                                  |
| 500         | INTERNAL_ERROR | 服务器内部错误                                        |

#### 业务场景

此接口用于向过板台设备写入控制信号或参数设置，例如：

- 告警信号触发（值为 1）和复位（值为 0）
- 设备控制参数调整
- 状态标志位设置

---

### 2. 读取过板台数据

读取过板台设备特定地址范围的数据。

#### 请求信息

- **URL**：`/plc/plate/readData`
- **方法**：POST
- **描述**：读取指定过板台设备一段连续地址的数据值

#### 请求参数

| 名称         | 类型   | 必填 | 说明             | 示例             |
| ------------ | ------ | ---- | ---------------- | ---------------- |
| plateName    | String | 是   | 过板台设备标识符 | "SMT09_HuiLiuLu" |
| startAddress | String | 是   | 读取的起始地址   | "R2000"          |
| endAddress   | String | 是   | 读取的结束地址   | "R2005"          |

#### 请求示例

```http
POST /plc/plate/readData HTTP/1.1
Host: iot-gateway.yun.hihonor.com
Content-Type: application/json

{
  "plateName": "SMT09_HuiLiuLu",
  "startAddress": "R2000",
  "endAddress": "R2005"
}
```

#### 响应参数

| 名称    | 类型    | 说明                             |
| ------- | ------- | -------------------------------- |
| success | Boolean | 操作是否成功                     |
| message | String  | 结果描述信息                     |
| data    | String  | 读取到的数据内容，按地址顺序拼接 |
| length  | Integer | 数据长度                         |

#### 响应示例 - 成功

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "读取成功",
  "data": "100110",
  "length": 6
}
```

#### 响应示例 - 失败

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": false,
  "message": "读取失败",
  "data": "",
  "length": 0
}
```

#### 错误码

| HTTP 状态码 | 错误码           | 说明                                                  |
| ----------- | ---------------- | ----------------------------------------------------- |
| 200         | -                | 请求成功处理，但业务逻辑可能失败，请查看 success 字段 |
| 400         | INVALID_PARAMS   | 请求参数错误或不完整                                  |
| 404         | DEVICE_NOT_FOUND | 指定的设备不存在                                      |
| 500         | INTERNAL_ERROR   | 服务器内部错误                                        |

#### 业务场景

此接口用于获取过板台设备的状态信息，例如：

- 读取当前设备运行状态
- 验证先前写入操作是否成功
- 批量获取设备参数配置
- 监控告警状态位

## 注意事项

1. 接口仅提供基础控制功能，复杂业务逻辑需在调用方实现
2. 建议写入操作后通过读取接口验证写入结果
3. 过板台设备名称(plateName)必须是系统中已配置的有效设备
4. 地址格式需符合设备规范，通常 R 类型点位格式为"R"后跟数字

## 数据类型说明

- **地址格式**：

  - R 类型：如"R2000"，用于控制点位
  - DT 类型：如"DT100"，用于存储数据

- **数据值**：
  - 即使是数值，也以字符串形式传输
  - 通常为 0/1 表示开关量，或其他数值

## 附录

### 常见错误处理

1. 设备不存在：检查 plateName 是否正确
2. 地址格式错误：确认 address 格式符合规范
3. 连接超时：可能是网络问题或设备离线

### 版本历史

- 1.0.0 (2024-06-01): 初始版本
