/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.deviceLog;

import java.util.Date;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Mapper
public interface PlcOperationLogMapper extends BaseMapper<PlcOperationLog> {
    @Select("<script>"
            + "SELECT * FROM iot_admin.plc_operation_log"
            + "<where>"
            + "    <if test='deviceName != null and deviceName.trim() != \"\"'>"
            + "        AND device_name = #{deviceName}"
            + "    </if>"
            + "    <if test='methodName != null and methodName.trim() != \"\"'>"
            + "        AND method_name ILIKE CONCAT('%', #{methodName}, '%')"
            + "    </if>"
            + "    <if test='startTime != null'>"
            + "        AND operation_time &gt;= #{startTime}"
            + "    </if>"
            + "    <if test='endTime != null'>"
            + "        AND operation_time &lt;= #{endTime}"
            + "    </if>"
            + "</where>"
            + " ORDER BY operation_time DESC"
            + "</script>")
    IPage<PlcOperationLog> selectByDeviceNameTimeRangeOrMethodName(Page<PlcOperationLog> page,
                                                                   @Param("deviceName") String deviceName,
                                                                   @Param("startTime") Date startTime,
                                                                   @Param("endTime") Date endTime,
                                                                   @Param("methodName") String methodName);


//    @Delete("DELETE FROM iot_admin.plc_operation_log WHERE operation_time " +
//            " BETWEEN #{startTime} AND #{endTime}")
//    int deleteByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 创建plc_operation_log表的分区。
     */
    @Update("SELECT iot_admin.create_plc_operation_log_partition()")
    void createPartition();

    /**
     * 删除plc_operation_log表的指定日期分区。
     *
     * @param date 要删除的分区的日期,格式为'YYYY-MM-DD'
     */
    @Update("SELECT iot_admin.drop_plc_operation_log_partition(#{date}::date)")
    void dropPartition(String date);

}