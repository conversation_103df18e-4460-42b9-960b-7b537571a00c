/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
public class ConnectionThreadPool {
    private static final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(10);

    public static ScheduledExecutorService getInstance() {
        return executorService;
    }
}
