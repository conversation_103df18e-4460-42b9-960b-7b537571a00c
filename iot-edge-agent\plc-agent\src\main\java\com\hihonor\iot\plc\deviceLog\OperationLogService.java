/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.deviceLog;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.iot.plc.boot.schedule.CustomScheduled;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Slf4j
@Service
public class OperationLogService {
    @Autowired
    private PlcOperationLogMapper operationLogMapper;



    /**
     * 保存操作日志
     *
     * @param operationLog 操作日志对象
     */
    public void saveOperationLog(PlcOperationLog operationLog) {
        operationLogMapper.insert(operationLog);
        log.info("保存操作日志: {}", operationLog);
    }

    // @PostConstruct
    public void init() {
        log.info("OperationLogService initialized");
        createNewPartition();
        createNewPartition();
    }
    /**
     * 根据设备名称、时间范围或方法名查询操作日志
     *
     * @param page       分页参数
     * @param deviceName 设备名称
     * @param startTime  起始时间
     * @param endTime    结束时间
     * @param methodName 方法名
     * @return 分页后的操作日志列表和总数信息
     */
    public IPage<PlcOperationLog> getOperationLogs(Page<PlcOperationLog> page, String deviceName, Date startTime, Date endTime, String methodName) {
        IPage<PlcOperationLog> operationLogsPage = operationLogMapper.selectByDeviceNameTimeRangeOrMethodName(page, deviceName, startTime, endTime, methodName);
        log.info("查询操作日志, 设备名称: {}, 时间范围: [{}, {}], 方法名: {}, 当前页: {}, 每页显示数: {}, 总结果数: {}",
                deviceName, startTime, endTime, methodName, page.getCurrent(), page.getSize(), operationLogsPage.getTotal());
        return operationLogsPage;
    }




    @CustomScheduled(cron = "0 0 1 * * ?")
    public void cleanupOperationLogs() {
        // 计算7天前的日期
        // 调用 PlcOperationLogMapper 的 deleteByTimeRange 方法删除7天前的记录
        //  int deletedCount = deleteOperationLogsByDate(7);
        createNewPartition();
        removeOldPartition(getDateBefore7Days());
        //    log.info("定时清理操作日志,删除记录数: {}", deletedCount);
    }

    /**
     * 计算当前日期前7天的日期。
     *
     * @return 前7天的日期字符串, 格式为"YYYY-MM-DD"
     */
    String getDateBefore7Days() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算7天前的日期
        LocalDate dateBefore7Days = currentDate.minusDays(7);

        // 格式化日期为字符串
        String formattedDate = dateBefore7Days.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        log.info("Current date: {}, Date before 7 days: {}", currentDate, formattedDate);

        return formattedDate;
    }

    private void createNewPartition() {
        try {
            operationLogMapper.createPartition();
            log.info("Successfully created new partition for plc_operation_log table");
        } catch (Exception e) {
            log.error("Failed to create new partition", e);
            throw e;
        }
    }

    private void removeOldPartition(String date) {
        try {
            operationLogMapper.dropPartition(date);
            log.info("Successfully dropped partition for date: {}", date);
        } catch (Exception e) {
            log.error("Failed to drop partition for date: {}", date, e);
            throw e;
        }


    }
}