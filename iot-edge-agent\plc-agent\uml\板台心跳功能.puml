@startuml
actor "Scheduler" as S
participant "HeartbeatService" as HS
participant "Device" as D
participant "Database" as DB
participant "M2MService" as M2M

S -> HS : 触发心跳检测 (每2分钟)
activate HS

HS -> D : 发送获取1轨状态指令
alt 设备在线
    D --> HS : 返回状态信息
    HS -> DB : 报告设备在线状态
    HS -> M2M : 发送心跳信息
    alt 心跳发送成功
        M2M --> HS : 返回成功
        HS -> HS : 记录心跳成功日志
    else 心跳发送失败
        M2M --> HS : 返回失败
        HS -> HS : 记录心跳失败日志
    end
else 设备离线
    D --> HS : 无响应
    HS -> HS : 关闭当前连接
    HS -> D : 尝试重新连接
    HS -> HS : 记录重连日志
end

HS -> HS : 记录设备状态日志

deactivate HS
@enduml