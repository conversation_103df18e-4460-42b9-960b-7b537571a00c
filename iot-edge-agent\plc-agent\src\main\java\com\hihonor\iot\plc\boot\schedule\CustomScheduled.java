/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot.schedule;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CustomScheduled {
    long fixedRate() default -1; // 按固定间隔执行的时间（毫秒）
    String cron() default "";    // CRON表达式
}

