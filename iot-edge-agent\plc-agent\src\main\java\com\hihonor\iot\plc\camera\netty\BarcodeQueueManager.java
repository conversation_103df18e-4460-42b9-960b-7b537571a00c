/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Slf4j
public class BarcodeQueueManager {
    // 默认队列容量
    private static final int DEFAULT_QUEUE_CAPACITY = 100;
    // 入队超时时间（毫秒）
    private static final long ENQUEUE_TIMEOUT_MS = 500;

    // 使用 ConcurrentHashMap 存储 IP:PORT 和对应的阻塞队列
    private static final Map<String, LinkedBlockingQueue<String>> queueMap = new ConcurrentHashMap<>();

    /**
     * 将条码数据添加到对应的阻塞队列中。
     *
     * @param key     设备的 IP:PORT
     * @param barcode 要添加的条码数据
     * @return true 如果入队成功，false 如果入队失败（队列已满或超时）
     */
    public static boolean enqueueBarcode(String key, String barcode) {
        // 参数校验
        if (key == null || key.isEmpty()) {
            log.warn("key参数为空，无法入队");
            return false;
        }
        if (barcode == null || barcode.isEmpty()) {
            log.warn("barcode参数为空，无法入队");
            return false;
        }

        LinkedBlockingQueue<String> queue = queueMap.computeIfAbsent(key,
                k -> new LinkedBlockingQueue<>(DEFAULT_QUEUE_CAPACITY));

        try {
            // 尝试在指定时间内将条码放入队列
            boolean success = queue.offer(barcode, ENQUEUE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
            if (success) {
                log.info("条码入队成功，设备:{}, 条码:{}", key, barcode);
            } else {
                log.warn("队列已满或超时，设备:{}, 丢弃条码:{}", key, barcode);
            }
            return success;
        } catch (Exception e) {
            log.error("条码入队失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从对应的阻塞队列中取出条码数据。
     *
     * @param key 设备的 IP:PORT
     * @return 从队列中取出的条码数据，或 null 如果没有可用数据
     */
    public static String dequeueBarcode(String key) {
        LinkedBlockingQueue<String> queue = queueMap.computeIfAbsent(key,
                k -> new LinkedBlockingQueue<>(DEFAULT_QUEUE_CAPACITY));

        try {
            return queue.take();
        } catch (Exception e) {
            log.error("Failed to dequeue barcode: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将条码数据添加到对应连接的阻塞队列中。
     *
     * @param channel 设备连接的 Channel
     * @param barcode 要添加的条码数据
     * @return true 如果入队成功，false 如果入队失败
     */
    public static boolean enqueueBarcode(NioSocketChannel channel, String barcode) {
        String key = getKey(channel);
        return enqueueBarcode(key, barcode);
    }

    /**
     * 从对应连接的阻塞队列中取出条码数据。
     *
     * @param channel 设备连接的 Channel
     * @return 从队列中取出的条码数据，或 null 如果没有可用数据
     */
    public static String dequeueBarcode(NioSocketChannel channel) {
        String key = getKey(channel);
        return dequeueBarcode(key);
    }

    /**
     * 获取队列的当前容量。
     *
     * @param key 设备的 IP:PORT
     * @return 队列当前大小
     */
    public static int getQueueSize(String key) {
        LinkedBlockingQueue<String> queue = queueMap.get(key);
        return (queue != null) ? queue.size() : 0;
    }

    /**
     * 获取队列的剩余容量。
     *
     * @param key 设备的 IP:PORT
     * @return 队列剩余容量
     */
    public static int getRemainingCapacity(String key) {
        LinkedBlockingQueue<String> queue = queueMap.get(key);
        return (queue != null) ? queue.remainingCapacity() : DEFAULT_QUEUE_CAPACITY;
    }

    /**
     * 清理指定设备的队列
     *
     * @param key 设备的 IP:PORT
     * @return 被清理队列中剩余的条码数量
     */
    public static int cleanupQueue(String key) {
        LinkedBlockingQueue<String> removedQueue = queueMap.remove(key);
        if (removedQueue != null) {
            int remaining = removedQueue.size();
            log.info("清理队列 {}，剩余条码数量: {}", key, remaining);
            return remaining;
        } else {
            log.info("尝试清理不存在的队列：{}", key);
            return 0;
        }
    }

    /**
     * 清理指定 Channel 对应的队列
     *
     * @param channel 设备连接的 Channel
     * @return 被清理队列中剩余的条码数量
     */
    public static int cleanupQueue(NioSocketChannel channel) {
        String key = getKey(channel);
        return cleanupQueue(key);
    }

    /**
     * 获取 IP:PORT 字符串作为键。
     *
     * @param channel 设备连接的 Channel
     * @return IP:PORT 字符串
     */
    private static String getKey(NioSocketChannel channel) {
        return channel.remoteAddress().getHostString() + ":" + channel.remoteAddress().getPort();
    }
}