/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate.mewutil;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.StandardSocketOptions;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Slf4j
@Component
@Scope("prototype")
public class MEWClient {
    private SocketChannel socketChannel;
    private ByteBuffer readBuffer = ByteBuffer.allocate(1024);
    private ByteBuffer writeBuffer = ByteBuffer.allocate(1024);

    private volatile boolean connected = false;
    private String host;
    private int port;
    private String thingName;

    public boolean getConnected() {
        // 简单的方式来验证连接，例如发送一个小的数据包
        // 具体实现根据实际情况而定
        return connected && socketChannel != null && socketChannel.isOpen() && socketChannel.isConnected();
    }

    public MEWClient() {

    }

    /**
     * 初始化MEWClient实例。
     *
     * @param host      PLC服务器的IP地址
     * @param port      PLC服务器的端口号
     * @param thingName 用于标识客户端的名称
     */
    public void init(String host, int port, String thingName) {
        int attempt = 0;
        this.thingName = thingName;
        this.host = host;
        this.port = port;

    }

    /**
     * 初始化MEWClient实例。
     *
     * @param host PLC服务器的IP地址
     * @param port PLC服务器的端口号
     */
    public void init(String host, int port) {
        init(host, port, "mewclient");
    }

    /**
     * 尝试连接到服务器。
     *
     * @param timeout 超时时间（毫秒）
     * @return 如果连接成功返回true，否则返回false
     */
    public synchronized boolean connect(int timeout) {
        if (connected && socketChannel != null) {
            // 如果条件满足，认为连接仍然活跃
            return true;
        }

        // 关闭之前的连接
        close();

        try {
            socketChannel = SocketChannel.open();
            socketChannel.configureBlocking(true);
            socketChannel.socket().connect(new InetSocketAddress(host, port), timeout);
            socketChannel.setOption(StandardSocketOptions.SO_KEEPALIVE, true);
            connected = true;
            log.info("设备[{}]连接成功，地址：{}:{}", thingName, host, port);
            return true;
        } catch (Exception e) {
            log.error("设备[{}]连接失败，地址：{}:{}，原因：{}", thingName, host, port, e.toString());
            socketChannel = null; // 显式地设置为null
            connected = false;
            return false;
        }
    }

    /**
     * 读取PLC的数据
     *
     * @param startAdrss 起始地址
     * @param endAdrss   结束地址
     * @return 结果
     */
    public String readData(String startAdrss, String endAdrss) {
        String res = "";
        log.info("设备[{}]开始读取数据，地址：{}:{}，起始地址：{}，结束地址：{}",
                thingName, host, port, startAdrss, endAdrss);
        if (connect(1000)) {
            List<String> dataList = readDataInner(startAdrss, endAdrss);
            StringBuilder sb = new StringBuilder();
            for (String data : dataList) {
                sb.append(data);
            }
            res = sb.toString();
            log.info("设备[{}]读取数据完成，地址：{}:{}，起始地址：{}，结束地址：{}，数据：{}",
                    thingName, host, port, startAdrss, endAdrss, res);
        } else {
            log.error("设备[{}]读取数据失败，地址：{}:{}，起始地址：{}，结束地址：{}，原因：连接失败",
                    thingName, host, port, startAdrss, endAdrss);
        }
        return res;
    }

    private List<String> readDataInner(String startAdrss, String endAdrss) {
        try {
            List<String> res = new ArrayList<>();
            String command = MEWTOCOL.generateCommandByAddressType(startAdrss, endAdrss);
            // 内部方法，不记录正常情况的日志
            if (command != null && !command.isEmpty()) {
                String response = sendCommandAndReceiveResponse(command, 2000);
                if (response != null) {
                    res = MEWTOCOL.parsePLCResponse(response);
                }
            }
            return res;
        } catch (Exception ex) {
            connected = false;
            log.error("设备[{}]读取数据内部处理失败，地址：{}:{}，起始地址：{}，结束地址：{}，原因：{}",
                    thingName, host, port, startAdrss, endAdrss, ex.getMessage());
            return new ArrayList<>();
        }
    }

    public boolean sendCommon(String address, String... data) {
        log.info("设备[{}]开始发送命令，地址：{}:{}，目标地址：{}，写入数据：{}",
                thingName, host, port, address, Arrays.toString(data));

        if (!connect(1000)) {
            log.error("设备[{}]发送命令失败，地址：{}:{}，目标地址：{}，原因：连接失败",
                    thingName, host, port, address);
            return false;
        }

        try {
            // 空值检查
            if (address == null || data == null || data.length == 0) {
                log.error("设备[{}]发送命令参数无效，地址：{}:{}，目标地址：{}，数据为空",
                        thingName, host, port, address);
                return false;
            }

            String common;
            boolean isDTAddress = address.startsWith("DT");

            if (isDTAddress) {
                // 使用 DT 类型的编码方法
                List<String> dataList = Arrays.asList(data);
                String endAddress = calculateEndAddress(address, data.length);
                common = MEWTOCOL.encodeWriteRegisterCommand(address, endAddress, dataList);
            } else {
                // 使用原有的 R 类型编码方法，只使用第一个数据
                common = MEWTOCOL.writeSingleContact(address, data[0]);
            }

            // 检查生成的命令是否为null
            if (common == null) {
                log.error("设备[{}]生成命令失败，地址：{}:{}，目标地址：{}，命令为空",
                        thingName, host, port, address);
                return false;
            }

            log.info("设备[{}]生成命令成功，地址：{}:{}，目标地址：{}，命令：{}",
                    thingName, host, port, address, common);

            String response;
            try {
                response = sendCommandAndReceiveResponse(common, 2000);
            } catch (IOException e) {
                log.error("设备[{}]发送命令执行异常，地址：{}:{}，目标地址：{}，原因：{}",
                        thingName, host, port, address, e.getMessage());
                return false;
            }

            // 检查响应是否为null
            if (response == null) {
                log.error("设备[{}]响应为空，地址：{}:{}，目标地址：{}",
                        thingName, host, port, address);
                return false;
            }

            log.info("设备[{}]发送命令成功，地址：{}:{}，目标地址：{}，响应：{}",
                    thingName, host, port, address, response);

            boolean result;
            if (isDTAddress) {
                // 使用 DT 类型的解码方法
                result = MEWTOCOL.decodeWriteRegisterResponse(response);
            } else {
                // 使用原有的 R 类型解码方法
                result = MEWTOCOL.parseWriteSingleContactResponse(response);
            }

            log.info("设备[{}]命令执行{}，地址：{}:{}，目标地址：{}",
                    thingName, host, port, address, result ? "成功" : "失败");
            return result;

        } catch (Exception ex) {
            connected = false;
            log.error("设备[{}]发送命令失败，地址：{}:{}，目标地址：{}，原因：{}",
                    thingName, host, port, address, ex.getMessage());
            return false;
        }
    }

    private String calculateEndAddress(String startAddress, int dataCount) {
        // 假设地址格式为 "DTxxxxx"
        int startNum = Integer.parseInt(startAddress.substring(2));
        int endNum = startNum + dataCount - 1;
        return String.format("DT%05d", endNum);
    }

    /**
     * 发送命令并接收响应，支持重试机制。
     *
     * @param command 发送的命令
     * @param timeout 超时时间（毫秒）
     * @return 设备的响应
     * @throws IOException 如果发送命令或接收响应失败
     */
    private synchronized String sendCommandAndReceiveResponse(String command, int timeout) throws IOException {
        final int maxRetries = 3; // 最大重试次数
        int attempt = 0; // 当前尝试次数

        while (attempt < maxRetries) {
            long startTime = System.currentTimeMillis(); // 记录开始时间
            // 底层方法不记录正常操作的日志

            // 发送命令
            writeBuffer.clear();
            writeBuffer.put(command.getBytes(StandardCharsets.US_ASCII));
            writeBuffer.flip();

            // 发送命令
            try {
                while (writeBuffer.hasRemaining()) {
                    if (System.currentTimeMillis() - startTime > timeout) {
                        log.error("设备[{}]发送命令超时，地址：{}:{}，命令：{}，超时时间：{}ms",
                                thingName, host, port, command, timeout);
                        throw new IOException("发送命令超时");
                    }
                    socketChannel.write(writeBuffer);
                }
                // 底层方法不记录正常操作的日志
            } catch (IOException e) {
                attempt++;
                log.error("设备[{}]发送命令失败，地址：{}:{}，命令：{}，超时时间：{}ms，原因：{}，尝试次数：{}/{}",
                        thingName, host, port, command, timeout, e.getMessage(), attempt, maxRetries);
                if (attempt >= maxRetries) {
                    throw new IOException("发送命令失败，已达到最大重试次数", e);
                }
                try {
                    Thread.sleep(2000); // 失败后等待2秒再重试
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
                continue; // 继续重试
            }

            // 接收响应
            readBuffer.clear();
            StringBuilder response = new StringBuilder();

            while (true) {
                if (socketChannel.read(readBuffer) > 0) {
                    readBuffer.flip();
                    while (readBuffer.hasRemaining()) {
                        byte b = readBuffer.get();
                        char character = (char) b;
                        response.append(character);
                        // 检查是否为0D结束符
                        if (b == 0x0D) { // 十六进制0D等同于'\r'
                            log.info("设备[{}]接收到0D结束符，完整响应：{}", thingName, response.toString());
                            return response.toString();
                        }
                    }
                    readBuffer.clear();
                    log.info("设备[{}]当前接收到的不完整响应：{}", thingName, response.toString());
                } else if (System.currentTimeMillis() - startTime > timeout) {
                    log.error("设备[{}]接收响应超时，地址：{}:{}，命令：{}，超时时间：{}ms",
                            thingName, host, port, command, timeout);
                    throw new IOException("接收响应超时");
                } else {
                    // 没有数据可读，短暂休眠避免CPU占用过高
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        // 如果到达这里，说明已经重试了最大次数但仍然失败
        throw new IOException("发送命令失败，已达到最大重试次数");
    }

    /**
     * 关闭与服务器的连接。
     *
     * @throws IOException 如果在关闭连接时发生I/O错误
     */

    public void close() {
        if (socketChannel != null) {
            try {
                socketChannel.close();
                log.info("设备[{}]连接已关闭，地址：{}:{}", thingName, host, port);
            } catch (IOException e) {
                log.error("设备[{}]关闭连接失败，地址：{}:{}，原因：{}", thingName, host, port, e.getMessage());
            } finally {
                socketChannel = null;
                connected = false;
            }
        }
    }

}