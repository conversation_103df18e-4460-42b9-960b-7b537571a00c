过站管控系统功能需求规范与流程说明
1. 系统需求概述
   1.1 高可用性保障
   系统整体可用性必须达到99.95%以上，具备极高的稳定性和可靠性。需要建立完善的容错和快速恢复机制，确保设备层、通信层和应用层的高可用性，避免因系统故障导致的产线停止。
   1.2 实时性能要求
   系统必须确保从相机扫码到板台执行的完整过站管控流程在1秒内完成。这包括条码采集、MES系统验证和板台控制等全流程的处理时间，每个环节都需要进行精确的时间控制和监控。
   1.3 多系统协同要求
   系统需实现设备层、IOT平台和MES系统的协同工作。所有系统间的交互采用同步响应模式（Request-Reply），确保业务处理的实时性和准确性，避免出现状态不明确的情况。
   1.4 安全性要求
   在调用上游MES系统的REST接口时，需要实现动态token验证机制，确保系统间通信的安全性。
2. 核心功能要求
   2.1 设备管理

实时监控相机和板台的在线状态、工作状态和异常状态
记录详细的状态变更信息，包括时间戳和状态描述
确保设备状态信息在整个系统中保持一致

2.2 指令控制

实现严格的请求-响应机制
等待并确认设备执行结果后才进行下一步操作
记录完整的指令执行链路信息

2.3 数据完整性

对输入数据进行严格的格式验证和内容校验
确保条码数据符合预定义的格式规范
实现数据的验证、清洗和转换机制

2.4 可观测性与异常处理

提供完整的操作日志、性能指标监控和异常告警机制
支持系统行为的追踪和分析
提供实时监控面板
及时通知现场设备管理员异常情况
提供清晰的异常信息和处理建议

3. 业务流程图
   mermaidCopysequenceDiagram
   box 设备层 #LightYellow
   participant PCB as 单板
   participant Camera as 相机
   participant Platform as 板台(默认关闭)
   end

   box 系统层 #LightBlue
   participant IOT as IOT平台
   participant MES as MES系统
   end

   Note over Camera,MES: 系统间通信采用同步响应模式(Request-Reply)<br/>MES接口调用需进行动态Token验证

   rect rgb(240, 240, 240)
   Note right of IOT: 设备状态监控(持续进行)
   IOT->>Camera: 设备状态检测
   IOT->>Platform: 设备状态检测
   end

   rect rgb(240, 240, 240)
   Note right of IOT: 单板过站流程(1s内完成)
   PCB->>Camera: 单板到达扫码位置
   activate Camera
   Camera->>Camera: 条码格式校验
   Camera->>IOT: 上报条码数据
   deactivate Camera

   activate IOT
   IOT->>IOT: 数据完整性校验
   IOT->>MES: 发送条码验证请求
   Note right of MES: 动态Token验证

   alt 条码验证通过
   MES-->>IOT: 返回验证成功
   IOT->>Platform: 发送开启信号(OK1)
   activate Platform
   Platform->>PCB: 临时开启，放行单板
   PCB->>Platform: 单板通过
   Platform->>Platform: 自动恢复关闭状态(OK0)
   Platform->>IOT: 执行结果反馈
   deactivate Platform
   else 条码验证失败
   MES-->>IOT: 返回验证失败
   IOT->>Platform: 保持关闭并发送报警
   Note over IOT: 记录异常并通知管理员
   end
   deactivate IOT
   end

   rect rgb(240, 240, 240)
   Note right of IOT: 状态记录与监控
   IOT->>IOT: 记录完整业务链路日志
   Note right of IOT: 包含时间戳、状态变更<br/>异常信息等完整记录
   end
   本文档通过系统需求概述、核心功能要求和业务流程图三个部分，完整描述了过站管控系统的功能需求规范。其中，业务流程图直观展示了系统各组件之间的交互关系和处理流程，有助于理解系统的运行机制。