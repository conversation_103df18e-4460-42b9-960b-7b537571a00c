/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.iot.plc.ApiPageResponse;
import com.hihonor.iot.plc.plcbase.request.DeleteDevicesRequest;
import com.hihonor.iot.plc.plcbase.request.SaveOrUpdateDevicesRequest;
import com.hihonor.iot.plc.plcbase.request.SearchDevicesRequest;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@RestController
@RequestMapping("/plc")
@Validated
public class DeviceController {

    @Autowired
    private PlcDeviceService plcDeviceService;

    @PostMapping("/search")
    public ApiPageResponse<List<PlcDevice>> searchDevices(@RequestBody @Valid SearchDevicesRequest request) {
        Page<PlcDevice> page = new Page<>(request.getPage(), request.getSize());
        IPage<PlcDevice> result = plcDeviceService.searchDevices(page, request.getType(), request.getName(), request.getTag(), request.getStatus());

        ApiPageResponse<List<PlcDevice>> response = new ApiPageResponse<>(
                true,
                "查询成功",
                result.getRecords(),
                result.getRecords().size(),
                result.getPages(),
                result.getTotal(),
                result.getCurrent()
        );

        return response;
    }

    @PostMapping("/delete")
    public ApiPageResponse<Map<String, Boolean>> deleteDevicesByNames(@RequestBody @Valid DeleteDevicesRequest request) {
        Map<String, Boolean> results = plcDeviceService.deleteDevicesByNames(request.getNames());
        return new ApiPageResponse<>(
                !results.containsValue(false),
                "批量删除设备操作完成",
                results,
                results.size(),
                results.size(),
                results.size(),
                1 // 由于这是一个批量操作，不涉及分页，我们可以将当前页码设置为1
        );
    }

    @PostMapping("/saveOrUpdate")
    public ApiPageResponse<Map<String, Boolean>> saveOrUpdateDevices(@RequestBody @Valid SaveOrUpdateDevicesRequest request) {
        Map<String, Boolean> results = plcDeviceService.saveOrUpdateDevices(request.getDevices());
        return new ApiPageResponse<>(
                !results.containsValue(false),
                "批量新增或更新设备操作完成",
                results,
                results.size(),
                results.size(),
                results.size(),
                1 // 由于这是一个批量操作，不涉及分页，我们可以将当前页码设置为1
        );
    }

    @PostMapping("/export")
    public ResponseEntity<ByteArrayResource> exportDevices(@RequestBody @Valid SearchDevicesRequest request) throws IOException {
        // 获取所有匹配的记录，不进行分页
        List<PlcDevice> devices = plcDeviceService.searchAllDevices(request.getType(), request.getName(), request.getTag(), request.getStatus());

        // 创建工作簿和工作表
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("设备管理");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("设备类型");
        headerRow.createCell(1).setCellValue("设备名称");
        headerRow.createCell(2).setCellValue("ip");
        headerRow.createCell(3).setCellValue("端口");
        headerRow.createCell(4).setCellValue("在线状态");
        headerRow.createCell(5).setCellValue("最后通信时间");
        headerRow.createCell(6).setCellValue("便签");
        headerRow.createCell(7).setCellValue("描述");
        // 填充数据
        int rowNum = 1;
        for (PlcDevice device : devices) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(device.getPlcDeviceType());
            row.createCell(1).setCellValue(device.getPlcDeviceName());
            row.createCell(2).setCellValue(device.getIpAddress());
            row.createCell(3).setCellValue(device.getPort());
            row.createCell(4).setCellValue(device.getConnectionStatus() != null && device.getConnectionStatus()?"在线":"离线");
            row.createCell(5).setCellValue(device.getLastCommunication() != null ? dateFormat.format(device.getLastCommunication()) : "");
            row.createCell(6).setCellValue(device.getPlcTag());
            row.createCell(7).setCellValue(device.getDescription());
        }

        // 将工作簿写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        // 创建ByteArrayResource
        ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray());

        // 设置HTTP头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=plc_devices.xlsx");

        // 返回ResponseEntity
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .body(resource);
    }

//    @PostMapping("/export")
//    public ResponseEntity<ByteArrayResource> exportDevices(@RequestBody @Valid SearchDevicesRequest request) throws IOException {
//        // 获取所有匹配的记录，不进行分页
//        List<PlcDevice> devices = plcDeviceService.searchAllDevices(request.getType(), request.getName(), request.getTag(), request.getStatus());
//
//        // 创建工作簿和工作表
//        Workbook workbook = new XSSFWorkbook();
//        Sheet sheet = workbook.createSheet("PLC Devices");
//
//        // 创建标题行
//        Row headerRow = sheet.createRow(0);
//        headerRow.createCell(0).setCellValue("Device Name");
//        headerRow.createCell(1).setCellValue("IP Address");
//        headerRow.createCell(2).setCellValue("Port");
//        headerRow.createCell(3).setCellValue("Connection Status");
//        headerRow.createCell(4).setCellValue("Last Communication");
//        headerRow.createCell(5).setCellValue("Device Type");
//        headerRow.createCell(6).setCellValue("PLC Tag");
//        headerRow.createCell(7).setCellValue("Description");
//
//        // 设置日期格式
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//        // 填充数据
//        int rowNum = 1;
//        for (PlcDevice device : devices) {
//            Row row = sheet.createRow(rowNum++);
//            row.createCell(0).setCellValue(device.getPlcDeviceName());
//            row.createCell(1).setCellValue(device.getIpAddress());
//            row.createCell(2).setCellValue(device.getPort());
//            row.createCell(3).setCellValue(device.getConnectionStatus() != null ? device.getConnectionStatus().toString() : "");
//            row.createCell(4).setCellValue(device.getLastCommunication() != null ? dateFormat.format(device.getLastCommunication()) : "");
//            row.createCell(5).setCellValue(device.getPlcDeviceType());
//            row.createCell(6).setCellValue(device.getPlcTag());
//            row.createCell(7).setCellValue(device.getDescription());
//        }
//
//        // 将工作簿写入字节数组
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        workbook.write(outputStream);
//        workbook.close();
//
//        // 创建ByteArrayResource
//        ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray());
//
//        // 设置HTTP头
//        HttpHeaders headers = new HttpHeaders();
//        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=plc_devices.xlsx");
//
//        // 返回ResponseEntity
//        return ResponseEntity.ok()
//                .headers(headers)
//                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
//                .body(resource);
//    }


    @PostMapping("/import")
    public ApiPageResponse<Map<String, Boolean>> importDevices(@RequestParam("file") MultipartFile file) {
        try {
            List<PlcDevice> devices = readExcelFile(file);
            Map<String, Boolean> results = plcDeviceService.saveOrUpdateDevices(devices);
            return new ApiPageResponse<>(
                    true,
                    "导入操作完成",
                    null,
                    results.size(),
                    results.size(),
                    results.size(),
                    1
            );
        } catch (Exception e) {
            return new ApiPageResponse<>(false, "数据格式错误: " + e.getMessage(), null,0, 0, 0, 1);
        }
    }

    private boolean validateHeaderRow(Row headerRow) {
        String[] expectedHeaders = {
                "设备类型", "设备名称", "ip", "端口",
                "在线状态", "最后通信时间", "便签", "描述"
        };

        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            if (cell == null || !expectedHeaders[i].equals(cell.getStringCellValue())) {
                return false;
            }
        }
        return true;
    }

    private List<PlcDevice> readExcelFile(MultipartFile file) throws IOException {
        List<PlcDevice> devices = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        // 校验标题行
        Row headerRow = sheet.getRow(0);
        if (headerRow == null || !validateHeaderRow(headerRow)) {
            workbook.close();
            throw new IllegalArgumentException("Excel文件的标题行不符合预期");
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue; // 跳过标题行

            PlcDevice device = new PlcDevice();
            device.setPlcDeviceType(getCellValueAsString(row.getCell(0)));

            device.setPlcDeviceName(getCellValueAsString(row.getCell(1)));
            device.setIpAddress(getCellValueAsString(row.getCell(2)));
            device.setPort(getCellValueAsInteger(row.getCell(3)));
            device.setConnectionStatus(getCellValueAsBoolean(row.getCell(4)));
            device.setLastCommunication(getCellValueAsDate(row.getCell(5), dateFormat));
            device.setPlcTag(getCellValueAsString(row.getCell(6)));
            device.setDescription(getCellValueAsString(row.getCell(7)));

            validateDevice(device);
            devices.add(device);
        }

        workbook.close();
        return devices;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case STRING: return cell.getStringCellValue();
            case NUMERIC: return String.valueOf(cell.getNumericCellValue());
            default: return null;
        }
    }

    private Integer getCellValueAsInteger(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case NUMERIC: return (int) cell.getNumericCellValue();
            case STRING: return Integer.parseInt(cell.getStringCellValue());
            default: return null;
        }
    }

    private Boolean getCellValueAsBoolean(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case BOOLEAN: return cell.getBooleanCellValue();
            case STRING: return Boolean.parseBoolean(cell.getStringCellValue());
            default: return null;
        }
    }

    private Date getCellValueAsDate(Cell cell, SimpleDateFormat dateFormat) {
        if (cell == null) return null;
        try {
            switch (cell.getCellType()) {
                case NUMERIC: return cell.getDateCellValue();
                case STRING: return dateFormat.parse(cell.getStringCellValue());
                default: return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    private void validateDevice(PlcDevice device) {
        if (device.getPlcDeviceName() == null || device.getPlcDeviceName().trim().isEmpty()) {
            throw new IllegalArgumentException("设备名称不能为空");
        }
        if (device.getIpAddress() == null || device.getIpAddress().trim().isEmpty()) {
            throw new IllegalArgumentException("IP地址不能为空");
        }
        if (device.getPort() == null) {
            throw new IllegalArgumentException("端口不能为空");
        }
        if (device.getPlcDeviceType() == null || device.getPlcDeviceType().trim().isEmpty()) {
            throw new IllegalArgumentException("设备类型不能为空");
        }
    }
}
