@startuml
class "设备报警上传(SendAlarm)" as AlarmReport {
    Topic: M2M_DEVICE_ALARM_REPORT
    消息量/天: 33411
    --
    <b>消息参数</b>
    iotId: String [必选]              // 物联编码
    equipmentSn: String [可选]        // 设备唯一编码
    alarmSn: String [可选]            // 报警序列号
    alarmMethod: String [必选]        // 报警方法
    alarmLevel: String [可选]         // 报警等级
    alarmCode: String [必选]          // 报警状态码
    alarmDesc: String [必选]          // 报警描述
    alarmTime: String [必选]          // 报警时间(yyyy-MM-dd HH:mm:ss)
}

note right of AlarmReport
报警方法(alarmMethod):
- AlarmSet: 产生警报
- AlarmClear: 警报消除

报警等级(alarmLevel):
- info: 提示信息
- warning: 警告信息
- error: 错误信息

报警码(alarmCode)示例:
- 001001: 安全门打开
- 002: 马达过载
注：需提供报警对照码全集清单
end note

note bottom of AlarmReport
消息示例:
{
    "iotId": "AOI00196",
    "equipmentSn": "A00000200000",
    "alarmSn": "SCYTE002123",
    "alarmMethod": "AlarmSet",
    "alarmLevel": "info",
    "alarmCode": "001",
    "alarmDesc": "测试报警001",
    "alarmTime": "2023-06-25 14:51:43"
}
end note
@enduml