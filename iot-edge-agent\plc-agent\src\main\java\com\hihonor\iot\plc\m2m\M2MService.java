/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.m2m;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.hihonor.iot.plc.Util;
import com.hihonor.iot.plc.boot.MasterOnly;
import com.hihonor.iot.plc.m2m.mode.AlarmRequest;
import com.hihonor.iot.plc.m2m.mode.HeartbeatRequest;
import com.hihonor.iot.plc.m2m.mode.M2MResponse;
import com.hihonor.iot.plc.m2m.mode.StatusChangeRequest;

import lombok.Getter;
import lombok.Setter;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
public class M2MService {

    @Value("${m2m.url}")
    @Getter
    @Setter
    String apiUrl;

    @Autowired
    @Lazy
    @Getter
    @Setter
    WebClient webClient;

    /**
     * 发送告警
     *
     * @param request 告警请求
     * @return 告警响应
     */
    @MasterOnly
    public M2MResponse sendHeartbeat(HeartbeatRequest request) {
        String url = "http://" + apiUrl + "/api/new/heartBeat";
        return sendRequest(url, request);
    }

    public boolean sendHeartbeat(String iotId, String heartbeatSpan) {
        try {
            HeartbeatRequest request = new HeartbeatRequest();
            request.setIotId(iotId);
            request.setHeartbeatSpan(heartbeatSpan);
            request.setTimestamp(Util.getcurrentTime());

            String url = "http://" + apiUrl + "/api/new/heartBeat";
            M2MResponse response = sendRequest(url, request);

            // 假设 'success' 字段为 "true" 表示成功
            return "true".equalsIgnoreCase(response.getSuccess());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 发送状态变更
     *
     * @param request 状态变更请求
     * @return 状态变更响应
     */
    public M2MResponse sendStatusChange(StatusChangeRequest request) {
        String url = "http://" + apiUrl + "/api/new/statusChange";
        request.setStatusTime(Util.getcurrentTime());
        return sendRequest(url, request);
    }

    /**
     * 发送告警
     *
     * @param request 告警请求
     * @return 告警响应
     */
    @MasterOnly
    public M2MResponse sendAlarm(AlarmRequest request) {
        String url = "http://" + apiUrl + "/api/new/alarm";
        return sendRequest(url, request);
    }

    private <T> M2MResponse sendRequest(String url, T request) {
        try {
            // 验证必选字段是否为空
            return webClient.post()
                    .uri(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(M2MResponse.class)
                    .block(); // 同步阻塞方式
        } catch (Exception e) {
            return null; // 或者根据需要返回一个默认的 M2MResponse 对象
        }
    }

    public static void main(String[] args) {
        // 创建 WebClient 实例
        WebClient webClient = WebClient.create(); // 根据需要配置 WebClient

        // 创建 M2MService 实例
        M2MService m2mService = new M2MService();
        m2mService.setWebClient(webClient); // 假设您有一个 setter 方法来设置 WebClient
        m2mService.setApiUrl("iot-mfg-agent.test.hihonor.com"); // 替换为实际的 API URL

        // 测试 sendHeartbeat
        String iotId = "A2309000149"; // 替换为实际的 IoT ID
        String heartbeatSpan = "60"; // 示例间隔（秒）
        boolean heartbeatSuccess = m2mService.sendHeartbeat(iotId, heartbeatSpan);
        System.out.println("心跳发送成功: " + heartbeatSuccess);

        // 测试 sendStatusChange
        StatusChangeRequest statusChangeRequest = new StatusChangeRequest();
        statusChangeRequest.setIotId(iotId);
        statusChangeRequest.setStatusGeneral("RUN"); // 示例状态
        M2MResponse statusChangeResponse = m2mService.sendStatusChange(statusChangeRequest);
        System.out.println("状态已更改: " + statusChangeResponse.toString());
    }
}