@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

Container(main_app, "主控制应用", "Java, Spring Boot", "负责管理整个系统的逻辑和流程") {
    Component(camera_network_module, "相机网络模块", "处理与相机的网络连接和协议")
    Component(plate_network_module, "板台网络模块", "处理与板台的网络连接和协议")
    Component(network_module, "网络模块", "连接管理和协议处理", "确保与外部设备的稳定连接")
    Component(heartbeat_module, "设备心跳模块", "监控设备的在线状态")
    Component(device_config_module, "设备信息配置组件", "管理设备信息的增删改查", "处理板台和相机之间的绑定关系")
    Component(log_management_module, "日志管理模块", "存储和检索设备操作日志")
    Component(device_control_module, "设备控制模块", "实现设备控制功能", "相机读取条码，通知板台通讯等")
}

Rel(device_control_module, camera_network_module, "调用", "读取条码")
Rel(device_control_module, plate_network_module, "通知", "控制板台")
Rel(device_config_module, network_module, "更新设备状态", "通过网络模块进行通信")
Rel(heartbeat_module, network_module, "发送心跳信号", "监控设备状态")
Rel(device_control_module, log_management_module, "记录操作日志", "存储设备操作日志")

@enduml