/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.zk;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 主节点注解处理切面
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Aspect
@Component
@Slf4j
@DependsOn("zkElectionManager")
public class LeaderAspect {

    @Autowired
    private ZkElectionManager zkElectionManager;



    /**
     * 拦截带有@RunAsLeader注解的方法，实现仅在主节点执行的功能
     *
     * @param joinPoint   切点
     * @param runAsLeader 注解
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("@annotation(runAsLeader)")
    public Object runOnLeader(ProceedingJoinPoint joinPoint, RunAsLeader runAsLeader) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();
        long waitTimeoutMs = runAsLeader.waitTimeoutMs();
        boolean throwException = runAsLeader.throwException();

        log.debug("检查是否是主节点，方法: {}", methodName);

        // 无限等待模式
        if (waitTimeoutMs < 0) {
            while (true) {
                if (zkElectionManager.isLeader()) {
                    log.debug("作为主节点执行方法: {}", methodName);
                    return joinPoint.proceed();
                }

                if (throwException) {
                    throw new NotLeaderException("当前节点不是主节点，方法 " + methodName + " 只能在主节点执行");
                }

                log.debug("不是主节点，等待成为主节点以执行方法: {}", methodName);
                Thread.sleep(1000); // 等待1秒再检查
            }
        }
        // 有超时限制的等待模式
        else {
            long startTime = System.currentTimeMillis();

            while (System.currentTimeMillis() - startTime < waitTimeoutMs) {
                if (zkElectionManager.isLeader()) {
                    log.debug("作为主节点执行方法: {}", methodName);
                    return joinPoint.proceed();
                }

                // 避免CPU过度消耗
                Thread.sleep(Math.min(1000, waitTimeoutMs - (System.currentTimeMillis() - startTime)));
            }

            if (throwException) {
                throw new NotLeaderException("等待成为主节点超时，方法 " + methodName + " 无法执行");
            }

            log.warn("等待成为主节点超时，方法 {} 未执行", methodName);
            return null;
        }
    }

    /**
     * 不是主节点异常
     */
    public static class NotLeaderException extends RuntimeException {
        private static final long serialVersionUID = 1L;

        public NotLeaderException(String message) {
            super(message);
        }
    }
}