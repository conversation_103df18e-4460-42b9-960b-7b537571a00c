/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plcbase;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
public enum DeviceType {
    PLATE("plate"),
    CAMERA("camera");

    private final String value;

    /**
     * 构造函数，初始化设备类型的字符串值。
     * @param value 设备类型的字符串表示。
     */
    DeviceType(String value) {
        this.value = value;
    }

    /**
     * 获取设备类型的字符串表示。
     * @return 设备类型的字符串值。
     */
    public String getValue() {
        return this.value;
    }
}

