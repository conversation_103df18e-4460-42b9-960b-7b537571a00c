/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import java.util.List;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
public class BatchWriteTransferBoardPropertiesResponse {
    private List<TransferBoardWriteResult> transferBoardWriteResults; // 过板台写入结果列表

    @Data
    public static class TransferBoardWriteResult {
        private String name; // 过板台名称
        private boolean success; // 写入是否成功
        private String message; // 写入结果信息


        public  TransferBoardWriteResult(String name, boolean success, String message) {
            this.name = name;
            this.success = success;
            this.message = message;
        }
    }
}

