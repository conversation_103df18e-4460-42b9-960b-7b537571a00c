/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.zk;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ZooKeeper集群状态控制器
 * 提供API查看当前主备节点状态
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@RestController
@RequestMapping("/plc/zk")
public class ZkStatusController {

    @Autowired
    private ZkElectionManager zkElectionManager;

    /**
     * 获取当前集群状态，包括主节点和备节点信息
     *
     * @return 集群状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getClusterStatus() {
        Map<String, Object> result = new HashMap<>();

        // 获取主节点信息
        String leaderId = zkElectionManager.getLeaderId();
        result.put("leaderId", leaderId);

        // 获取本地节点信息
        String localNodeId = zkElectionManager.getLocalNodeId();
        result.put("localNodeId", localNodeId);
        result.put("isLeader", zkElectionManager.isLeader());

        // 获取所有参与节点
        List<String> allNodes = zkElectionManager.getParticipants();
        result.put("totalNodes", allNodes.size());

        // 获取备用节点列表（所有不是主节点的节点）
        List<String> standbyNodes = allNodes.stream()
                .filter(node -> !node.equals(leaderId))
                .collect(Collectors.toList());
        result.put("standbyNodes", standbyNodes);

        // 返回完整节点列表
        result.put("allNodes", allNodes);

        return ResponseEntity.ok(result);
    }

    /**
     * 获取简化版集群状态，只包含主节点和备节点IP
     * 
     * @return 简化版集群状态
     */
    @GetMapping("/simple-status")
    public ResponseEntity<Map<String, Object>> getSimpleClusterStatus() {
        Map<String, Object> result = new HashMap<>();

        // 获取主节点信息
        String leaderId = zkElectionManager.getLeaderId();
        result.put("leaderIp", leaderId != null ? leaderId.split(":")[0] : "未知");

        // 获取所有参与节点
        List<String> allNodes = zkElectionManager.getParticipants();

        // 备用节点IP列表
        List<String> standbyIps = allNodes.stream()
                .filter(node -> !node.equals(leaderId))
                .map(node -> node.split(":")[0])
                .collect(Collectors.toList());
        result.put("standbyIps", standbyIps);

        // 本地节点信息
        String localNodeId = zkElectionManager.getLocalNodeId();
        result.put("localIp", localNodeId.split(":")[0]);
        result.put("isLeader", zkElectionManager.isLeader());

        return ResponseEntity.ok(result);
    }
}