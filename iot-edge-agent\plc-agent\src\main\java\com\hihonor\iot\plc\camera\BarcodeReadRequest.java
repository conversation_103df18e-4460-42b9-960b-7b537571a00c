/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Data
public class BarcodeReadRequest {
    @NotEmpty(message = "设备名称列表不能为空")
    private List<String> deviceNames;

    // Getters and setters

}
