/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;

import lombok.Data;
import reactor.core.publisher.Mono;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Service
public class BarcodeService {

    private WebClient webClient;
    @Value("${barcode.validation.url}")
    private String url;

    public BarcodeService() {
        this.webClient = WebClient.builder().build();

    }

    // public Boolean validateBarcode(ScanRequest request) {
    // return webClient.post()
    // .uri(url)
    // .header("Content-Type", "application/json")
    // .bodyValue(request)
    // .retrieve()
    // .bodyToMono(BarcodeResponse.class)
    // .map(response -> "1".equals(response.getIsAllowed()))
    // .onErrorReturn(false)
    // .block();
    // }

    public BarcodeValidationResult validateBarcode(ScanRequest request) {
        // 构建包含资产ID的URL
        String requestUrl = url;
        if (request.getResourceId() != null && !request.getResourceId().isEmpty()) {
            requestUrl = UriComponentsBuilder.fromUriString(url)
                    .queryParam("resourceId", request.getResourceId())
                    .toUriString();
        }

        return webClient.post()
                .uri(requestUrl)
                .header("Content-Type", "application/json")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(BarcodeResponse.class)
                .map(response -> {
                    boolean isValid = "1".equals(response.getIsAllowed());
                    String description = response.getDescription();
                    return new BarcodeValidationResult(isValid, description);
                })
                .onErrorResume(error -> {
                    // 处理错误情况
                    return Mono.just(new BarcodeValidationResult(false, "验证过程中发生错误: " + error.getMessage()));
                })
                .block();
    }

    @Data
    private static class BarcodeResponse {
        private String code;
        private String msg;
        private String isAllowed;
        private Object[] data;
        private String description;
    }

    // Getters and Setters
}
