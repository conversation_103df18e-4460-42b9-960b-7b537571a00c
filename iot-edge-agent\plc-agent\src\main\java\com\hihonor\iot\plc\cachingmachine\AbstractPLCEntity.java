/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine;

import java.security.SecureRandom;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.common.config.BaseConfig;
import com.hihonor.iot.edge.sdk.entity.EntityThing;
import com.hihonor.iot.plc.Util;
import com.hihonor.iot.plc.cachingmachine.repo.PlateDevice;
import com.hihonor.iot.plc.cachingmachine.repo.PlateDeviceService;
import com.hihonor.iot.plc.m2m.M2MService;
import com.hihonor.iot.plc.m2m.mode.AlarmRequest;
import com.hihonor.iot.plc.m2m.mode.M2MResponse;
import com.hihonor.iot.plc.thing.ThingInterface;
import com.hihonor.iot.plc.thingworx.service.GetProertyQuality;
import com.hihonor.iot.plc.thingworx.service.GetPropertys;
import com.hihonor.iot.plc.thingworx.service.SetPropertys;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Slf4j
@Component
@Scope("prototype")
public abstract class AbstractPLCEntity extends EntityThing implements ThingInterface {

    // 将特定的属性名列表移除，子类将提供这些
    /**
     * 缓存的数据
     */
    protected Map<String, String> cachedData;  // 缓存的数据

    // 其他依赖和属性...
    /**
     * 基础配置
     */
    @Autowired
    protected BaseConfig baseConfig;
    /**
     * 设置属性服务
     */
    @Autowired
    protected SetPropertys setPropertys;
    /**
     * 获取属性服务
     */
    @Autowired
    protected GetPropertys getPropertys;
    /**
     * 获取属性质量服务
     */
    @Autowired
    protected GetProertyQuality getProertyQuality;
    /**
     * 设备服务
     */
    @Autowired
    protected PlateDeviceService plateDeviceService;
    /**
     * M2M服务
     */
    @Autowired
    protected M2MService m2mService;
    /**
     * 随机数生成器
     */
    protected final SecureRandom random = new SecureRandom();

    // 模板方法 - 子类必须提供属性名列表

    /**
     * 获取所有属性名
     *
     * @return 所有属性名
     */
    protected abstract List<String> getAllPropertyNames();

    /**
     * 初始化属性名
     */
    protected abstract void initPropertyNames();


    // 模板方法 - 子类需要实现如何处理数据映射

    /**
     * 处理数据映射
     *
     * @param input 输入数据
     * @return 处理后的数据
     */
    protected abstract Map<String, String> processMap(Map<String, String> input);

    // 模板方法 - 子类需要实现如何检查并发送状态变更

    /**
     * 检查并发送状态变更
     *
     * @param oldMap 旧数据
     * @param newMap 新数据
     */
    public abstract void checkAndSendStatusChange(Map<String, String> oldMap, Map<String, String> newMap);


    /**
     * 检查并发送高级警报
     *
     * @param oldMap 旧数据
     * @param newMap 新数据
     */
    public abstract void checkAndSendAdvancedAlarm(Map<String, String> oldMap, Map<String, String> newMap);

    /**
     * 获取全名
     *
     * @return 全名
     */
    protected abstract String getFullName();


    boolean init(String name) {
        boolean res = false;
        try {
            setName(name);
            log.info("{}：init complete", name);
            cachedData = fetchData();
            res = true;
        } catch (Exception e) {
            log.error("initialize error", e);
            res = true;
        }
        return res;

    }

    @Override
    public void initialize(org.json.JSONObject json) {
        try {
            init(json.getString("name"));
            initPropertyNames();
            log.info("initialize complete");

        } catch (Exception e) {
            log.error("initialize error", e);
        }
    }

    @Override
    public void destroy() {

    }

    @Override
    public void onBind() {

    }

    @Override
    public void unBind() {

    }

    Map<String, String> fetchData() {
        Map<String, String> map = getPropertys.invorke(getName());
        Map<String, String> map1 = processMap(map);
        map1.put("statusTime", Util.getcurrentTime());
        map1.put("iotId", getName());
        return map1;

    }

    // ...其他通用方法和钩子的实现...
    void insertRepo(Map data) {
        PlateDevice plateDevice = new PlateDevice();
        plateDevice.setData(data.toString());
        plateDevice.setDeviceId(getName());
        plateDevice.setTimestamp(new Timestamp(new Date().getTime()));
        plateDeviceService.insert(plateDevice);
    }

    void update() {
        long start = System.currentTimeMillis();
        Map newDate = fetchData();
        checkAndSendAdvancedAlarm(cachedData, newDate);
        checkAndSendStatusChange(cachedData, newDate);
        if (newDate != null) {
            synchronized (this) {
                cachedData = newDate;
            }
        }
        insertRepo(newDate);
        long end = System.currentTimeMillis();
        //使用英文做日志
        log.info("update complete, time: {}ms", end - start);

    }
    // ...其他通用方法...


    @Override
    public void updating(org.json.JSONObject json) {
        update();
    }

    @Override
    public Map<String, String> getAttributes() {
        Map<String, String> res = getValuesSynchronously();
        return res;
    }

    /**
     * 获取同步值
     *
     * @return 同步值
     */
    public synchronized Map<String, String> getValuesSynchronously() {
        // 复制数据以返回
        Map<String, String> dataToReturn = new HashMap<>(cachedData);
        return dataToReturn;
    }

    @Override
    public boolean getOnlineStatus() {
        // 获取两个随机的、不同的索引
        int index1 = random.nextInt(getAllPropertyNames().size());
        int index2;
        do {
            index2 = random.nextInt(getAllPropertyNames().size());
        } while (index1 == index2); // 确保两个索引不同
        List<String> selectPropertyNames = new ArrayList<>();
        selectPropertyNames.add(getAllPropertyNames().get(index1));
        selectPropertyNames.add(getAllPropertyNames().get(index2));
        return getProertyQuality.invorke(getName(), selectPropertyNames);
    }


    @Override
    public boolean sendConfiguration(List<Map<String, Object>> config) {
        String prix = getFullName();
        for (Map<String, Object> item : config) {
            // 把ITEM 中KEY 为name 的entry 的值加上前缀prix
            String name = (String) item.get("name");
            item.put("name", prix + name);
        }
        log.info("sendConfiguration:{}", config);
        return setVales(config);
    }

    /**
     * 设置值
     *
     * @param values 值
     * @return 是否成功
     */
    public boolean setVales(List<Map<String, Object>> values) {
        if (getOnlineStatus()) {
            return setPropertys.invorke(getName(), values);
        }
        return false;
    }

    /**
     * 检查并发送告警
     *
     * @param oldMap        旧数据
     * @param newMap        新数据
     * @param attributeName 属性名
     * @param alarmState    告警状态
     * @param trace         跟踪
     */
    protected void checkAndSendAlarmForAttribute(Map<String, String> oldMap, Map<String, String> newMap, String attributeName, String alarmState, String trace) {
        // 对参数做空值检测
        if (oldMap == null || newMap == null || attributeName == null || alarmState == null) {
            log.error("Error when checking and sending alarm for attribute {}, oldMap:{}, newMap:{}, alarmState:{}", attributeName, oldMap, newMap, alarmState);
            return;
        }

        String oldState = oldMap.getOrDefault(attributeName, null);
        String newState = newMap.getOrDefault(attributeName, null);

        try {
            if (!Objects.equals(alarmState, oldState) && Objects.equals(alarmState, newState)) {
                // 从非告警到告警状态，触发报警
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), trace, true));
                log.info("Alarm triggered for {}: AlarmSet, response: {}", attributeName, response);
            } else if (Objects.equals(alarmState, oldState) && !Objects.equals(alarmState, newState)) {
                // 从告警到非告警状态，触发报警消除
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), trace, false));
                log.info("Alarm triggered for {}: AlarmClear, response: {}", attributeName, response);
            }
        } catch (Exception e) {
            log.error("Error when checking and sending alarm for attribute {}", attributeName, e);
        }
    }

    /**
     * 检查并发送告警
     *
     * @param oldMap        旧数据
     * @param newMap        新数据
     * @param attributeName 属性名
     * @param alarmState    告警状态
     */
    protected void checkAndSendAlarmForAttribute(Map<String, String> oldMap, Map<String, String> newMap, String attributeName, String alarmState) {
        // 参数空值检查
        if (oldMap == null || newMap == null || attributeName == null) {
            log.error("Null parameter(s) provided. oldMap: {}, newMap: {}, attributeName: {}", oldMap, newMap, attributeName);
            return;
        }
        String oldState = oldMap.getOrDefault(attributeName, null);
        String newState = newMap.getOrDefault(attributeName, null);

        try {
            if (!Objects.equals(alarmState, oldState) && Objects.equals(alarmState, newState)) {
                // 从非告警到告警状态，触发报警
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), "", true));
                log.info("Alarm triggered for {}: AlarmSet, response: {}", attributeName, response);
            } else if (Objects.equals(alarmState, oldState) && !Objects.equals(alarmState, newState)) {
                // 从告警到非告警状态，触发报警消除
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), "", false));
                log.info("Alarm triggered for {}: AlarmClear, response: {}", attributeName, response);
            }
        } catch (Exception e) {
            log.error("Error when checking and sending alarm for attribute {}", attributeName, e);
        }
    }


}