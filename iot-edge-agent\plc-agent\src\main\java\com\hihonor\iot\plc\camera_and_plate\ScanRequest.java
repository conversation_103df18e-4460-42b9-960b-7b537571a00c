/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import java.util.List;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Data
public class ScanRequest {
    private String resourceId;
    private String deviceNo;
    private List<ScanResult> scanResult;

    @Data
    public static class ScanResult {
        private String area;
        private String barcode;
    }

    public static ScanRequest createRequest(String deviceNo, String barcode) {
        ScanRequest request = new ScanRequest();
        request.setResourceId(deviceNo);
        request.setDeviceNo(deviceNo);

        ScanResult scanResult = new ScanResult();
        scanResult.setArea("区域A");
        scanResult.setBarcode(barcode);

        request.setScanResult(List.of(scanResult));
        return request;
    }
}