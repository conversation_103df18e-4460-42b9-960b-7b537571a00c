/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate.request;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Data
public class SearchCriteria {
    @Min(value = 1, message = "页码必须大于等于1")
    private int pageNum = 1;

    @Min(value = 1, message = "每页大小必须大于等于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private int pageSize = 10;

    private String cameraDeviceName;
    private String plateDeviceName;

    // Existing getters and setters

}