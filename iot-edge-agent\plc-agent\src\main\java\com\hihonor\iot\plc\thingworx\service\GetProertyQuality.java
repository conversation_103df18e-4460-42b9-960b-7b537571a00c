/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.thingworx.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.common.manager.EdgeManager;
import com.hihonor.iot.plc.thing.ThingManagement;
import com.hihonor.iot.plc.thingworx.ThingworxService;
import com.thingworx.communications.client.ConnectedThingClient;
import com.thingworx.types.InfoTable;
import com.thingworx.types.collections.ValueCollection;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Component
@Slf4j
public class GetProertyQuality {
    /**
     * thingworxService
     */
    @Autowired
    protected ThingworxService thingworxService;
    /**
     * thingManagement
     */
    @Autowired
    protected ThingManagement thingManagement;
    /**
     * edgeManager
     */
    @Autowired
    protected EdgeManager edgeManager;
    /**
     * client
     */
    @Autowired
    protected ConnectedThingClient client;

    /**
     * 获取属性质量
     *
     * @param thingName    设备名称
     * @param propertyName 属性名称
     * @return true:GOOD false:BAD
     */
    public boolean invorke(String thingName, String propertyName) {
        try {
            ValueCollection valueCollection = new ValueCollection();
            valueCollection.SetStringValue("propertyName", propertyName);
            InfoTable infoTable = edgeManager.invokeService(thingName, "GetPropertyQuality", valueCollection);
            ValueCollection res = infoTable.getRow(0);
            String succ = res.getStringValue("Quality");
            if (succ.equals("GOOD")) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            log.error("invorke error: {}", ex.getMessage());
            return false;
        }
    }

    /**
     * 获取属性质量
     *
     * @param thingName    设备名称
     * @param propertyName 属性名称
     * @return true:GOOD false:BAD
     */
    public boolean invorke(String thingName, List<String> propertyName) {
        for (String propery : propertyName) {
            if (!invorke(thingName, propery)) {
                return false;
            }
        }
        return true;

    }


}
