/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate.BarcodeValidatorTest;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
class BarcodeValidationResult {
    private boolean valid;
    private String message;

    public BarcodeValidationResult(boolean valid, String message) {
        this.valid = valid;
        this.message = message;
    }

    public boolean isValid() {
        return valid;
    }

    public String getMessage() {
        return message;
    }
}