/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.deviceLog;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("plc_operation_log")
public class PlcOperationLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String deviceType;
    private String deviceName;
    private Boolean onlineStatus;
    private String methodName;
    private String methodParams;
    private String userId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date operationTime;

    private Integer executionTime;
    private String returnValue;
    private String exceptionMessage;
}