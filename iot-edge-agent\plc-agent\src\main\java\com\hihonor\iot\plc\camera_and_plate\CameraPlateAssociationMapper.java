/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Mapper
public interface CameraPlateAssociationMapper extends BaseMapper<CameraPlateAssociation> {
    /**
     * 根据相机名获取关联的过板台设备名。
     *
     * @param cameraDeviceName 相机设备名
     * @return 过板台设备名
     */
    @Select("SELECT plate_device_name FROM iot_admin.camera_plate_association_by_name WHERE camera_device_name = #{cameraDeviceName}")
    String getPlateDeviceNameByCameraName(String cameraDeviceName);

    /**
     * 根据过板台设备名获取关联的相机名。
     *
     * @param plateDeviceName 过板台设备名
     * @return 相机设备名
     */
    @Select("SELECT * FROM iot_admin.camera_plate_association_by_name WHERE plate_device_name = #{plateDeviceName}")
    List<CameraPlateAssociation> getCameraDeviceNameByPlateName(String plateDeviceName);


    /**
     * 插入或更新相机与过板台的关联信息。
     * 如果存在相同的相机名，则更新关联的过板台设备名和描述；如果不存在，则插入新的记录。
     *
     * @param association 相机与过板台的关联信息实体
     * @return 影响的行数
     */
    @Insert("INSERT INTO iot_admin.camera_plate_association_by_name (camera_device_name, plate_device_name, association_description,track_number) " +
            "VALUES (#{cameraDeviceName}, #{plateDeviceName}, #{associationDescription},#{trackNumber}) " +
            "ON CONFLICT (camera_device_name) DO UPDATE SET " +
            "plate_device_name = EXCLUDED.plate_device_name, " +
            "association_description = EXCLUDED.association_description")
    int upsertCameraPlateAssociation(CameraPlateAssociation association);


    /**
     * 根据相机名和过板台设备名进行模糊查询，并支持分页。
     * 如果参数为空或空字符串，则不使用该条件。
     *
     * @param page             分页对象
     * @param cameraDeviceName 相机设备名
     * @param plateDeviceName  过板台设备名
     * @return 分页后的关联信息列表
     */
    @SelectProvider(type = CameraPlateAssociationSqlProvider.class, method = "selectByCameraOrPlateName")
    IPage<CameraPlateAssociation> selectByCameraOrPlateName(Page<CameraPlateAssociation> page,
                                                            @Param("cameraDeviceName") String cameraDeviceName,
                                                            @Param("plateDeviceName") String plateDeviceName);

    /**
     * 查询相机与过板台的关联信息。
     */
    class CameraPlateAssociationSqlProvider {
        /**
         * 根据相机设备名或过板台设备名查询相机与过板台的关联信息。
         *
         * @param cameraDeviceName 相机设备名，如果为空则不参与查询
         * @param plateDeviceName  过板台设备名，如果为空则不参与查询
         * @return 返回生成的SQL查询字符串
         */
        public String selectByCameraOrPlateName(@Param("cameraDeviceName") String cameraDeviceName,
                                                @Param("plateDeviceName") String plateDeviceName) {
            return new SQL() {{
                SELECT("*");
                FROM("iot_admin.camera_plate_association_by_name");
                if (cameraDeviceName != null && !cameraDeviceName.isEmpty()) {
                    WHERE("camera_device_name LIKE CONCAT('%', #{cameraDeviceName}, '%')");
                }
                if (plateDeviceName != null && !plateDeviceName.isEmpty()) {
                    WHERE("plate_device_name LIKE CONCAT('%', #{plateDeviceName}, '%')");
                }
            }}.toString();
        }
    }

    /**
     * 根据相机设备名或过板台设备名查询所有相机与过板台的关联记录
     *
     * @param cameraDeviceName 相机设备名，可为空，若不为空则进行模糊匹配
     * @param plateDeviceName  过板台设备名，可为空，若不为空则进行模糊匹配
     * @return 返回查询到的相机与过板台的关联记录列表
     */
    @Select("<script>" +
            "SELECT * FROM iot_admin.camera_plate_association_by_name " +
            "WHERE 1=1 " +
            "<if test='cameraDeviceName != null and cameraDeviceName.trim() != \"\"'>" +
            "AND camera_device_name LIKE CONCAT('%', #{cameraDeviceName}, '%') " +
            "</if>" +
            "<if test='plateDeviceName != null and plateDeviceName.trim() != \"\"'>" +
            "AND plate_device_name LIKE CONCAT('%', #{plateDeviceName}, '%') " +
            "</if>" +
            "</script>")
    List<CameraPlateAssociation> selectAllByCameraOrPlateName(@Param("cameraDeviceName") String cameraDeviceName,
                                                              @Param("plateDeviceName") String plateDeviceName);


    /**
     * 根据相机设备名删除关联记录。
     *
     * @param cameraDeviceName 相机设备名
     * @return 影响的行数
     */
    @Delete("DELETE FROM iot_admin.camera_plate_association_by_name WHERE camera_device_name = #{cameraDeviceName}")
    int deleteByCameraDeviceName(@Param("cameraDeviceName") String cameraDeviceName);


    /**
     * 根据相机名称列表查询相机实体。
     *
     * @param cameraDeviceNames 相机设备名称列表
     * @return 相机实体列表
     */
    @Select("<script>" +
            "SELECT * FROM iot_admin.camera_plate_association_by_name " +
            "WHERE camera_device_name IN " +
            "<foreach item='name' collection='cameraDeviceNames' open='(' separator=',' close=')'>" +
            "#{name}" +
            "</foreach>" +
            "</script>")
    List<CameraPlateAssociation> selectByCameraDeviceNames(@Param("cameraDeviceNames") List<String> cameraDeviceNames);

    /**
     * 根据过板台设备名列表查询未与车牌识别设备关联的相机设备名列表。
     *
     * @param cameraName 相机设备名，用于模糊查询，可以为空
     * @return 返回一个包含未与车牌识别设备关联的相机设备名的列表
     */
    /**
     * 根据过板台设备名列表查询相机实体。
     *
     * @param cameraName
     * @return
     */
    @Select("<script>" +
            "SELECT pd.plc_device_name " +
            "FROM iot_admin.plc_device pd " +
            "WHERE pd.plc_device_type = 'camera' " +
            "AND pd.plc_device_name NOT IN (" +
            "    SELECT camera_device_name " +
            "    FROM iot_admin.camera_plate_association_by_name" +
            ") " +
            "<if test='cameraName != null and cameraName.trim() != \"\"'>" +
            "    AND pd.plc_device_name LIKE CONCAT('%', #{cameraName}, '%')" +
            "</if>" +
            "</script>")
    List<String> getUnboundCameraNames(@Param("cameraName") String cameraName);

    /**
     * 根据轨道号和相机名称进行模糊查询未绑定的相机设备名。
     *
     * @param trackNumber 轨道号
     * @param plateName   过板台设备名
     * @return 未绑定的相机设备名列表
     */
    @Select("<script>" +
            "SELECT pd.plc_device_name " +
            "FROM iot_admin.plc_device pd " +
            "WHERE pd.plc_device_type = 'plate' " +
            "AND pd.plc_device_name NOT IN (" +
            "    SELECT cpa.plate_device_name " +
            "    FROM iot_admin.camera_plate_association_by_name cpa " +
            "    WHERE cpa.track_number = #{trackNumber}" +
            ") " +
            "<if test='plateName != null and plateName.trim() != \"\"'>" +
            "    AND pd.plc_device_name LIKE CONCAT('%', #{plateName}, '%')" +
            "</if>" +
            "</script>")
    List<String> getUnboundPlateNamesByTrackAndName(@Param("trackNumber") Integer trackNumber,
                                                    @Param("plateName") String plateName);


}