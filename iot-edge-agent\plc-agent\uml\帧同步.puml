@startuml
actor Client
entity Server

Client -> Client: 判断当前帧 F 是否为关键帧 K1
alt 是 K1
    Client -> Client: 检查是否收到 K1 的 UPDATE 数据
    alt 收到 UPDATE 数据
        Client -> Client: 采集当前 K1 的输入作为 CTRL 数据
        Client -> Server: 发送 CTRL 数据 (Ctrl-K)
        Server -> Server: 收集所有客户端的 CTRL 数据 (Ctrl-K)
        Server -> Server: 计算下一个关键帧 K2 的 UPDATE 和 K3
        Server -> Client: 发送 UPDATE 数据
        Client -> Client: 从 UPDATE K1 中获取 K2 和输入数据 I
        Client -> Client: 使用 I 处理虚拟输入
        Client -> Client: 更新 K1 = K2
        Client -> Client: 执行当前帧逻辑
    else 否
        Client -> Client: 等待 K1 的 UPDATE 数据
        note right of Client: 继续等待 UPDATE 数据
    end
else 否
    Client -> Client: 跳转到当前帧逻辑
end

@enduml