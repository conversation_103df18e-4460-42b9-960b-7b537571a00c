/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Slf4j
public class Reconnect<PERSON><PERSON>ler implements DeviceStatusListener {
    private final NettyScannerClient client;
    private final String host;
    private final int port;

    public ReconnectHandler(String host, int port) {
        this.client = NettyScannerClient.getInstance();
        this.host = host;
        this.port = port;
    }

    @Override
    public void onDeviceConnected() {
        log.info("设备已连接。");
    }

    @Override
    public void onDeviceDisconnected() {
        log.info("设备已断开。正在尝试重新连接...");
        client.reconnect(host, port);
    }

}
