/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import java.util.HashMap;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import com.hihonor.iot.plc.cachingmachine.controller.ApiResponse;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesResponse;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesResponse;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@RestController
@RequestMapping("/plc/plate")
public class PlateController {

    @Autowired
    private PlateManager plateManager;

    @Autowired
    private PlateStatusConfig plateStatusConfig;

    @GetMapping("/status/{deviceId}")
    public PlateStatus getPlateStatus(@PathVariable String deviceId) {
        // 使用 PlateManager 获取板台
        Plate plate = plateManager.getPlate(deviceId);
        if (plate == null) {
            throw new IllegalArgumentException("Invalid device ID: " + deviceId);
        }

        // 调用 PlateStatusConfig 的 readDeviceAttributes 方法
        return plateStatusConfig.readDeviceAttributes(plate);
    }

    /**
     * 批量获取过板台属性信息。
     *
     * @param request 包含过板台名称和属性名称列表的请求对象
     * @return 包含属性值的响应对象
     */
    @PostMapping("/batchReadProperties")
    public ApiResponse<BatchReadTransferBoardPropertiesResponse> batchReadTransferBoardProperties(
            @RequestBody @Valid BatchReadTransferBoardPropertiesRequest request) {
        BatchReadTransferBoardPropertiesResponse response = plateManager.batchReadPlateProperties(request);
        return new ApiResponse<>(true, "批量获取过板台属性信息成功", response);
    }

    /**
     * 批量写入过板台属性值。
     *
     * @param request 包含过板台名称和属性名称及其值的请求对象
     * @return 包含写入结果的响应对象
     */
    @PostMapping("/batchWriteProperties")
    public ApiResponse<BatchWriteTransferBoardPropertiesResponse> batchWriteTransferBoardProperties(
            @RequestBody @Valid BatchWriteTransferBoardPropertiesRequest request) {
        BatchWriteTransferBoardPropertiesResponse response = plateManager.batchWriteTransferBoardProperties(request);
        return new ApiResponse<>(true, "批量写入过板台属性值成功", response);
    }

    @PostMapping("/sendBoardingStatus")
    public Map<String, Object> sendBoardingStatus(@Valid @RequestBody BoardingStatusRequest request) {
        boolean result = plateManager.sendBoardingStatus(request.getDeviceNo(), request.getOperate(),
                request.getSubNo(), request.getDescription());
        Map<String, Object> response = new HashMap<>();
        if (result) {
            response.put("code", 200);
            response.put("msg", "success");
        } else {
            response.put("code", 500);
            response.put("msg", "failed");
        }
        return response;
    }

    @PostMapping("/writeValue")
    public ApiResponse<Boolean> writeRTypePointValue(@Valid @RequestBody WriteValueRequest request) {
        boolean result = plateManager.writePointValue(request.getPlateName(), request.getAddress(), request.getValue());
        String message = result ? "写入成功" : "写入失败";
        return new ApiResponse<>(result, message, result);
    }

    @PostMapping("/readData")
    public ApiResponse<String> readDataFromPlc(@Valid @RequestBody ReadDataRequest request) {
        String data = plateManager.readDataFromPlc(request.getPlateName(), request.getStartAddress(),
                request.getEndAddress());
        boolean success = !data.isEmpty();
        String message = success ? "读取成功" : "读取失败";
        return new ApiResponse<>(success, message, data);
    }

    /**
     * 控制第一轨道禁止通行并在一秒后复位
     * 
     * @param request 包含设备ID的请求对象
     * @return 操作结果
     */
    @PostMapping("/controlTrack1Prohibition")
    public Track1ProhibitionResponse controlTrack1Prohibition(@Valid @RequestBody Track1ProhibitionRequest request) {
        try {
            // 获取板台
            Plate plate = plateManager.getPlate(request.getResourceId());
            if (plate == null) {
                throw new ResponseStatusException(null, "找不到ID为 " + request.getResourceId() + " 的设备");
            }

            // 调用控制方法
            boolean result = plate.controlTrack1ProhibitionWithReset();

            // 构建响应
            Track1ProhibitionResponse response = new Track1ProhibitionResponse();
            response.setSuccess(result);
            response.setMessage(result ? "操作成功" : "操作失败");

            return response;

        } catch (Exception e) {
            return new Track1ProhibitionResponse(false, "处理请求时发生错误: " + e.getMessage());

        }
    }

    /**
     * 控制第二轨道禁止通行并在一秒后复位
     * 
     * @param request 包含设备ID的请求对象
     * @return 操作结果
     */
    @PostMapping("/controlTrack2Prohibition")
    public Track2ProhibitionResponse controlTrack2Prohibition(@Valid @RequestBody Track2ProhibitionRequest request) {
        try {
            // 获取板台
            Plate plate = plateManager.getPlate(request.getResourceId());
            if (plate == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "找不到ID为 " + request.getResourceId() + " 的设备");
            }

            // 调用控制方法
            boolean result = plate.controlTrack2ProhibitionWithReset();

            // 构建响应
            Track2ProhibitionResponse response = new Track2ProhibitionResponse();
            response.setSuccess(result);
            response.setMessage(result ? "操作成功" : "操作失败");

            return response;

        } catch (Exception e) {
            return new Track2ProhibitionResponse(false, "处理请求时发生错误: " + e.getMessage());

        }
    }

    @Data
    public static class WriteValueRequest {
        @NotBlank(message = "过板台名称不能为空")
        private String plateName;

        @NotBlank(message = "地址不能为空")
        // @Pattern(regexp = "^R\\d{4}$", message = "地址格式不正确，必须为R后面跟4个数字")
        private String address;

        @NotBlank(message = "值不能为空")
        private String value;
    }

    @Data
    public static class ReadDataRequest {
        @NotBlank(message = "过板台名称不能为空")
        private String plateName;

        @NotBlank(message = "起始地址不能为空")
        // @Pattern(regexp = "^(R\\d{4}|DT\\d+)$", message =
        // "起始地址格式不正确，必须为R后面跟4个数字，或者DT后面跟数字")
        private String startAddress;

        @NotBlank(message = "结束地址不能为空")
        // @Pattern(regexp = "^(R\\d{4}|DT\\d+)$", message =
        // "结束地址格式不正确，必须为R后面跟4个数字，或者DT后面跟数字")
        private String endAddress;
    }

    /**
     * 第一轨道禁止通行请求类
     */
    @Data
    public static class Track1ProhibitionRequest {
        @NotBlank(message = "设备ID不能为空")
        private String resourceId;
    }

    /**
     * 第一轨道禁止通行响应类
     */
    @Data
    public static class Track1ProhibitionResponse {
        private boolean success;
        private String message;

        public Track1ProhibitionResponse() {
        }

        public Track1ProhibitionResponse(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }

    /**
     * 第二轨道禁止通行请求类
     */
    @Data
    public static class Track2ProhibitionRequest {
        @NotBlank(message = "设备ID不能为空")
        private String resourceId;
    }

    /**
     * 第二轨道禁止通行响应类
     */
    @Data
    public static class Track2ProhibitionResponse {
        private boolean success;
        private String message;

        public Track2ProhibitionResponse() {
        }

        public Track2ProhibitionResponse(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }
}
