/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.thing;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.boot.ClusterManager;
import com.hihonor.iot.plc.thingworx.ThingworxService;
import com.hihonor.iot.edge.common.manager.EdgeManager;
import com.hihonor.iot.edge.sdk.entity.EntityThing;
import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.thingworx.communications.client.ConnectedThingClient;
import com.thingworx.relationships.RelationshipTypes;
import com.thingworx.types.InfoTable;
import com.thingworx.types.collections.ValueCollection;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Component
@Slf4j
public class ThingManagement implements Runnable {
    @Autowired
    EdgeManager edgeManager;
    @Autowired
    ConnectedThingClient client;
    @Autowired
    ApplicationContext context;

    /**
     * Thing Map
     */
    public static  final ConcurrentHashMap<String, EntityThing> thingMap = new ConcurrentHashMap<String, EntityThing>();
    @Autowired
    ThingworxService thingworxService;

    @Value("${thingworx.template}")
    String templates = "THSTemplate";

    @Value("${app.updateCycle}")
    int updateCycle;
    @Autowired
    TaskRegistrar taskRegistrar;
    @Autowired
    ThingAgent thingAgent;
    @Autowired
    ClusterManager clusterManager;


    /**
     * 初始化Thing
     *
     * @throws Exception Exception
     */
    public void initThing() {

        try {
            clusterManager.init();
        } catch (Exception ex) {
            log.info("clusterManager init error:{}", ex.getMessage());
        }
        try {
            thingworxService.init();
            thingAgent.initThing();
            Thread thread = new Thread(this, "ThingManagementRunUpdate");
            thread.setUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
                @Override
                public void uncaughtException(Thread t1, Throwable ex) {
                    // 给出异常的英文注释
                    log.error("Thread:{},Error:{}", t1.getName(), ex.getMessage());
                }
            });
            taskRegistrar.init();
            thread.start();
            log.info("initALLThing complete");
        } catch (Exception ex) {
            log.error("initThing error:{}", ex.getMessage());
        }

    }

    /**
     * 添加Thing
     *
     * @param thing thing
     * @return boolean
     */
    public boolean addThing(EntityThing thing) {
        if (thingMap.containsKey(thing.getName())) {
            return false;
        } else {
            thingMap.put(thing.getName(), thing);
            return true;
        }
    }

    /**
     * 更新Thing
     */
    public void updateThing() {
        thingAgent.initThing();
    }


    /**
     * 获取Thing列表
     *
     * @return List<EntityThing>
     */
    public List<EntityThing> getEntityThings() {
        return new ArrayList<>(thingMap.values());
    }

    /**
     * 获取Thing
     *
     * @param name thing name
     * @return EntityThing
     */
    public EntityThing getThing(String name) {
        if (thingMap.containsKey(name)) {
            return thingMap.get(name);
        } else {
            return null;
        }
    }

    /**
     * 获取Thing列表
     *
     * @return List<String>
     */
    List<String> getThingList() {
        return getThingList(templates);

    }

    List<String> getThingList(String thingTemplate) {
        List<String> stringList = new ArrayList<>();
        try {
            ValueCollection valueCollection = new ValueCollection();
            valueCollection.SetStringValue("thingTemplate", thingTemplate);
            InfoTable res = client.invokeService(RelationshipTypes.ThingworxEntityTypes.Resources, "SearchFunctions", "SearchThingsByTemplate", valueCollection, 2000);
            for (int i = 0; i < res.getLength(); i++) {
                ValueCollection valueCollection1 = res.getRow(i);
                String thingName = valueCollection1.getStringValue("name");
                stringList.add(thingName);
                log.info("valueCollection1 :{}", valueCollection1.toString());
            }
            log.info("getThingList res:{}", res.toString());
        } catch (Exception ex) {
            log.error("getThingList error:{}", ex.getMessage());
        }
        return stringList;
    }


    void update() {
        try {
            while (!client.isShutdown()) {
                if (client.isConnected()) {
                    for (EntityThing thing : thingMap.values()) {
                        try {
                            thing.updating(null);
                        } catch (Exception eProcessing) {
                            log.error("Error Processing Scan Request for [" + thing.getName() + "] : " + eProcessing.getMessage());
                        }
                    }
                }
                Thread.sleep(updateCycle);
            }

        } catch (Exception ex) {
            log.error("run error", ex);
        }
    }

    @Override
    public void run() {

        update();

    }
}