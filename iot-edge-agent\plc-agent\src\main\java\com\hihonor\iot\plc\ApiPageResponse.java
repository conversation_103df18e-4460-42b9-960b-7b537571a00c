/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Data
@NoArgsConstructor
public class ApiPageResponse<T> {

    private boolean success;
    private String message;
    private T data;
    private long length = 0; // 数据长度
    private long total=0; // 总记录数
    private long pageSizes=0; // 总页数
    private long currentPage=0; // 当前页码


    public ApiPageResponse(boolean success, String message, T data, long length, long pageSizes, long totalElements, long currentPage) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.pageSizes = pageSizes;
        this.length = length;
        this.total = totalElements;
        this.currentPage = currentPage;
    }
}