SELECT iot_admin.create_plc_operation_log_partition()


-- 1. 将 method_params 字段类型改回 VARCHAR(255)
ALTER TABLE iot_admin.plc_operation_log
ALTER COLUMN method_params TYPE VARCHAR(255) USING method_params::TEXT;

-- 2. 将 return_value 字段类型改回 VARCHAR(255)
ALTER TABLE iot_admin.plc_operation_log
    ALTER COLUMN return_value TYPE VARCHAR(255) USING return_value::TEXT;


-- 测试插入一条操作日志（VARCHAR版本）
INSERT INTO iot_admin.plc_operation_log (
    device_type, device_name, online_status, method_name,
    method_params, user_id, operation_time, execution_time,
    return_value, exception_message
) VALUES (
             'test_device', 'test_name', true, 'test_method',
             '["param1", "param2"]', 'test_user', CURRENT_TIMESTAMP, 100,
             '{"status": "success"}', null
         );



-- 查看继承关系（分区表）
SELECT
    n.nspname as schema_name,
    c.relname as table_name,
    p.relname as partition_name
FROM pg_inherits i
         JOIN pg_class c ON i.inhparent = c.oid
         JOIN pg_class p ON i.inhrelid = p.oid
         JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'iot_admin'
  AND c.relname = 'plc_operation_log'
ORDER BY p.relname;