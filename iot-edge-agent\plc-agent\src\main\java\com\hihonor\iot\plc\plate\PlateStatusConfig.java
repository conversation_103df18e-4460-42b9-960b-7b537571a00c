/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Component
@ConfigurationProperties(prefix = "plate-status")
@Data
@Slf4j
public class PlateStatusConfig {

    private List<Brand> brands;
    private Map<String, Brand> brandMap = new HashMap<>();


    @PostConstruct
    public void init() {
        // 初始化品牌映射
        if (brands != null) {
            for (Brand brand : brands) {
                brandMap.put(brand.getBrand(), brand);
            }
        }
    }

    /**
     * /**
     * 根据 Plate 对象读取设备上报的属性值。
     *
     * @param plate 设备的 Plate 对象
     * @return PlateStatus 对象，包含资源ID、时间戳和轨道状态列表
     */
    public PlateStatus readDeviceAttributes(Plate plate) {
        String resourceId = plate.getThingName(); // 获取资源ID
        PlateStatus plateStatus = new PlateStatus();
        plateStatus.setResourceId(resourceId);
        plateStatus.setTimestamp(LocalDateTime.now()); // 设置当前时间

        // 获取品牌信息
        Brand brand = findBrandByTag(plate.getTag()); // 假设 Plate 类有 getTag() 方法

        if (brand != null) {
            // 遍历品牌的所有轨道
            for (Track track : brand.getTracks()) {
                String trackName = track.getTrack();
                Map<String, String> attributeMap = new HashMap<>(); // 存储最终属性值

                // 遍历每个轨道的属性
                for (Attribute attribute : track.getAttributes()) {
                    Map<String, String> rawValues = new HashMap<>(); // 存储所有地址的原始值

                    // 遍历属性地址并读取原始值
                    for (String address : attribute.getAdress()) { // 获取属性地址列表
                        String rawValue = plate.readDataFromPlc(address, address); // 读取原始属性值
                        rawValues.put(address, rawValue); // 将原始值放入 Map 中
                    }

                    // 根据 StatusMapping 转换原始值为最终值
                    String finalValue = mapRawValueToFinalValue(rawValues, attribute.getStatusMapping());

                    if (finalValue != null) {
                        attributeMap.put(attribute.getAttribute(), finalValue); // 将最终值放入映射中
                    }
                }

                // 创建 TrackStatus 对象并添加到 PlateStatus 中
                PlateStatus.TrackStatus trackStatus = new PlateStatus.TrackStatus(trackName, attributeMap);
                plateStatus.getTrackStatuses().add(trackStatus);
            }
        }

        return plateStatus; // 返回构建好的 PlateStatus 对象
    }

    /**
     * 根据属性值和状态映射列表，映射原始属性值到最终状态值。
     *
     * @param attributeValues 属性地址与对应值的映射
     * @param statusMappings  状态映射列表，包含每个状态的条件和逻辑运算符
     * @return 返回映射后的状态值，如果没有匹配则返回 null
     */
    private String mapRawValueToFinalValue(Map<String, String> attributeValues, List<StatusMapping> statusMappings) {
        // 遍历每个状态映射
        for (StatusMapping mapping : statusMappings) {
            boolean conditionMet = false; // 初始化条件匹配标志

            // 检查逻辑运算符
            if ("OR".equals(mapping.getLogicalOperator())) {
                // 如果是 OR 逻辑，检查任一条件是否满足
                for (Condition condition : mapping.getConditions()) {
                    String value = attributeValues.get(condition.getPoint()); // 获取对应属性地址的值
                    if (value != null && condition.getValue().equals(value)) {
                        conditionMet = true; // 满足任一条件
                        log.info("满足 OR 条件: {} = {}", condition.getPoint(), value); // 记录日志
                        break; // 找到一个匹配条件，跳出循环
                    }
                }
            } else if ("AND".equals(mapping.getLogicalOperator())) {
                // 如果是 AND 逻辑，所有条件必须满足
                conditionMet = true; // 默认认为满足所有条件
                for (Condition condition : mapping.getConditions()) {
                    String value = attributeValues.get(condition.getPoint()); // 获取对应属性地址的值
                    if (value == null || !condition.getValue().equals(value)) {
                        conditionMet = false; // 只要有一个条件不满足，就设置为 false
                        log.info("不满足 AND 条件: {} != {}", condition.getPoint(), value); // 记录日志
                        break; // 找到不满足的条件，跳出循环
                    }
                }
            } else {
                // 如果没有逻辑连接符，直接检查第一个条件是否匹配
                if (!mapping.getConditions().isEmpty()) {
                    Condition condition = mapping.getConditions().get(0);
                    String value = attributeValues.get(condition.getPoint()); // 获取对应属性地址的值
                    if (value != null && condition.getValue().equals(value)) {
                        conditionMet = true; // 满足该条件
                        log.info("满足单个条件: {} = {}", condition.getPoint(), value); // 记录日志
                    } else {
                        log.info("不满足单个条件: {} != {}", condition.getPoint(), value); // 记录日志
                    }
                }
            }

            // 如果满足条件，则返回映射后的状态值，并记录日志
            if (conditionMet) {
                log.info("返回状态: {}", mapping.getStatus()); // 记录日志
                return mapping.getStatus();
            }
        }

        log.warn("没有匹配的状态，返回 null"); // 记录警告日志
        return null; // 如果没有匹配，返回 null 或者可以返回原始值，根据需求决定
    }

    /**
     * 根据传入的设备描述信息查找匹配的品牌配置。
     *
     * @param tag 输入的设备描述信息，可以包含多个品牌名，用 || 分隔。
     * @return 匹配的品牌配置，如果没有匹配则返回 null。
     */
    public Brand findBrandByTag(String tag) {
        if (tag == null || tag.isEmpty()) {
            return null; // 如果输入为空，返回null
        }

        // 遍历所有品牌，检查描述信息中是否包含该品牌名
        for (Brand brand : brands) {
            // 检查描述中是否包含品牌名（支持使用 || 分隔）
            String[] brandNames = brand.getBrand().split("\\|\\|");
            for (String name : brandNames) {
                if (tag.contains(name.trim())) { // 检查描述中是否包含该品牌名
                    return brand; // 返回匹配的品牌配置
                }
            }
        }
        return null; // 如果没有找到匹配的品牌，返回null
    }


    @Data
    public static class Brand {
        private String brand;
        private List<Track> tracks;
    }

    @Data
    public static class Track {
        private String track;
        private List<Attribute> attributes;
    }

    @Data
    public static class Attribute {
        private String attribute;
        private List<String> adress; // 注意：'adress' 是按照原始YAML中的拼写
        private List<StatusMapping> statusMapping;
    }

    @Data
    public static class StatusMapping {
        private String status;
        private String logicalOperator; // 可选，可以为null
        private List<Condition> conditions;
    }

    @Data
    public static class Condition {
        private String point;
        private String value;
    }

}