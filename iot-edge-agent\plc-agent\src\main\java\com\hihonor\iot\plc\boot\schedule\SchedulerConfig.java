/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot.schedule;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Configuration
public class SchedulerConfig {
    /**
     * myTaskScheduler
     *
     * @return TaskScheduler
     */
    @Bean("customTaskScheduler")
    @Primary
    public TaskScheduler myTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(20);
        scheduler.setThreadNamePrefix("scheduled-task-");
        scheduler.setDaemon(true);
        scheduler.initialize();
        return scheduler;
    }

}