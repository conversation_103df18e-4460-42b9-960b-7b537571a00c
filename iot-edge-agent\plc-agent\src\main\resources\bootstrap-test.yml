#spring:
#  application:
#    name: plc-agent
logging:
  config:  classpath:logback-spring.xml
#  profiles:
#    active: test
#  cloud:
#    nacos:
#      config:
#        server-addr: mse.beta.hihonor.com:80
#        context-path: /nacos/service

# 测试环境使用pekctxt
docker_region: pekctxt
# 应用配置环境
docker_env: sit1
# 应用信息
application:
  # IAM项目ID，应用ID
  appId: 9ad5727dad4c4a55b8f16e2dd0500420
  # 部署单元
  subAppId: plc-cachingMachine-agent
# IAM项目信息
#iam:
#  enterprise: 99999999999999999999999999999999
#  project: 9ad5727dad4c4a55b8f16e2dd0500420
#  endpoint: http://apig.heds.hihonor.com/api
#  account: 9ad5727dad4c4a55b8f16e2dd0500420
#  secret: FO5tQnLxgt3CKC0mTYDc0J8CAn56Z+c/oqeFd3cC

spring:
  application:
    name: plc-agent
  profiles:
    active: test
  cloud:
    nacos:
      config:
        endpoint: mse.beta.hihonor.com:80
        context-path: /nacos-address/service/nacos
    compatibility-verifier:
      enabled: false
#        server-addr: mse.beta.hihonor.com:80
#        context-path: /nacos/service
truss:
  apimall:
    enterprise: 99999999999999999999999999999999
    ## 生产为：https://yun.hihonor.com
    endpoint: https://yun.beta.hihonor.com
    ## apimall中注册的API集成账号
    project: 9ad5727dad4c4a55b8f16e2dd0500420
    account: 9ad5727dad4c4a55b8f16e2dd0500420
    secret: FO5tQnLxgt3CKC0mTYDc0J8CAn56Z+c/oqeFd3cC


idaas:
  auth:
    enable: false
    sk: 7cb3f844e7f54063bc619623b6d2e1d02d9e0479a0044097bb34161611c5ef4d
    ak: iot-equip_c5cdee2248de49089b931dbca35830b6
    url: https://auth-uat.test.hihonor.com/intidgateway/intvalidatorapi/rest/idaas/validator/api/token-auth

iam:
  enterprise: 99999999999999999999999999999999
  project: 9ad5727dad4c4a55b8f16e2dd0500420
apimall:
  consumer: true
  endpoint: https://yun.beta.hihonor.com
  account: 9ad5727dad4c4a55b8f16e2dd0500420
  secret: FO5tQnLxgt3CKC0mTYDc0J8CAn56Z+c/oqeFd3cC

# 流量切换模块配置
trafficswitch:
  api:
    base-url: http://agw.beta.hihonor.com  # API基础地址
    upstream-id: 549948858034102272        # 要查询的上游服务ID (请根据实际情况修改)

