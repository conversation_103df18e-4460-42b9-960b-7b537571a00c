/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot;

import java.io.BufferedReader;
import java.util.Collections;
import java.util.Enumeration;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.HandlerInterceptor;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-26
 */

@Slf4j
@Component
@DependsOn("clusterManager")
public class LeaderInterceptor implements HandlerInterceptor {

    @Autowired
    ClusterManager clusterManager;

    @Autowired
    private WebClient.Builder webClientBuilder;
    private static final String HEADER_FORWARD_FLAG = "X-Forwarded-By";


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (shouldForwardRequest(handler) && !clusterManager.isLeader() && !isForwarded(request)) {
            log.warn("Current node is not Leader. Forwarding the request to the Leader.");

            WebClient webClient = webClientBuilder.build();
            String leaderInfo = clusterManager.getLeaderInfo().getNodeInfo();
            String url = "http://" + leaderInfo + request.getRequestURI();
            // 创建一个 MultiValueMap 来存储请求头
            MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
            headers.put(HEADER_FORWARD_FLAG, Collections.singletonList("1"));
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headers.addAll(headerName, Collections.list(request.getHeaders(headerName)));
            }

            // 直接读取HttpServletRequest的输入流，并转为字符串
            String requestBody = "";
            try (BufferedReader reader = request.getReader()) {
                requestBody = reader.lines().collect(Collectors.joining(System.lineSeparator()));
            }

            HttpMethod resolvedMethod = HttpMethod.resolve(request.getMethod());
            if (resolvedMethod == null) {
                log.error("Unsupported HTTP method");
                return false; // 或者抛出异常
            }


            // 使用 WebClient 发送请求
            Mono<String> responseMono = webClient.method(resolvedMethod)
                    .uri(url)
                    .headers(httpHeaders -> httpHeaders.addAll(headers))
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class);
            response.setContentType("application/json; charset=UTF-8");
            // 获取响应体
            String result = responseMono.block();
            if (result != null) {
                response.getWriter().write(result);
            }

            log.info("Successfully forwarded the request and got the response.");
            return false;
        } else {
            log.info("Handler does not have @ForwardToLeader annotation or is not an instance of HandlerMethod. Handling request locally.");
            return true;
        }
    }

    private boolean isUrlValid(String url) {
        return true;
    }

    // 检查是否应该将请求转发到主节点
    private boolean shouldForwardRequest(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            ForwardToLeader annotation = handlerMethod.getMethodAnnotation(ForwardToLeader.class);
            if (annotation != null) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断请求是否已经被转发过。
     *
     * @param request 当前的HttpServletRequest对象
     * @return 如果请求已经包含转发标志，则返回true；否则返回false。
     */
    private boolean isForwarded(HttpServletRequest request) {
        return request.getHeader(HEADER_FORWARD_FLAG) != null;
    }


}