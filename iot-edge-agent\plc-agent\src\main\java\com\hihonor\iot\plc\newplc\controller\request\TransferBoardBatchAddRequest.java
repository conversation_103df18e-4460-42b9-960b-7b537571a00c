/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import com.hihonor.iot.plc.newplc.modle.TransferBoardInfo;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-30
 */
@Data
public class TransferBoardBatchAddRequest {

    @NotEmpty(message = "过板台信息列表不能为空")
    @Valid
    private List<TransferBoardInfo> transferBoardInfoList;
}