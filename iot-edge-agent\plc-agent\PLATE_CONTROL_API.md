# 过板台设备控制 API 文档

本文档详细描述了用于控制和读取过板台设备状态的 HTTP API 接口。

**基础域名**: `http://iot-gateway.yun.hihonor.com`

---

## 1. 写入过板台点位值

### 接口基本信息

- **接口名称**: 写入过板台点位值
- **接口地址**: `http://iot-gateway.yun.hihonor.com/plc/plate/writeValue`
- **请求方式**: POST
- **接口描述**: 向指定的过板台设备写入单个点位的值，用于控制各类过板台设备的运行状态、告警信号等。
- **请求头**:
  - Content-Type: application/json

### 请求参数

| 参数名    | 类型   | 必填 | 描述                                 | 示例值           |
| --------- | ------ | ---- | ------------------------------------ | ---------------- |
| plateName | String | 是   | 过板台设备名称，设备的唯一标识符     | "SMT09_HuiLiuLu" |
| address   | String | 是   | 要写入的点位地址，遵循设备定义的格式 | "R2000"          |
| value     | String | 是   | 要写入的数值，通常为字符串形式的数字 | "1"              |

### 响应参数

| 参数名  | 类型    | 描述                                          |
| ------- | ------- | --------------------------------------------- |
| success | Boolean | 操作结果状态，true 表示成功，false 表示失败   |
| message | String  | 操作结果的文字描述                            |
| data    | Boolean | 具体的写入结果，true 表示成功，false 表示失败 |
| length  | Integer | 此接口固定为 0                                |

### 请求示例

```
POST http://iot-gateway.yun.hihonor.com/plc/plate/writeValue
Content-Type: application/json

{
  "plateName": "SMT09_HuiLiuLu",
  "address": "R2000",
  "value": "1"
}
```

### 响应示例

**成功响应**:

```json
{
  "success": true,
  "message": "写入成功",
  "data": true,
  "length": 0
}
```

**失败响应 - 设备不存在**:

```json
{
  "success": false,
  "message": "写入失败",
  "data": false,
  "length": 0
}
```

**失败响应 - 地址格式不正确**:

```json
{
  "success": false,
  "message": "写入失败",
  "data": false,
  "length": 0
}
```

### 错误码

| HTTP 状态码 | 错误描述                                     |
| ----------- | -------------------------------------------- |
| 400         | 请求参数不合法（缺少必填字段、格式错误等）   |
| 500         | 服务器内部错误（设备连接失败、设备不存在等） |

### 使用说明

1. 此接口提供最基础的点位写入功能，仅负责向指定设备的指定地址写入值
2. 复杂业务逻辑（如告警触发写入 1、处理后自动复位写入 0）需由调用方自行实现
3. 写入后，建议使用读取接口验证写入是否真实生效

**使用场景示例**:

- 告警信号发送：向地址 R2000 写入 1 触发告警，后续需手动写入 0 复位
- 设备复位：向控制地址写入特定值触发设备复位
- 参数配置：向配置类地址写入参数值

---

## 2. 读取过板台数据

### 接口基本信息

- **接口名称**: 读取过板台数据
- **接口地址**: `http://iot-gateway.yun.hihonor.com/plc/plate/readData`
- **请求方式**: POST
- **接口描述**: 从指定的过板台设备读取一个或多个连续地址范围的数据，用于获取设备状态、验证写入操作等。
- **请求头**:
  - Content-Type: application/json

### 请求参数

| 参数名       | 类型   | 必填 | 描述                             | 示例值           |
| ------------ | ------ | ---- | -------------------------------- | ---------------- |
| plateName    | String | 是   | 过板台设备名称，设备的唯一标识符 | "SMT09_HuiLiuLu" |
| startAddress | String | 是   | 读取的起始点位地址               | "R2000"          |
| endAddress   | String | 是   | 读取的结束点位地址（包含此地址） | "R2005"          |

### 响应参数

| 参数名  | 类型    | 描述                                        |
| ------- | ------- | ------------------------------------------- |
| success | Boolean | 操作结果状态，true 表示成功，false 表示失败 |
| message | String  | 操作结果的文字描述                          |
| data    | String  | 按照地址顺序拼接的原始数据字符串            |
| length  | Integer | 返回的数据字符串长度                        |

### 请求示例

```
POST http://iot-gateway.yun.hihonor.com/plc/plate/readData
Content-Type: application/json

{
  "plateName": "SMT09_HuiLiuLu",
  "startAddress": "R2000",
  "endAddress": "R2005"
}
```

### 响应示例

**成功响应**:

```json
{
  "success": true,
  "message": "读取成功",
  "data": "100110",
  "length": 6
}
```

**失败响应**:

```json
{
  "success": false,
  "message": "读取失败",
  "data": "",
  "length": 0
}
```

### 错误码

| HTTP 状态码 | 错误描述                                       |
| ----------- | ---------------------------------------------- |
| 400         | 请求参数不合法（缺少必填字段、地址范围无效等） |
| 500         | 服务器内部错误（设备连接失败、读取超时等）     |

### 使用说明

1. 此接口可读取指定范围内的连续地址数据，返回原始字符串格式
2. 对于较大的地址范围，请合理控制读取范围，避免响应超时
3. 适用于验证写入结果或批量获取设备状态信息

**使用场景示例**:

- 写入验证：在写入操作后立即读取相同地址，确认写入是否成功
- 设备状态监控：定期读取状态地址范围，监控设备运行状态
- 诊断与调试：读取设备寄存器或状态位进行问题诊断
