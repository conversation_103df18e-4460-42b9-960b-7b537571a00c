/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;


import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@Slf4j
@SpringBootApplication
@ComponentScan(value = {"com.hihonor.iot.edge","com.hihonor.iot.plc.*","com.hihonor.iot.plc.cachingmachine.repo","com.hihonor.iot.plc.thingworx"})
@EnableScheduling
@EnableConfigurationProperties
public class PlcApplication {
    public static void main(String[] args) {
        SpringApplication.run(PlcApplication.class, args);
        log.info("CachingMachineApplication start success");
    }
}
