/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.deviceLog;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * PLC设备操作日志切面类
 * <p>
 * 该切面类负责拦截标记了{@link PlcLogOperation}注解的方法，记录设备操作的详细信息，
 * 包括设备信息、方法调用参数、执行时间、返回结果以及可能发生的异常等。
 * 通过AOP技术实现非侵入式的操作日志记录，提高代码的可维护性和解耦性。
 * </p>
 * <p>
 * 日志记录过程中实现了严格的空值检查和异常处理，确保日志功能不会影响正常业务逻辑的执行。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Slf4j
@Aspect
@Component
public class PlcOperationLogAspect {
    /**
     * 操作日志服务，用于持久化日志记录
     */
    @Autowired
    private OperationLogService operationLogService;

    /**
     * JSON对象映射器，用于序列化方法返回值
     */
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 环绕通知，处理正常执行的操作日志记录
     * <p>
     * 该方法会在目标方法执行前后进行拦截，记录方法的执行时间、返回值等信息。
     * 通过try-catch结构处理各种可能的异常情况，确保即使日志记录失败，也不会影响主要业务逻辑。
     * </p>
     *
     * @param joinPoint 连接点，包含目标方法的信息
     * @return 目标方法的返回值
     * @throws Throwable 如果目标方法执行过程中抛出异常，则透传该异常
     */
    @Around("@annotation(com.hihonor.iot.plc.deviceLog.PlcLogOperation)")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 记录方法开始执行的时间
            long startTime = System.currentTimeMillis();

            // 执行目标方法并获取返回值
            Object result = joinPoint.proceed();

            // 计算方法执行耗时
            long executionTime = System.currentTimeMillis() - startTime;

            // 获取方法签名信息
            String methodName = joinPoint.getSignature().getName();
            Object[] methodParams = joinPoint.getArgs();

            // 获取设备信息，并进行空值处理
            Device device = (Device) joinPoint.getTarget();
            String deviceType = device != null ? device.getType() : "未知设备类型";
            String deviceName = device != null ? device.getThingName() : "未知设备名称";
            boolean onlineStatus = device != null && device.getConnected();

            // 构建操作日志对象
            PlcOperationLog operationLog = PlcOperationLog.builder()
                    .deviceType(deviceType != null ? deviceType : "未知设备类型")
                    .deviceName(deviceName != null ? deviceName : "未知设备名称")
                    .onlineStatus(onlineStatus)
                    .methodName(methodName != null ? methodName : "未知方法")
                    .methodParams(methodParams != null ? Arrays.toString(methodParams) : "无参数")
                    .operationTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()))
                    .executionTime((int) executionTime)
                    .returnValue(null)
                    .build();

            // 序列化返回值，并处理可能的序列化异常
            try {
                if (result != null) {
                    operationLog.setReturnValue(objectMapper.writeValueAsString(result));
                }
            } catch (Exception ex) {
                log.error("序列化返回值时发生错误: {}", ex.getMessage());
                operationLog.setReturnValue("序列化失败: " + ex.getMessage());
            }

            // 保存操作日志，并处理可能的保存异常
            try {
                operationLogService.saveOperationLog(operationLog);
            } catch (Exception ex) {
                log.error("保存操作日志时发生错误: {}", ex.getMessage());
            }

            // 返回原方法执行结果
            return result;
        } catch (Throwable t) {
            // 记录切面本身出现的异常，然后重新抛出，不影响原有异常传播
            log.error("操作日志记录过程发生未预期异常: {}", t.getMessage(), t);
            throw t;
        }
    }

    /**
     * 异常通知，处理方法执行过程中抛出异常的日志记录
     * <p>
     * 当目标方法执行抛出异常时，该方法会被调用，记录异常信息及相关上下文。
     * 此方法与环绕通知互补，专注于异常情况的日志记录。
     * </p>
     *
     * @param joinPoint 连接点，包含目标方法的信息
     * @param e         目标方法抛出的异常
     */
    @AfterThrowing(pointcut = "@annotation(com.hihonor.iot.plc.deviceLog.PlcLogOperation)", throwing = "e")
    public void logException(JoinPoint joinPoint, Throwable e) {
        try {
            // 获取方法签名信息
            String methodName = joinPoint.getSignature().getName();
            Object[] methodParams = joinPoint.getArgs();

            // 获取设备信息，增加异常处理
            Device device = null;
            try {
                device = (Device) joinPoint.getTarget();
            } catch (Exception ex) {
                log.error("获取设备信息时发生错误: {}", ex.getMessage());
            }

            // 处理设备信息空值
            String deviceType = device != null ? device.getType() : "未知设备类型";
            String deviceName = device != null ? device.getThingName() : "未知设备名称";
            boolean onlineStatus = device != null && device.getConnected();

            // 构建异常日志对象
            PlcOperationLog operationLog = PlcOperationLog.builder()
                    .deviceType(deviceType != null ? deviceType : "未知设备类型")
                    .deviceName(deviceName != null ? deviceName : "未知设备名称")
                    .onlineStatus(onlineStatus)
                    .methodName(methodName != null ? methodName : "未知方法")
                    .methodParams(methodParams != null ? Arrays.toString(methodParams) : "无参数")
                    .operationTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()))
                    .exceptionMessage(e != null ? e.getMessage() : "未知异常")
                    .build();

            // 保存异常日志，处理可能的保存异常
            try {
                operationLogService.saveOperationLog(operationLog);
            } catch (Exception ex) {
                log.error("保存异常日志时发生错误: {}", ex.getMessage());
            }
        } catch (Exception ex) {
            // 记录异常日志过程中出现的异常
            log.error("异常日志记录过程发生未预期错误: {}", ex.getMessage(), ex);
        }
    }
}