/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.recipes.cache.NodeCache;
import org.apache.curator.framework.recipes.cache.PathChildrenCache;
import org.apache.curator.framework.recipes.cache.PathChildrenCacheEvent;
import org.apache.curator.framework.recipes.leader.LeaderLatch;
import org.apache.curator.framework.recipes.leader.LeaderLatchListener;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Component  // Spring组件
@Slf4j  // 日志
public class ClusterManager {
    // 存储集群中所有节点信息的映射
    /**
     * nodeInfoMap
     */
    public final static ConcurrentHashMap<String, NodeInfo> NODE_INFO_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    private CuratorFramework client;  // Zookeeper客户端
    private PathChildrenCache childrenCache;  // 子节点缓存
    /**
     * localNode
     */
    public NodeInfo localNode = new NodeInfo();  // 本地节点信息

    @Value("${zookeeper.connection-string}")  // Zookeeper连接字符串
    private String zookeeperServer;

    @Value("${server.port}")  // 服务器端口
    private String serverPort;

    @Value("${spring.application.name}")  // 应用名称
    private String appName;



    // Bean创建后执行的初始化方法

    /**
     * 初始化
     */
    public void init() {
        log.info("Initializing ClusterManager...");
        initZookeeperClient();  // 初始化Zookeeper客户端
        initLocalNodeInfo();  // 初始化本地节点信息
        initChildrenCache();  // 初始化子节点缓存
        initLeaderLatch();  // 初始化领导选举
        initLeaderCache();  // 初始化领导节点缓存
    }

    // 初始化Zookeeper客户端
    private void initZookeeperClient() {
        log.info("Initializing Zookeeper client...");
        client = CuratorFrameworkFactory.newClient(zookeeperServer, new ExponentialBackoffRetry(1000, 3));
        client.start();
    }

    String getHostAddress() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                // 过滤掉 loopback 和非激活的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    // 确保地址不是 loopback 地址并且是 IPv4
                    if (!addr.isLoopbackAddress() && addr instanceof Inet4Address) {
                        // 返回 IPv4 地址
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null; // 或者返回一个默认地址
    }

    // 初始化本地节点信息

    private void initLocalNodeInfo() {
        try {
            log.info("Initializing local node info...");
            String hostAddress = getHostAddress();
            localNode.setIp(hostAddress);
            localNode.setPort(Integer.parseInt(serverPort));
            localNode.setLeader(false);
            NODE_INFO_CONCURRENT_HASH_MAP.putIfAbsent(localNode.getNodeInfo(), localNode);
            String localNodePath = "/" + appName + "/nodes/" + localNode.getNodeInfo();
            localNode.setZkPath(localNodePath);
            // 创建临时节点
            client.create().creatingParentsIfNeeded().withMode(CreateMode.EPHEMERAL).forPath(localNodePath, localNode.getNodeInfo().getBytes(StandardCharsets.UTF_8));
        } catch (Exception ex) {
            log.error("eader node already exists at path");
        }
    }

    // 初始化子节点缓存
    private void initChildrenCache() {
        try {
            log.info("Initializing children cache...");
            childrenCache = new PathChildrenCache(client, "/" + appName + "/nodes", true);
            childrenCache.start();

            // 添加子节点变化监听器
            childrenCache.getListenable().addListener((curatorFramework, event) -> {
                String path = event.getData().getPath();
                String data = new String(event.getData().getData(),StandardCharsets.UTF_8);
                if (event.getType() == PathChildrenCacheEvent.Type.CHILD_ADDED) {
                    NodeInfo nodeInfo = new NodeInfo(data);
                    nodeInfo.setZkPath("/" + appName + "/nodes/" + data);
                    NODE_INFO_CONCURRENT_HASH_MAP.putIfAbsent(data, nodeInfo);
                    log.info("Node added, path: {}, data: {}", path, data);
                } else if (event.getType() == PathChildrenCacheEvent.Type.CHILD_REMOVED) {
                    NODE_INFO_CONCURRENT_HASH_MAP.remove(data);
                    log.info("Node removed, path: {}", path);
                }
            });
        } catch (Exception ex) {
            log.error("Children cache has exit", ex);

        }

    }


    // 初始化领导节点缓存
    private void initLeaderCache() {
        try {
            log.info("Initializing leader cache...");
            String leaderPath = "/" + appName + "/currentLeader";

            Stat nodeStat = client.checkExists().forPath(leaderPath);
            if (nodeStat == null) {
                client.create().creatingParentsIfNeeded().forPath(leaderPath);
                log.info("Leader node created at path: {}", leaderPath);
            }
            NodeCache leaderCache = new NodeCache(client, leaderPath);
            leaderCache.getListenable().addListener(() -> {
                byte[] data = leaderCache.getCurrentData().getData();
                String leaderInfo = new String(data, StandardCharsets.UTF_8);
                log.info("Leader data changed to: {}", leaderInfo);
                updateLeaderInfoInNodeMap(leaderInfo);
            });
            leaderCache.start();
        } catch (Exception e) {
            log.error(" leader cache has exit", e);
        }

    }

    // 更新nodeInfoMap中的领导信息
    private void updateLeaderInfoInNodeMap(String leaderInfo) {
        log.info("Updating leader information in nodeInfoMap...");
        // 先将所有节点的isLeader设置为false
        for (NodeInfo nodeInfo : NODE_INFO_CONCURRENT_HASH_MAP.values()) {
            nodeInfo.isLeader = false;
        }
        NodeInfo node = NODE_INFO_CONCURRENT_HASH_MAP.get(leaderInfo);
        if (node != null) {
            node.isLeader = true;
        } else {
            NodeInfo nodeInfo = new NodeInfo(leaderInfo);
            nodeInfo.isLeader = true;
            NODE_INFO_CONCURRENT_HASH_MAP.put(leaderInfo, nodeInfo);
            nodeInfo.setZkPath("/" + appName + "/nodes/" + leaderInfo);
        }
    }

    // 初始化领导选举
    private void initLeaderLatch() {
        try {
            log.info("Initializing leader latch...");
            String leaderPath = "/" + appName + "/leader";
            LeaderLatch leaderLatch = new LeaderLatch(client, leaderPath);
            leaderLatch.addListener(new LeaderLatchListener() {
                /**
                 * isLeader
                 */
                @Override
                public void isLeader() {
                    try {
                        client.create().orSetData().forPath("/" + appName + "/currentLeader", localNode.getNodeInfo().getBytes(StandardCharsets.UTF_8));
                    } catch (Exception e) {
                        log.error("Failed to set leader data in Zookeeper", e);
                    }
                    log.info("I am the leader now.");
                }

                /**
                 * notLeader
                 */
                @Override
                public void notLeader() {
                    log.info("I am not the leader anymore.");
                }
            });
            leaderLatch.start();  // 开始领导选举
        } catch (Exception ex) {
            log.info("asd");
        }

    }

    // 判断是否为本地节点

    /**
     * 判断是否为本地节点
     *
     * @param ipAndPort ipAndPort
     * @return boolean
     */
    public boolean isLocalNode(String ipAndPort) {
        String[] parts = ipAndPort.split(":");
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid ipAndPort format. Expected format 'ip:port'.");
        }
        String ip = parts[0];
        int port = Integer.parseInt(parts[1]);

        // 获取本地节点的IP和端口信息
        String localIp = localNode.ip;
        int localPort = localNode.port;

        // 检查IP和端口是否与本地节点相匹配
        // 如果IP是127.0.0.1，并且端口相同，也认为是本地节点
        return (localIp.equals(ip) && localPort == port) || ("127.0.0.1".equals(ip) && localPort == port);

    }

    // 获取本地节点的Zookeeper路径

    /**
     * 获取本地节点的Zookeeper路径
     *
     * @return String
     */
    public String getLocalNodePath() {
        return localNode.getZkPath();
    }

    // 判断当前节点是否是领导节点

    /**
     * 判断当前节点是否是领导节点
     *
     * @return boolean
     */
    public boolean isLeader() {
        return localNode.isLeader;
    }

    // 获取当前集群领导的信息

    /**
     * 获取当前集群领导的信息
     *
     * @return NodeInfo
     */
    public NodeInfo getLeaderInfo() {

        for (NodeInfo nodeInfo : NODE_INFO_CONCURRENT_HASH_MAP.values()) {
            if (nodeInfo.isLeader) {
                return nodeInfo;
            }
        }
        return null;  // 没有找到领导节点
    }

    // 根据IP和端口号构造节点的Zookeeper路径

    /**
     * 根据IP和端口号构造节点的Zookeeper路径
     *
     * @param ip   ip
     * @param port port
     * @return String
     */
    public String constructNodePath(String ip, int port) {
        return "/" + appName + "/nodes/" + ip + ":" + port;
    }

    // 根据完整的hostAddress构造节点的Zookeeper路径

    /**
     * 根据完整的hostAddress构造节点的Zookeeper路径
     *
     * @param hostAddress hostAddress
     * @return String
     */
    public String constructNodePath(String hostAddress) {
        return "/" + appName + "/nodes/" + hostAddress;
    }

    /**
     * NodeInfo
     */
    @Data
    @NoArgsConstructor
    public static class NodeInfo {

        String ip;
        int port;
        boolean isLeader;

        String ZkPath;

        public NodeInfo(String data) {
            String[] parts = data.split(":");
            this.ip = parts[0];
            this.port = Integer.parseInt(parts[1]);
            this.isLeader = false; // can be updated later
        }

        String getNodeInfo() {
            return ip + ":" + port;
        }


    }

}
