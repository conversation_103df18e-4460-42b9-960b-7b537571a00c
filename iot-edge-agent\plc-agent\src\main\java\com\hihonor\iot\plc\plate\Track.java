/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@Data
public class Track {
    private String boardEntryStatus; // 进板状态
    private String deviceStatus; // 设备状态

    public Track(String boardEntryStatus, String deviceStatus) {
        this.boardEntryStatus = boardEntryStatus;
        this.deviceStatus = deviceStatus;
    }
}