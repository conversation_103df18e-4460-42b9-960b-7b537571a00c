/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
public class BatchWriteTransferBoardPropertiesRequest {

    @NotEmpty(message = "过板台写入属性列表不能为空")
    private List<@Valid TransferBoardWriteProperty> transferBoardWriteProperties; // 过板台写入属性列表

    @Data
    public static class TransferBoardWriteProperty {

        @NotBlank(message = "过板台名称不能为空")
        private String name; // 过板台名称

        @NotEmpty(message = "属性写入映射不能为空")
        private Map<@NotBlank(message = "属性名称不能为空") String, @NotNull(message = "属性值不能为null") String> propertiesToWrite; // 要写入的属性名称和值的映射
    }
}