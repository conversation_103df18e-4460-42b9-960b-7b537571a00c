/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera_and_plate.BarcodeValidatorTest;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.Queue;
import java.util.Set;
import java.util.logging.Logger;


/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
public class BarcodeValidator {

    private static final Logger log = Logger.getLogger(BarcodeValidator.class.getName());
    private static final int MAX_HISTORY_SIZE = 3;
    private Queue<String> historyQueue = new LinkedList<>();
    private BarcodeService barcodeService;

    public BarcodeValidator(BarcodeService barcodeService) {
        this.barcodeService = barcodeService;
    }

    public BarcodeValidationResult validateBarcode(String barcodeInput) {
        // 分割输入字符串
        String[] barcodes = barcodeInput.split("\r");

        // 去重集合
        Set<String> uniqueBarcodes = new HashSet<>();

        // 遍历分割后的条码
        for (String barcode : barcodes) {
            // 检查条码长度是否为16位
            if (barcode.length() != 16) {
                log.info("条码长度不对: " + barcode);
                continue;
            }

            // 检查条码是否在历史记录中
            if (isBarcodeInHistory(barcode)) {
                log.info("条码重复: " + barcode);
                continue;
            }

            // 添加到去重集合
            uniqueBarcodes.add(barcode);
            updateHistory(barcode);
        }

        // 调用验证服务进行验证
        for (String barcode : uniqueBarcodes) {
            BarcodeValidationResult result = barcodeService.validateBarcode(ScanRequest.createRequest("cameraThingName", barcode));
            if (result.isValid()) {
                // 更新历史记录

                return result;
            }
        }

        return new BarcodeValidationResult(false, "所有条码验证不通过");
    }

    private boolean isBarcodeInHistory(String barcode) {
        return historyQueue.contains(barcode);
    }

    private void updateHistory(String barcode) {
        if (historyQueue.size() >= MAX_HISTORY_SIZE) {
            historyQueue.poll();
        }
        historyQueue.offer(barcode);
    }

    public static void main(String[] args) {
        // 示例用法
        BarcodeService barcodeService = new BarcodeService();
        BarcodeValidator validator = new BarcodeValidator(barcodeService);

        String input = "02AVXNXX49780172\r02AVXNXX49780173\r02AVXNXX49780174";
        BarcodeValidationResult result = validator.validateBarcode(input);

        log.info(result.getMessage());
        String input2 = "02AVXNXX49780175";
        BarcodeValidationResult result1=validator.validateBarcode(input2);
        log.info(result1.getMessage());
    }
}