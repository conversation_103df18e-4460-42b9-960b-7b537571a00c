/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot.test;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Component
@Slf4j
public class ScheduledTasks {


    // @Scheduled(fixedRate = 5000)
// @MasterOnly
    void runOnlyOnMaster() {
        log.info("This task should only run on the Master node.");
    }

    // @Scheduled(fixedRate = 5000)
// @BackupOnly
    void runOnlyOnBackup() {
        log.info("This task should only run on the Backup node.");
    }

    // @Scheduled(fixedRate = 2000)
    void runOnAny() {
        log.info("This task can run on any node.");
    }
}