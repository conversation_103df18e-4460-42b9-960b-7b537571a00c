/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.thingworx.service;

import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.common.manager.EdgeManager;
import com.hihonor.iot.plc.thing.ThingManagement;
import com.hihonor.iot.plc.thingworx.ThingworxService;
import com.thingworx.communications.client.ConnectedThingClient;
import com.thingworx.types.InfoTable;
import com.thingworx.types.collections.ValueCollection;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Component
@Slf4j
public class SetPropertys {

    static String serviceName = "SetPropertys";
    /**
     * thingworxService
     */
    @Autowired
    protected ThingworxService thingworxService;
    /**
     * thingManagement
     */
    @Autowired
    protected ThingManagement thingManagement;
    /**
     * edgeManager
     */
    @Autowired
    protected EdgeManager edgeManager;
    /**
     * client
     */
    @Autowired
    protected ConnectedThingClient client;

    /**
     * buildRequestBody
     *
     * @param rows rows
     * @return String
     */
    public String buildRequestBody(List<Map<String, Object>> rows) {
        try {
            JSONObject requestBody = new JSONObject();

            // 构建dataShape部分
            JSONObject dataShape = new JSONObject();
            JSONObject fieldDefinitions = new JSONObject();

            JSONObject nameDef = new JSONObject();
            nameDef.put("name", "name");
            nameDef.put("aspects", new JSONObject().put("isPrimaryKey", false));
            nameDef.put("description", "");
            nameDef.put("baseType", "STRING");
            nameDef.put("ordinal", 1);

            JSONObject valueDef = new JSONObject();
            valueDef.put("name", "value");
            valueDef.put("aspects", new JSONObject().put("isPrimaryKey", false));
            valueDef.put("description", "");
            valueDef.put("baseType", "NUMBER");
            valueDef.put("ordinal", 2);

            fieldDefinitions.put("name", nameDef);
            fieldDefinitions.put("value", valueDef);
            dataShape.put("fieldDefinitions", fieldDefinitions);

            // 构建rows部分
            JSONArray rowsArray = new JSONArray();
            for (Map<String, Object> row : rows) {
                rowsArray.put(new JSONObject(row));
            }
            // 构建整体请求体
            JSONObject values = new JSONObject();
            values.put("dataShape", dataShape);
            values.put("rows", rowsArray);
            requestBody.put("values", values);

            return requestBody.toString();
        } catch (Exception ex) {
            log.info("buildRequestBody error:{}", ex.getMessage());
            return null;
        }
    }
    /**
     * invorke
     *
     * @param thingName thingName
     * @param rows      rows
     * @return boolean
     */
    public boolean invorke(String thingName, List<Map<String, Object>> rows) {
        try {
            ValueCollection valueCollection = new ValueCollection();
            InfoTable infoTable = new InfoTable(thingworxService.getNameValues());
            valueCollection.SetInfoTableValue("values", infoTable);
            for (Map<String, Object> row : rows) {
                ValueCollection valueCollection1 = new ValueCollection();
                valueCollection1.SetStringValue("name", row.get("name").toString());
                valueCollection1.SetNumberValue("value", Double.parseDouble(row.get("value").toString()));
                infoTable.addRow(valueCollection1);
            }
            edgeManager.invokeService(thingName, serviceName, valueCollection);
            log.info("thingName:{} report values:{} success", thingName, rows);
            return true;
        } catch (Exception ex) {
            log.info("invorke error:{}", ex.getMessage());
            return false;
        }

    }


}
