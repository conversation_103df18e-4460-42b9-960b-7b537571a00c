/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.boot.context.event.ApplicationStartingEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.camera.CameraManager;
import com.hihonor.iot.plc.camera_and_plate.CameraPlateAssociationEntityManager;
import com.hihonor.iot.plc.plate.PlateManager;

import java.util.concurrent.CompletableFuture;
import lombok.NoArgsConstructor;
import com.hihonor.iot.plc.zk.LeadershipBlockingManager;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@NoArgsConstructor
@Component
public class ApplicationStartListener implements ApplicationListener<ApplicationEvent> {
    private static final Logger logger = LoggerFactory.getLogger(ApplicationStartListener.class);
    // @Autowired
    // IotClientStatusListener iotClientStatusListener;
    //
    // @Autowired
    // ConnectedThingClient client;

    @Autowired
    PlateManager plateManager;

    @Autowired
    CameraManager cameraManager;

    @Autowired
    CameraPlateAssociationEntityManager cameraPlateAssociationEntityManager;

    @Autowired
    LeadershipBlockingManager leadershipBlockingManager;

    /**
     * 事件监听
     *
     * @param event 事件
     */
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        // SpringBoot应用启动且未作任何处理（除listener注册和初始化）的时候发送ApplicationStartingEvent

        if (event instanceof ApplicationStartingEvent) {
            logger.debug("ApplicationStarting...");
            return;
        }

        // 确定springboot应用使用的Environment且context创建之前发送这个事件
        if (event instanceof ApplicationEnvironmentPreparedEvent) {
            logger.debug("ApplicationEnvironmentPrepared...");
            return;
        }

        // context已经创建且没有refresh发送个事件
        if (event instanceof ApplicationPreparedEvent) {
            logger.debug("ApplicationPrepared...");
            return;
        }

        // context已经refresh且application and command-line runners（如果有） 调用之前发送这个事件
        if (event instanceof ApplicationStartedEvent) {
            logger.debug("ApplicationStarted...");
            return;
        }

        if (event instanceof ApplicationReadyEvent) {
            // ApplicationContext context = ((ApplicationReadyEvent)
            // event).getApplicationContext();
            // EdgeManager edgeManager = context.getBean(EdgeManager.class);
//            // edgeManager.start();
//            try {
//                leadershipBlockingManager.waitForLeadership();
//            } catch (InterruptedException e) {
//                logger.error("等待领导权失败", e);
//                System.exit(1);
//                return;
//            }
            // transferBoardManager.init();
            CompletableFuture.allOf(plateManager.initAsync(), cameraManager.initAsync()).join();
            logger.info("过板台和相机初始化完成");
            cameraPlateAssociationEntityManager.initializeEntities();
            logger.info("相机和过板台关联初始化完成");
            logger.debug("ApplicationReady...");
            return;
        }

        // 应用启动失败后产生这个事件
        if (event instanceof ApplicationFailedEvent) {
            logger.debug("ApplicationFailed...");
            return;
        }
    }
}
