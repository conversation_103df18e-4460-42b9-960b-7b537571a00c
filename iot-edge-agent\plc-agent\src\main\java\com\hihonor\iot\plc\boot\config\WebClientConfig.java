/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.hihonor.iot.plc.boot.ForwardingInterceptor;
import com.hihonor.iot.plc.boot.LeaderInterceptor;


/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Configuration
public class WebClientConfig implements WebMvcConfigurer {

    /**
     * webClientBuilder
     *
     * @return WebClient.Builder
     */
    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }

    /**
     * webClient
     *
     * @return WebClient
     */
    @Bean
    public WebClient webClient() {
        return WebClient.create();
    }


    @Autowired
    @Lazy
    private LeaderInterceptor leaderInterceptor;
    @Autowired
    ForwardingInterceptor forwardingInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(forwardingInterceptor);
    }

}
