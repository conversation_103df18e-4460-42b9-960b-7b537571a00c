/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate;

import java.util.Arrays;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.Util;
import com.hihonor.iot.plc.boot.schedule.CustomScheduled;
import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.hihonor.iot.plc.deviceLog.Device;
import com.hihonor.iot.plc.deviceLog.PlcLogOperation;
import com.hihonor.iot.plc.m2m.M2MService;
import com.hihonor.iot.plc.newplc.PlcConfiguration;
import com.hihonor.iot.plc.newplc.Point;
import com.hihonor.iot.plc.plate.mewutil.MEWClient;
import com.hihonor.iot.plc.plcbase.PlcDeviceService;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Slf4j
@Scope("prototype")
@Component
public class Plate implements Device {
    @Getter
    private String ip;
    @Getter
    private int port;
    @Getter
    private String tag;

    private String thingName;
    @Autowired
    TaskRegistrar taskRegistrar;

    @Autowired
    PlcDeviceService plcDeviceService;

    /**
     * 设备状态地址
     */
    private static final String DEVICE_STATUS_ADDRESS = "DT88";

    /**
     * 进板信号地址
     */
    private static final String LOADING_SIGNAL_ADDRESS = "R2000";

    @Autowired
    private PlcConfiguration plcConfiguration;

    private Plate prox;

    private MEWClient mewClient;

    /**
     * 初始化方法，用于设置IP、端口、物体名称和传输板
     *
     * @param ip        IP地址
     * @param port      端口号
     * @param thingName 物体名称
     * @param porx      传输板
     */
    public void init(String ip, int port, String thingName, String tag, Plate porx) {
        // 将传入的IP赋值给对象的ip属性
        this.ip = ip;
        // 将传入的端口号赋值给对象的port属性
        this.port = port;
        this.tag = tag;
        // 将传入的物体名称赋值给对象的thingName属性
        this.thingName = thingName;
        // 创建一个新的MEWClient对象
        mewClient = new MEWClient();
        // 调用MEWClient对象的初始化方法，传入IP、端口和物体名称
        mewClient.init(ip, port, thingName);
        // 将传入的传输板赋值给对象的prox属性
        this.prox = porx;
        // 调用connect方法，建立连接
        connect();
        // 使用taskRegistrar为该对象注册任务
        taskRegistrar.registerTasksForObject(this);
    }

    @Override
    public String getType() {
        return "plate";
    }

    @Override
    public String getThingName() {
        return thingName;
    }

    public void reInit(String ip, int port, String thingName) {
        mewClient.close();
        this.ip = ip;
        this.port = port;
        this.thingName = thingName;
        mewClient = new MEWClient();
        mewClient.init(ip, port, thingName);
        connect();
    }

    private boolean connect() {
        boolean connected = mewClient.connect(2000);
        if (connected) {
            log.info("设备[{}]连接PLC成功，地址：{}:{}，设备类型：{}", thingName, ip, port, tag);
        } else {
            log.error("设备[{}]连接PLC失败，地址：{}:{}，设备类型：{}", thingName, ip, port, tag);
        }
        return connected;
    }

    /**
     * 根据点位名称读取值
     *
     * @param pointName 点位名称
     * @return 读取到的值的列表，如果读取失败或点位不存在，则返回空列表
     */
    @PlcLogOperation
    public String readValuesByPointName(String pointName) {
        log.info("设备[{}]开始读取点位值，地址：{}:{}，点位名称：{}", thingName, ip, port, pointName);
        Point point = plcConfiguration.getPointByName(pointName);
        if (point != null) {
            String startAddress = point.getStartAddress();
            String endAddress = point.getEndAddress();
            String result;

            if (endAddress == null) {
                // 单个地址
                result = mewClient.readData(startAddress, startAddress);
            } else {
                // 地址范围
                result = mewClient.readData(startAddress, endAddress);
            }

            if (result != null && !result.isEmpty()) {
                log.info("设备[{}]读取点位值成功，地址：{}:{}，点位名称：{}，值：{}",
                        thingName, ip, port, pointName, result);
                return result;
            } else {
                log.error("设备[{}]读取点位值为空，地址：{}:{}，点位名称：{}",
                        thingName, ip, port, pointName);
                return "";
            }
        } else {
            log.error("设备[{}]点位不存在，地址：{}:{}，点位名称：{}", thingName, ip, port, pointName);
        }
        return "";
    }

    @Autowired
    M2MService m2MService;

    @CustomScheduled(fixedRate = 30 * 1000)
    public void updateStatus() {
        try {
            // readValuesByPointName 这个方法可以维护设备连接的状态，如果没有读取成功，设备连接状态会置为FALSE
            String status = null;
            try {
                status = prox.readValuesByPointName("track1State");
            } catch (Exception e) {
                log.error("设备[{}]读取track1State状态失败，地址：{}:{}，原因：{}",
                        thingName, ip, port, e.getMessage());
            }

            // getConnected()方法会返回设备的连接状态
            if (getConnected()) {
                try {
                    // 向数据库写入在设备在线状态
                    plcDeviceService.reportDeviceOnline(thingName);
                } catch (Exception e) {
                    log.error("设备[{}]报告设备在线状态失败，地址：{}:{}，原因：{}",
                            thingName, ip, port, e.getMessage());
                }
                // 向M2M服务报告心跳
                // boolean res = m2MService.sendHeartbeat(thingName, "120");
                // if (res) {
                // log.info("thingName :{} ip:{} heartBeat success", thingName, ip + port);
                // } else {
                // log.info("thingName :{} ip:{} heartBeat failed", thingName, ip + port);
                // }
            } else {
                try {
                    mewClient.close();
                    // 心跳失败 就会重新连接
                    connect();
                    log.info("设备[{}]重新连接成功，地址：{}:{}", thingName, ip, port);
                } catch (Exception e) {
                    log.error("设备[{}]重新连接失败，地址：{}:{}，原因：{}",
                            thingName, ip, port, e.getMessage());
                }
            }

            if (status != null) {
                log.info("设备[{}]状态更新，地址：{}:{}，轨道状态：{}",
                        thingName, ip, port, status);
            }
        } catch (Exception e) {
            log.error("设备[{}]更新状态异常，地址：{}:{}，原因：{}",
                    thingName, ip, port, e.getMessage());
        }
    }

    /**
     * 根据点位名称写入值（只支持R类型地址）
     *
     * @param pointName 点位名称
     * @param value     要写入的值（只能是0或1）
     * @return 是否写入成功
     */
    @PlcLogOperation
    public boolean writeValueByPointName(String pointName, String value) {
        log.info("设备[{}]开始写入点位值，地址：{}:{}，点位名称：{}，值：{}",
                thingName, ip, port, pointName, value);
        Point point = plcConfiguration.getPointByName(pointName);
        if (point != null) {
            String address = point.getAddress();
            String addressType = point.getAddressType();

            if (point.isAddressRange()) {
                log.error("设备[{}]不能写入地址范围，地址：{}:{}，点位名称：{}",
                        thingName, ip, port, pointName);
                return false;
            }

            if ("R".equals(addressType)) {
                // R类型地址，只能写入0或1
                if ("0".equals(value) || "1".equals(value)) {
                    boolean result = mewClient.sendCommon(address, value);
                    if (result) {
                        log.info("设备[{}]写入点位值成功，地址：{}:{}，点位名称：{}，值：{}",
                                thingName, ip, port, pointName, value);
                    } else {
                        log.error("设备[{}]写入点位值失败，地址：{}:{}，点位名称：{}，值：{}",
                                thingName, ip, port, pointName, value);
                    }
                    return result;
                } else {
                    log.error("设备[{}]写入值无效，地址：{}:{}，点位名称：{}，值：{}，R地址只能写入0或1",
                            thingName, ip, port, pointName, value);
                    return false;
                }
            } else {
                log.error("设备[{}]不支持的地址类型，地址：{}:{}，点位名称：{}，地址类型：{}",
                        thingName, ip, port, pointName, addressType);
                return false;
            }
        } else {
            log.error("设备[{}]点位不存在，地址：{}:{}，点位名称：{}",
                    thingName, ip, port, pointName);
            return false;
        }
    }

    public boolean sendBoardingStatus(String value) {
        return writeValueByPointName("track1BoardingStatus", value);
    }

    @PlcLogOperation
    public boolean sendtrack1BoardingNG(String value, String barcode) {
        return writeValueByPointName("track1BoardingStatus", value);
    }

    @PlcLogOperation
    public boolean sendtrack2BoardingNG(String value, String barcode) {
        return writeValueByPointName("track2BoardingStatus", value);
    }

    @PlcLogOperation
    public boolean sendtrack1BoardingOK1(String barcode) {

        return writeValueByPointName("track1BoardingOK", "1");
    }

    @PlcLogOperation
    public String readtrack1BoardingOK() {
        return readValuesByPointName("track1BoardingOK");
    }

    // @PlcLogOperation
    // public boolean sendtrack1BoardingOK0(String barcode) {
    // return writeValueByPointName("track1BoardingOK", "0");
    // }

    @PlcLogOperation
    public boolean sendtrack1BoardingOK0(String barcode) {
        int retries = 2;
        while (retries >= 0) {
            if (writeValueByPointName("track1BoardingOK", "0")) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    // Thread.currentThread().interrupt();
                    log.error("Thread interrupted while waiting", e);
                    return false;
                }
                String status = prox.readtrack1BoardingOK();
                if ("0".equals(status)) {
                    return true;
                }
            }
            retries--;
        }
        return false;
    }

    @PlcLogOperation
    public boolean sendtrack2BoardingOK1(String barcode) {
        return writeValueByPointName("track2BoardingOK", "1");
    }

    @PlcLogOperation
    public String readtrack2BoardingOK() {
        return readValuesByPointName("track2BoardingOK");
    }

    @PlcLogOperation
    public boolean sendtrack2BoardingOK0(String barcode) {
        int retries = 2;
        while (retries >= 0) {
            if (writeValueByPointName("track2BoardingOK", "0")) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    // Thread.currentThread().interrupt();
                    log.error("Thread interrupted while waiting", e);
                    return false;
                }
                String status = prox.readtrack2BoardingOK();
                if ("0".equals(status)) {
                    return true;
                }
            }
            retries--;
        }
        return false;
    }
    //
    // @PlcLogOperation
    // public boolean sendtrack2BoardingOK0(String barcode) {
    // return writeValueByPointName("track2BoardingOK", "0");
    // }

    // @PlcLogOperation
    // public boolean sendtrack1BoardingStatus(String value) {
    // return retryWriteValueByPointName("track1BoardingStatus", value);
    // }
    //
    // @PlcLogOperation
    // public boolean sendtrack2BoardingStatus(String value) {
    // return retryWriteValueByPointName("track2BoardingStatus", value);
    // }
    //
    // @PlcLogOperation
    // public boolean sendtrack1BoardingOK1() {
    // return retryWriteValueByPointName("track1BoardingOK", "1");
    // }
    //
    // @PlcLogOperation
    // public boolean sendtrack1BoardingOK0() {
    // return retryWriteValueByPointName("track1BoardingOK", "0");
    // }
    //
    // @PlcLogOperation
    // public boolean sendtrack2BoardingOK1() {
    // return retryWriteValueByPointName("track2BoardingOK", "1");
    // }
    //
    // @PlcLogOperation
    // public boolean sendtrack2BoardingOK0() {
    // return retryWriteValueByPointName("track2BoardingOK", "0");
    // }
    //
    // private boolean retryWriteValueByPointName(String pointName, String value) {
    // int retries = 3;
    // while (retries > 0) {
    // if (writeValueByPointName(pointName, value)) {
    // return true;
    // }
    // retries--;
    // }
    // return false;
    // }

    public String readFirstTrackBoardEntryStatus() {
        String startAddress = "R2000"; // 一轨进板状态的起始地址
        String endAddress = "R2000"; // 一轨进板状态的结束地址

        if (!getConnected()) {
            log.warn("设备[{}]未连接，无法读取一轨进板状态，地址：{}:{}", thingName, ip, port);
            return "未知状态";
        }

        log.info("设备[{}]开始读取一轨进板状态，地址：{}:{}", thingName, ip, port);
        String result = mewClient.readData(startAddress, endAddress);
        if (result != null && !result.isEmpty()) {
            String status = result.equals("1") ? "禁止进板" : "可以进板";
            log.info("设备[{}]一轨进板状态读取成功，地址：{}:{}，状态：{}",
                    thingName, ip, port, status);
            return status;
        } else {
            log.warn("设备[{}]一轨进板状态读取失败，地址：{}:{}", thingName, ip, port);
            return "未知状态";
        }
    }

    public String readSecondTrackBoardEntryStatus() {
        String startAddress = "R2001"; // 二轨进板状态的起始地址
        String endAddress = "R2001"; // 二轨进板状态的结束地址

        if (!getConnected()) {
            log.warn("设备[{}]未连接，无法读取二轨进板状态，地址：{}:{}", thingName, ip, port);
            return "未知状态";
        }

        log.info("设备[{}]开始读取二轨进板状态，地址：{}:{}", thingName, ip, port);
        String result = mewClient.readData(startAddress, endAddress);
        if (result != null && !result.isEmpty()) {
            String status = result.equals("1") ? "禁止进板" : "可以进板";
            log.info("设备[{}]二轨进板状态读取成功，地址：{}:{}，状态：{}",
                    thingName, ip, port, status);
            return status;
        } else {
            log.warn("设备[{}]二轨进板状态读取失败，地址：{}:{}", thingName, ip, port);
            return "未知状态";
        }
    }

    public String readFirstTrackDeviceStatus() {
        if (tag.contains("永信达") || tag.contains("NUTEK")) {
            return readYongxindaFirstTrackDeviceStatus();
        } else if (tag.contains("国昊")) {
            return readGuohaoFirstTrackDeviceStatus();
        } else {
            log.warn("未知设备品牌: {}", tag);
            return "未知状态";
        }
    }

    public String readSecondTrackDeviceStatus() {
        if (tag.contains("永信达") || tag.contains("NUTEK")) {
            return readYongxindaSecondTrackDeviceStatus();
        } else if (tag.contains("国昊")) {
            return readGuohaoSecondTrackDeviceStatus();
        } else {
            log.warn("未知设备品牌: {}", tag);
            return "未知状态";
        }
    }

    String readGuohaoFirstTrackDeviceStatus() {
        String startAddress = "DT3100"; // 一轨设备状态的起始地址
        String endAddress = "DT3100"; // 一轨设备状态的结束地址

        if (!getConnected()) {
            log.warn("未连接，无法读取国昊一轨设备状态，IP: {}:{}", ip, port);
            return "未知状态";
        }

        String result = mewClient.readData(startAddress, endAddress);
        if (result != null && !result.isEmpty()) {
            log.info("国昊一轨设备状态读取成功: {}", result);
            switch (result) {
                case "1":
                    return "IDLE";
                case "2":
                    return "RUN";
                case "3":
                    return "DOWN";
                case "4":
                    return "IDLE";
                default:
                    return "未知状态";
            }
        } else {
            log.warn("国昊一轨设备状态读取失败或结果为空");
            return "未知状态";
        }
    }

    String readGuohaoSecondTrackDeviceStatus() {
        String startAddress = "DT3101"; // 二轨设备状态的起始地址
        String endAddress = "DT3101"; // 二轨设备状态的结束地址

        if (!getConnected()) {
            log.warn("未连接，无法读取国昊二轨设备状态，IP: {}:{}", ip, port);
            return "未知状态";
        }

        String result = mewClient.readData(startAddress, endAddress);
        if (result != null && !result.isEmpty()) {
            log.info("国昊二轨设备状态读取成功: {}", result);
            switch (result) {
                case "1":
                    return "IDLE";
                case "2":
                    return "RUN";
                case "3":
                    return "DOWN";
                case "4":
                    return "IDLE";
                default:
                    return "未知状态";
            }
        } else {
            log.warn("国昊二轨设备状态读取失败或结果为空");
            return "未知状态";
        }
    }

    String readYongxindaFirstTrackDeviceStatus() {
        String startAddress = "DT88"; // 一轨设备状态的起始地址
        String endAddress = "DT88"; // 一轨设备状态的结束地址

        if (!getConnected()) {
            log.warn("未连接，无法读取永信达一轨设备状态，IP: {}:{}", ip, port);
            return "未知状态";
        }

        String result = mewClient.readData(startAddress, endAddress);
        if (result != null && !result.isEmpty()) {
            log.info("永信达一轨设备状态读取成功: {}", result);
            switch (result) {
                case "0":
                    return "IDLE";
                case "1":
                    return "IDLE";
                case "2":
                    return "RUN";
                case "3":
                    return "DOWN";
                default:
                    return "未知状态";
            }
        } else {
            log.warn("永信达一轨设备状态读取失败或结果为空");
            return "";
        }
    }

    private String readYongxindaSecondTrackDeviceStatus() {
        String startAddress = "DT89"; // 二轨设备状态的起始地址
        String endAddress = "DT89"; // 二轨设备状态的结束地址

        if (!getConnected()) {
            log.warn("未连接，无法读取永信达二轨设备状态，IP: {}:{}", ip, port);
            return "未知状态";
        }

        String result = mewClient.readData(startAddress, endAddress);
        if (result != null && !result.isEmpty()) {
            log.info("永信达二轨设备状态读取成功: {}", result);
            switch (result) {
                case "0":
                    return "IDLE";
                case "1":
                    return "IDLE";
                case "2":
                    return "RUN";
                case "3":
                    return "DOWN";
                default:
                    return "未知状态";
            }
        } else {
            log.warn("永信达二轨设备状态读取失败或结果为空");
            return "未知状态";
        }
    }

    /**
     * 发送轨道进板信号
     *
     * @param trackNumber 轨道编号。如果为0，则向两个轨道（1和2）发送信号；否则向指定轨道发送信号
     * @return 如果信号发送成功返回true，否则返回false
     */
    public boolean sendTrackBoardingSignal(int trackNumber) {
        // 检查连接状态
        if (!getConnected()) {
            log.error("设备[{}]未连接，无法发送进板信号，地址：{}:{}", thingName, ip, port);
            return false;
        }

        // 确定需要发送信号的轨道
        int[] tracks = (trackNumber == 0) ? new int[] { 1, 2 } : new int[] { trackNumber };
        String trackDesc = trackNumber == 0 ? "全部轨道" : "轨道" + trackNumber;
        log.info("设备[{}]开始发送进板信号，地址：{}:{}，目标：{}",
                thingName, ip, port, trackDesc);

        // 遍历需要发送信号的轨道
        for (int track : tracks) {
            String pointName = "track" + track + "BoardingOK";

            // 发送OK1信号
            boolean ok1Result = writeValueByPointName(pointName, "1");
            if (!ok1Result) {
                log.error("设备[{}]发送进板信号失败(1)，地址：{}:{}，轨道：{}",
                        thingName, ip, port, track);
                return false;
            }

            // 等待0.5秒
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("设备[{}]发送进板信号等待中断，地址：{}:{}，轨道：{}",
                        thingName, ip, port, track);
                return false;
            }

            // 发送OK0信号
            boolean ok0Result = writeValueByPointName(pointName, "0");
            if (!ok0Result) {
                log.error("设备[{}]发送进板信号失败(0)，地址：{}:{}，轨道：{}",
                        thingName, ip, port, track);
                return false;
            }
        }

        log.info("设备[{}]发送进板信号成功，地址：{}:{}，目标：{}",
                thingName, ip, port, trackDesc);
        return true;
    }

    /**
     * 读取指定地址范围的数据
     *
     * @param startAddress 起始地址
     * @param endAddress   结束地址
     * @return 读取到的数据，如果读取失败则返回空字符串
     */
    @PlcLogOperation
    public String readDataFromPlc(String startAddress, String endAddress) {
        if (!getConnected()) {
            log.error("设备[{}]未连接，无法读取数据，地址：{}:{}，起始地址：{}，结束地址：{}",
                    thingName, ip, port, startAddress, endAddress);
            return "";
        }

        log.info("设备[{}]开始读取数据，地址：{}:{}，起始地址：{}，结束地址：{}",
                thingName, ip, port, startAddress, endAddress);
        String result = mewClient.readData(startAddress, endAddress);
        if (result != null && !result.isEmpty()) {
            log.info("设备[{}]读取数据成功，地址：{}:{}，起始地址：{}，结束地址：{}，数据：{}",
                    thingName, ip, port, startAddress, endAddress, result);
            return result;
        } else {
            log.error("设备[{}]读取数据为空，地址：{}:{}，起始地址：{}，结束地址：{}",
                    thingName, ip, port, startAddress, endAddress);
            return "";
        }
    }

    /**
     * 发送命令到PLC
     *
     * @param address 地址
     * @param value   要写入的值
     * @return 是否发送成功
     */
    @PlcLogOperation
    public boolean writePointValue(String address, String... value) {
        if (!getConnected()) {
            log.error("设备[{}]未连接，无法发送命令，地址：{}:{}，目标地址：{}",
                    thingName, ip, port, address);
            return false;
        }

        log.info("设备[{}]开始发送命令，地址：{}:{}，目标地址：{}，值：{}",
                thingName, ip, port, address, Arrays.toString(value));
        boolean result = mewClient.sendCommon(address, value);
        if (result) {
            log.info("设备[{}]发送命令成功，地址：{}:{}，目标地址：{}",
                    thingName, ip, port, address);
        } else {
            log.error("设备[{}]发送命令失败，地址：{}:{}，目标地址：{}",
                    thingName, ip, port, address);
        }
        return result;
    }

    /**
     * 发送禁止通过信号到指定轨道。如果传入的轨道号为0，则表示需要发送信号到两个轨道。
     *
     * @param trackNumber 轨道号。如果为0，则表示发送信号到轨道1和轨道2。
     * @return 如果信号发送成功，返回true；否则返回false。
     */
    public boolean sendTrackProhibitPassingSignal(int trackNumber) {
        // 检查连接状态
        if (!getConnected()) {
            log.info("thingName :{}  ip:{} 未连接，无法发送命令", thingName, ip + port);
            return false;
        }

        // 确定需要发送信号的轨道
        int[] tracks = (trackNumber == 0) ? new int[] { 1, 2 } : new int[] { trackNumber };

        for (int track : tracks) {
            String pointName = "track" + track + "BoardingStatus";
            log.info("thingName :{}  ip:{} 发送禁止通过信号，轨道号:{}", thingName, ip + port, track);

            // 发送状态 "1"
            boolean status1Result = writeValueByPointName(pointName, "1");
            if (!status1Result) {
                log.info("thingName :{}  ip:{} 发送禁止通过信号失败，轨道号:{}", thingName, ip + port, track);
                return false;
            }

            // 短暂等待，确保状态 "1" 被正确处理
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            // 发送状态 "0"
            boolean status0Result = writeValueByPointName(pointName, "0");
            if (!status0Result) {
                log.info("thingName :{}  ip:{} 发送禁止通过信号失败，轨道号:{}", thingName, ip + port, track);
                return false;
            }
        }

        log.info("thingName :{}  ip:{} 发送禁止通过信号成功", thingName, ip + port);
        return true;
    }

    /**
     * 发送轨道信号
     *
     * @param isBoarding  是否为进板信号。true表示进板信号，false表示禁止通行信号
     * @param trackNumber 轨道编号。如果为0，则向两个轨道（1和2）发送信号；否则向指定轨道发送信号
     * @return 如果信号发送成功返回true，否则返回false
     */
    @PlcLogOperation
    public boolean sendTrackSignal(boolean isBoarding, int trackNumber, String command) {
        // 检查连接状态
        if (!getConnected()) {
            log.info("设备名称:{}  IP地址:{} 未连接，无法发送命令", thingName, ip + ":" + port);
            return false;
        }

        log.info("设备名称:{}  IP地址:{} 发送{}信号 轨道编号:{}",
                thingName, ip + ":" + port,
                isBoarding ? "进板" : "禁止通行",
                trackNumber == 0 ? "全部" : trackNumber);

        boolean result;
        if (isBoarding) {
            result = sendTrackBoardingSignal(trackNumber);
        } else {
            result = sendTrackProhibitPassingSignal(trackNumber);
        }
        if (command != null || !command.equals("") || Util.isPlateInstruction(command)) {
            writePointValue(command, "1");

        }

        if (result) {
            log.info("设备名称:{}  IP地址:{} 发送{}信号成功 轨道编号:{}",
                    thingName, ip + ":" + port,
                    isBoarding ? "进板" : "禁止通行",
                    trackNumber == 0 ? "全部" : trackNumber);
        } else {
            log.error("设备名称:{}  IP地址:{} 发送{}信号失败 轨道编号:{}",
                    thingName, ip + ":" + port,
                    isBoarding ? "进板" : "禁止通行",
                    trackNumber == 0 ? "全部" : trackNumber);
        }

        return result;
    }

    /**
     * 获取连接状态
     *
     * @return 连接状态
     */
    @Override
    public boolean getConnected() {
        return mewClient.getConnected();
    }

    /**
     * 关闭连接
     */
    public void destroy() {
        taskRegistrar.cancelTask(this, "updateStatus");
        log.info("设备[{}]开始关闭连接，地址：{}:{}", thingName, ip, port);
        try {
            mewClient.close();
            mewClient = null;
            log.info("设备[{}]关闭连接成功，地址：{}:{}", thingName, ip, port);
        } catch (Exception e) {
            log.error("设备[{}]关闭连接失败，地址：{}:{}，原因：{}",
                    thingName, ip, port, e.getMessage());
        }
    }

    /**
     * 控制第一轨道禁止通行并在一秒后复位。
     * 首先发送禁止通行信号（"1"），如果成功则休眠1秒，
     * 然后发送允许通行信号（"0"）进行复位。
     *
     * @return 如果所有操作（发送禁止、休眠、发送允许）都成功，则返回 true；否则返回 false。
     */
    @PlcLogOperation
    public boolean controlTrack1ProhibitionWithReset() {
        log.info("设备[{}] 开始控制第一轨道通行状态并计划复位", thingName);

        // 检查设备是否在线
        if (!getConnected()) {
            log.warn("设备[{}] 未连接，无法控制第一轨道通行状态", thingName);
            return false;
        }

        // 1. 发送禁止通行信号 "1"
        log.info("设备[{}] 发送第一轨道禁止通行信号", thingName);
        boolean prohibitSent = sendtrack1BoardingNG("1", null);

        if (!prohibitSent) {
            log.error("设备[{}] 发送第一轨道禁止通行信号失败", thingName);
            return false;
        }
        log.info("设备[{}] 发送第一轨道禁止通行信号成功", thingName);

        // 2. 休眠1秒
        try {
            log.debug("设备[{}] 禁止信号发送成功，休眠1秒后复位", thingName);
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.warn("设备[{}] 在控制第一轨道通行状态休眠时被中断，异常: {}", thingName, e.getMessage());
            Thread.currentThread().interrupt(); // 恢复中断状态
        }

        // 3. 再次检查在线状态并发送复位信号
        if (!getConnected()) {
            log.warn("设备[{}] 在休眠后断开连接，无法发送复位信号。禁止信号可能未被复位", thingName);
            return false; // 因为复位失败，所以整体操作失败
        }

        // 4. 发送允许通行信号 "0"
        log.info("设备[{}] 发送第一轨道允许通行/复位信号", thingName);
        boolean allowSent = sendtrack1BoardingNG("0", null);

        if (!allowSent) {
            log.error("设备[{}] 发送第一轨道允许通行/复位信号失败", thingName);
            return false;
        }

        log.info("设备[{}] 成功控制第一轨道通行状态并复位", thingName);
        return true;
    }

    /**
     * 控制第二轨道禁止通行并在一秒后复位。
     * 首先发送禁止通行信号（"1"），如果成功则休眠1秒，
     * 然后发送允许通行信号（"0"）进行复位。
     *
     * @return 如果所有操作（发送禁止、休眠、发送允许）都成功，则返回 true；否则返回 false。
     */
    @PlcLogOperation
    public boolean controlTrack2ProhibitionWithReset() {
        log.info("设备[{}] 开始控制第二轨道通行状态并计划复位", thingName);

        // 检查设备是否在线
        if (!getConnected()) {
            log.warn("设备[{}] 未连接，无法控制第二轨道通行状态", thingName);
            return false;
        }

        // 1. 发送禁止通行信号 "1"
        log.info("设备[{}] 发送第二轨道禁止通行信号", thingName);
        boolean prohibitSent = sendtrack2BoardingNG("1", null);

        if (!prohibitSent) {
            log.error("设备[{}] 发送第二轨道禁止通行信号失败", thingName);
            return false;
        }
        log.info("设备[{}] 发送第二轨道禁止通行信号成功", thingName);

        // 2. 休眠1秒
        try {
            log.debug("设备[{}] 禁止信号发送成功，休眠1秒后复位", thingName);
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.warn("设备[{}] 在控制第二轨道通行状态休眠时被中断，异常: {}", thingName, e.getMessage());
            Thread.currentThread().interrupt(); // 恢复中断状态
        }

        // 3. 再次检查在线状态并发送复位信号
        if (!getConnected()) {
            log.warn("设备[{}] 在休眠后断开连接，无法发送复位信号。禁止信号可能未被复位", thingName);
            return false; // 因为复位失败，所以整体操作失败
        }

        // 4. 发送允许通行信号 "0"
        log.info("设备[{}] 发送第二轨道允许通行/复位信号", thingName);
        boolean allowSent = sendtrack2BoardingNG("0", null);

        if (!allowSent) {
            log.error("设备[{}] 发送第二轨道允许通行/复位信号失败", thingName);
            return false;
        }

        log.info("设备[{}] 成功控制第二轨道通行状态并复位", thingName);
        return true;
    }

}
