/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.thingworx.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.common.manager.EdgeManager;
import com.hihonor.iot.plc.thing.ThingManagement;
import com.hihonor.iot.plc.thingworx.ThingworxService;
import com.thingworx.communications.client.ConnectedThingClient;
import com.thingworx.types.InfoTable;
import com.thingworx.types.collections.ValueCollection;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Component
@Slf4j
public class GetPropertys {


    static String serviceName = "getValues";
    /**
     * thingworxService
     */
    @Autowired
    protected ThingworxService thingworxService;
    /**
     * thingManagement
     */
    @Autowired
    protected ThingManagement thingManagement;
    /**
     * edgeManager
     */
    @Autowired
    protected EdgeManager edgeManager;
    /**
     * client
     */
    @Autowired
    protected ConnectedThingClient client;


    /**
     * 获取属性值
     *
     * @param thingName 设备名称
     * @return 属性值
     */
    public Map<String, String> invorke(String thingName) {
        try {
            Map<String, String> res = new HashMap();
            InfoTable infoTable = edgeManager.invokeService(thingName, serviceName, new ValueCollection());
            for (int i = 0; i < infoTable.getLength(); i++) {
                ValueCollection valueCollection = infoTable.getRow(i);
                String name = valueCollection.getStringValue("name");
                String value = valueCollection.getValue("value").toString();
                res.put(name, value);
            }
            return res;
        } catch (Exception ex) {
            log.info("invorke error: {}", ex.getMessage());
            return null;
        }
    }

    /**
     * 获取属性值
     *
     * @param thingName   设备名称
     * @param proertyName 属性名称
     * @return 属性值
     * @throws Exception 异常
     */
    public String getProerty(String thingName, String proertyName) throws Exception {
        InfoTable infoTable = edgeManager.readProperty(thingName, proertyName);
        ValueCollection valueCollection = infoTable.getRow(0);
        String res = valueCollection.getStringValue(proertyName);
        return res;
    }


}
