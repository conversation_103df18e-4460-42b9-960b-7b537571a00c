/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-30
 */
@Data
public class TransferBoardNameRequest {

    @NotNull(message = "过板台名称不能为空")
    private String name;
}
