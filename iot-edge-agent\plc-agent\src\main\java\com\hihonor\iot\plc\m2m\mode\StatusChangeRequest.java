/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.m2m.mode;



import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.hihonor.iot.plc.Util;
import com.hihonor.iot.plc.plate.PlateStatus;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatusChangeRequest {
    private String iotId;
    private String equipmentSn;
    private String statusMode="autoMode";
    private String statusGeneral;
    private String statusCode;
    private String statusDesc;
    private String statusTime;
    // getters and setters
    /**
     * 根据给定的 PlateStatus 创建状态变更请求列表。
     *
     * @param plateStatus 包含轨道状态的 PlateStatus 对象
     * @return 状态变更请求列表
     */
    public static List<StatusChangeRequest> createStatusChangeRequestFromPlateStatus(PlateStatus plateStatus) {
        List<StatusChangeRequest> requests = new ArrayList<>();

        for (PlateStatus.TrackStatus trackStatus : plateStatus.getTrackStatuses()) {
            StatusChangeRequest request = new StatusChangeRequest();

            // 根据 resourceId 和 trackName 构造 IoT ID
            String baseIotId = plateStatus.getResourceId();
            String trackName = trackStatus.getTrackName();
            if ("1".equals(trackName) || "2".equals(trackName)) {
                request.setIotId(baseIotId + "_" + trackName);
            } else {
                request.setIotId(baseIotId);
            }

            // 从 attributes 中获取 deviceStatus 设置 statusGeneral
            if (trackStatus.getAttributes() != null && trackStatus.getAttributes().containsKey("deviceStatus")) {
                request.setStatusGeneral(trackStatus.getAttributes().get("deviceStatus"));
            }

            // 可选地设置其他字段
             request.setEquipmentSn(request.getIotId()) ;// 如果有设备序列号则设置
            // request.setStatusCode(...);   // 如果有状态代码则设置
            // request.setStatusDesc(...);    // 如果有状态描述则设置
             request.setStatusTime(Util.getcurrentTime());     // 如果有状态时间则设置

            requests.add(request);
        }

        return requests;
    }

}