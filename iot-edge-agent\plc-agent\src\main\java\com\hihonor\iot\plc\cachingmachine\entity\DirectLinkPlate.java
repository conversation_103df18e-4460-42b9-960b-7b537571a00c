/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.cachingmachine.entity;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.edge.common.manager.EdgeManager;
import com.hihonor.iot.edge.sdk.entity.EntityThing;
import com.hihonor.iot.plc.Util;
import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.hihonor.iot.plc.cachingmachine.repo.PlateDevice;
import com.hihonor.iot.plc.cachingmachine.repo.PlateDeviceService;
import com.hihonor.iot.plc.m2m.M2MService;
import com.hihonor.iot.plc.m2m.mode.AlarmRequest;
import com.hihonor.iot.plc.m2m.mode.M2MResponse;
import com.hihonor.iot.plc.plate.mewutil.MEWClient;
import com.hihonor.iot.plc.thing.ThingInterface;
import com.hihonor.iot.plc.thing.ThingTemplate;
import com.hihonor.iot.plc.thingworx.service.GetPropertys;
import com.hihonor.iot.plc.thingworx.service.SetPropertys;
import com.thingworx.types.InfoTable;
import com.thingworx.types.collections.ValueCollection;
import com.thingworx.types.primitives.JSONPrimitive;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-11-13
 */

@Slf4j
@Component
@ThingTemplate(value = "PLCTemplate", tag = "plc:plate1127_directLink")
@Scope("prototype")
public class DirectLinkPlate extends EntityThing implements ThingInterface {
    private MEWClient client;
    @Autowired
    ApplicationContext context;
    @Autowired
    EdgeManager edgeManager;
    @Autowired
    GetPropertys getPropertys;
    Config config;
    /**
     * 用于缓存数据的Map
     */
    protected Map<String, String> cachedData;
    /**
     * M2M服务
     */
    @Autowired
    protected M2MService m2mService;

    @Autowired
    SetPropertys setPropertys;
    /**
     * 用于缓存设备信息的服务
     */
    @Autowired
    protected PlateDeviceService plateDeviceService;
    @Autowired
    TaskRegistrar taskRegistrar;


    @Override
    public void initialize(JSONObject json) {
        try {
            setName(json.getString("name"));
            edgeManager.bindThing(this);
            config = new Config(getConfig());
        } catch (Exception ex) {
            log.error("initialize error", ex);
        }
        client = context.getBean(MEWClient.class);
        log.info("config:{}", config.toString());
        log.info("{} decivec initialize complete", getName());
        client.init(config.getIp(), Integer.parseInt(config.getPort()), getName());
        cachedData = fetchData();
        taskRegistrar.registerTasksForObject(this);
    }

    Map<String, String> fetchData() {
        Map<String, String> result = new HashMap<>();
        List<Property> properties = config.getProperties();
        for (Property property : properties) {
            String response = client.readData(property.getAddress(), null);
            if (response == null || response.isEmpty()) {
                log.info("fetchData error: No response for address: {}", property.getAddress());
                // 跳过当前迭代
                continue;
            }
            result.put(property.getName(), response);
        }

        // 检查是否获取到了任何数据
        if (result.isEmpty()) {
            log.info("fetchData: No data fetched");
            return null;
        } else {
            result.put("statusTime", Util.getcurrentTime());
            result.put("iotId", getName());
            log.info("fetchData: {}", result);
            return result;
        }
    }

    /**
     * 定时更新数据
     */
 //   @CustomScheduled(fixedRate = 5000)
    public synchronized void update() {
        long start = System.currentTimeMillis();
        Map newDate = fetchData();
        if (newDate != null && !newDate.isEmpty()) {
            checkAndSendAdvancedAlarm(cachedData, newDate);
            checkAndSendStatusChange(cachedData, newDate);
            synchronized (this) {
                cachedData = newDate;
            }

            insertRepo(newDate);
            reportToThingworx(newDate);
        }
        long end = System.currentTimeMillis();
        log.info("update complete, time: {}ms", end - start);
    }


    void insertRepo(Map data) {
        PlateDevice plateDevice = new PlateDevice();
        plateDevice.setData(data.toString());
        plateDevice.setDeviceId(getName());
        plateDevice.setTimestamp(new Timestamp(new Date().getTime()));
        plateDeviceService.insert(plateDevice);
    }

    void reportToThingworx(Map<String, String> originalMap) {
        // 需要移除的键列表
        Set<String> keysToRemove = new HashSet<>(Arrays.asList("iotId", "statusTime"));

        // 创建新的Map，并复制原始Map中的数据
        Map<String, String> mapCopy = new HashMap<>(originalMap);

        // 从复制的Map中移除特定的键
        keysToRemove.forEach(mapCopy::remove);

        List<Map<String, Object>> list = new ArrayList<>();

        // 把Map转成List<Map<String, Object>>
        for (Map.Entry<String, String> entry : mapCopy.entrySet()) {
            Map<String, Object> temp = new HashMap<>();
            temp.put("name", entry.getKey());
            temp.put("value", entry.getValue());
            list.add(temp);
        }
        log.info("reportToThingworx:{}", list);

        setPropertys.invorke(getName(), list);
    }


    private void checkAndSendStatusChange(Map<String, String> cachedData, Map newDate) {
    }

    private void checkAndSendAdvancedAlarm(Map<String, String> cachedData, Map newDate) {
        checkAndSendAlarmForAttribute(cachedData, newDate, "STATE", "3");

    }


    @Override
    public boolean getOnlineStatus() {
        return client.getConnected();
    }

    @Override
    public Map<String, String> getAttributes() {
        return getValuesSynchronously();
    }

    /**
     * 同步获取数据
     *
     * @return 数据
     */
    public synchronized Map<String, String> getValuesSynchronously() {
        if (cachedData != null) {
            // 复制数据以返回
            Map<String, String> dataToReturn = new HashMap<>(cachedData);
            return dataToReturn;
        } else {
            return null;
        }
    }

    @Override
    public boolean sendConfiguration(List<Map<String, Object>> config) {
        boolean res = false;
        for (Map<String, Object> item : config) {
            String name = (String) item.get("name");
            String value = (String) item.get("value");
            String adress = this.config.getAddressByName(name);
            res = client.sendCommon(adress, value);
        }
        log.info(" thingName:{}sendConfiguration:{}", getName(), res);
        return res;

    }

    @Override
    public void updating(JSONObject json) {
    }


    @Override
    public void destroy() {
        try {
            client.close();
            taskRegistrar.cancelTask(this, "update");
        } catch (Exception ex) {
            log.error("destroy error", ex);
        }


    }

    @Override
    public void onBind() {

    }

    @Override
    public void unBind() {

    }

    JSONObject getConfig() {
        JSONObject res = null;
        try {

            InfoTable infoTable = edgeManager.readProperty(getName(), "config");
            ValueCollection valueCollection = infoTable.getRow(0);
            JSONPrimitive jsonPrimitive = (JSONPrimitive) valueCollection.getPrimitive("config");
            res = jsonPrimitive.getValue();
            log.info("config:{}", res.toString());
        } catch (Exception ex) {
            log.info("getConfig error", ex);

        }
        return res;
    }

    /**
     * 检查并发送报警
     *
     * @param oldMap        旧数据
     * @param newMap        新数据
     * @param attributeName 属性名称
     * @param alarmState    报警状态
     * @param trace         跟踪信息
     */
    protected void checkAndSendAlarmForAttribute(Map<String, String> oldMap, Map<String, String> newMap, String attributeName, String alarmState, String trace) {

        String oldState = oldMap.getOrDefault(attributeName, null);
        String newState = newMap.getOrDefault(attributeName, null);

        try {
            if (!Objects.equals(alarmState, oldState) && Objects.equals(alarmState, newState)) {
                // 从非告警到告警状态，触发报警
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), trace, true));
                log.info("Alarm triggered for {}: AlarmSet, response: {}", attributeName, response);
            } else if (Objects.equals(alarmState, oldState) && !Objects.equals(alarmState, newState)) {
                // 从告警到非告警状态，触发报警消除
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), trace, false));
                log.info("Alarm triggered for {}: AlarmClear, response: {}", attributeName, response);
            }
        } catch (Exception e) {
            log.error("Error when checking and sending alarm for attribute {}", attributeName, e);
        }
    }

    /**
     * 检查并发送报警
     *
     * @param oldMap        旧数据
     * @param newMap        新数据
     * @param attributeName 属性名称
     * @param alarmState    报警状态
     */
    protected void checkAndSendAlarmForAttribute(Map<String, String> oldMap, Map<String, String> newMap, String attributeName, String alarmState) {

        String oldState = oldMap.getOrDefault(attributeName, null);
        String newState = newMap.getOrDefault(attributeName, null);

        try {
            if (!Objects.equals(alarmState, oldState) && Objects.equals(alarmState, newState)) {
                // 从非告警到告警状态，触发报警
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), "", true));
                log.info("Alarm triggered for {}: AlarmSet, response: {}", attributeName, response);
            } else if (Objects.equals(alarmState, oldState) && !Objects.equals(alarmState, newState)) {
                // 从告警到非告警状态，触发报警消除
                M2MResponse response = m2mService.sendAlarm(AlarmRequest.build(getName(), "", false));
                log.info("Alarm triggered for {}: AlarmClear, response: {}", attributeName, response);
            }
        } catch (Exception e) {
            log.error("Error when checking and sending alarm for attribute {}", attributeName, e);
        }
    }

    /**
     * 用于存储配置信息的类。
     */
    @Data
    public static class Config {
        private String port;
        private String ip;
        private List<Property> properties;

        private Map<String, String> propertyMap; // 用于存储属性名称和地址的映射

        public Config(JSONObject json) throws JSONException {
            this.port = json.getString("port");
            this.ip = json.getString("ip");

            this.properties = new ArrayList<>();
            this.propertyMap = new HashMap<>(); // 初始化映射表

            JSONArray propsArray = json.getJSONArray("properties");
            for (int i = 0; i < propsArray.length(); i++) {
                JSONObject propObject = propsArray.getJSONObject(i);
                Property property = new Property(propObject);
                this.properties.add(property);
                this.propertyMap.put(property.getName(), property.getAddress()); // 将属性添加到映射表中
            }
        }

        /**
         * 根据属性名称获取属性地址。
         *
         * @param name 属性的名称
         * @return 属性的地址。如果找不到指定名称的属性，则返回 null。
         */
        public String getAddressByName(String name) {
            return propertyMap.get(name); // 从映射表中获取地址
        }


        // Getter and setter methods here
        // ...
    }

    /**
     * 用于存储属性信息的类。
     */
    @Data
    public static class Property {
        private String name;
        private String address;

        public Property(JSONObject json) throws JSONException {
            this.name = json.getString("name");
            this.address = json.getString("adress"); // 注意JSON中的拼写
        }

        // Getter and setter methods here
        // ...
    }

}
