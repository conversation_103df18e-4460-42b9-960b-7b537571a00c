/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.boot;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Aspect
@Component
@Slf4j
@DependsOn("clusterManager")
public class MasterAspect {

    @Autowired
    ClusterManager clusterManager;

    /**
     * 检查是否是主节点
     *
     * @param joinPoint 切点
     * @return Object
     * @throws Throwable 异常
     */
    @Around("@annotation(com.hihonor.iot.plc.boot.RunAsMaster)")
    public Object runAsMaster(ProceedingJoinPoint joinPoint) throws Throwable {
        while (true) {
            // 使用 LeaderElectionService 来获取当前节点是否是 Leader
            if (clusterManager.isLeader()) {
                log.info("Running method as MASTER");
                return joinPoint.proceed();
            }
            log.warn("Not MASTER. Waiting to become MASTER...");

            // 等待一段时间再次检查
            Thread.sleep(5000);
        }
    }
}

