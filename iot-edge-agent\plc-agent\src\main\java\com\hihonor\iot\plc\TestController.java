/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hihonor.iot.plc.deviceLog.OperationLogService;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@RestController
public class TestController {

    @Autowired
    OperationLogService operationLogService;

    /**
     * 简单的测试端点，用于验证控制器方法的调用。
     */
    @GetMapping("/test")
    public void testMethod() {
        // 方法体为空，仅用于测试调用
        operationLogService.cleanupOperationLogs();
    }
}
