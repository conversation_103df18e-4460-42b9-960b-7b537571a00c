/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
public class BatchReadTransferBoardPropertiesResponse {
    private List<TransferBoardPropertyResult> transferBoardPropertyResults; // 过板台属性读取结果列表

    @Data
    public static class TransferBoardPropertyResult {
        private String name; // 过板台名称
        private boolean success; // 读取是否成功
        private String message; // 读取结果信息
        private Map<String, String> propertyValues; // 属性值MAP,key为属性名称,value为属性值列表

        public TransferBoardPropertyResult(String name, boolean success, String message, Map<String, String> propertyValues) {
            this.name = name;
            this.success = success;
            this.message = message;
            this.propertyValues = propertyValues;
        }

        public TransferBoardPropertyResult(String name, boolean success, String message) {
            this.name = name;
            this.success = success;
            this.message = message;
        }
    }
}
