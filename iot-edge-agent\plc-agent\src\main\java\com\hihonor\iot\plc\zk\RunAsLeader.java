/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.zk;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 主节点运行注解，标记的方法只在主节点执行
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RunAsLeader {
    /**
     * 等待成为主节点的最长时间(毫秒)，-1表示无限等待
     *
     * @return 等待超时时间
     */
    long waitTimeoutMs() default -1;

    /**
     * 如果不是主节点，是否抛出异常
     *
     * @return 是否抛出异常
     */
    boolean throwException() default false;
}