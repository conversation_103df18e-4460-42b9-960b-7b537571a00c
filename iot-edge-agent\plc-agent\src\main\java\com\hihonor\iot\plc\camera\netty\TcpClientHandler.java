/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera.netty;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Slf4j
public class TcpClientHandler extends SimpleChannelInboundHandler<String> {

    private static final long HEARTBEAT_INTERVAL = 30; // 心跳间隔(秒)
    private static final long HEARTBEAT_TIMEOUT = 90; // 心跳超时(秒)

    private ScheduledFuture<?> heartbeatTask;
    private long lastHeartbeatTime = System.currentTimeMillis();

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        super.channelActive(ctx);
        startHeartbeat(ctx); // 启动心跳任务
        lastHeartbeatTime = System.currentTimeMillis();
    }

    /**
     * 启动心跳任务。
     */
    private void startHeartbeat(ChannelHandlerContext ctx) {
        heartbeatTask = ctx.executor().scheduleAtFixedRate(() -> {
            if (System.currentTimeMillis() - lastHeartbeatTime > HEARTBEAT_TIMEOUT * 1000) {
                // 心跳超时，认为设备已离线
                stopHeartbeat();
                ctx.close(); // 关闭连接
                ConnectionManager.notifyDeviceDisconnected((NioSocketChannel) ctx.channel());
            } else {
                // 发送心跳指令
                ctx.writeAndFlush("KEYENCE");
                log.info("Sent heartbeat to {}", ctx.channel().remoteAddress());
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
    }

    /**
     * 停止心跳任务。
     */
    private void stopHeartbeat() {
        if (heartbeatTask != null) {
            heartbeatTask.cancel(true);
            heartbeatTask = null;
        }
    }

    /**
     * 处理从网络通道读取并解码的消息。
     * 当有新消息到达时,Netty 会自动调用此方法。
     *
     * @param ctx 通道处理器上下文
     * @param msg 解码后的消息
     * @throws Exception 处理消息时可能抛出的异常
     */
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String msg) {
        try {
            log.info("收到解码后的消息: {}", msg);

            if (msg == null) {
                log.warn("收到空消息");
                return;
            }

            MessageType messageType = getMessageType(msg);
            NioSocketChannel channel = (NioSocketChannel) ctx.channel();

            switch (messageType) {
                case BARCODE:
                    try {
                        InetSocketAddress remoteAddr = (InetSocketAddress) ctx.channel().remoteAddress();
                        if (remoteAddr == null) {
                            log.warn("从未连接的通道收到条码");
                            break;
                        }
                        String ip = remoteAddr.getHostString();
                        try {
                            if (!BarcodeCache.getInstance().isDuplicateBarcode(ip, msg)) {
                                if (BarcodeQueueManager.enqueueBarcode(channel, msg)) {
                                    log.info("成功将新条码加入队列: {}", msg);
                                } else {
                                    log.warn("条码入队失败: {}", msg);
                                }
                            } else {
                                log.info("检测到重复条码,已忽略: {}", msg);
                            }
                        } catch (IllegalStateException e) {
                            log.error("条码缓存操作失败: {}", e.getMessage());
                        }
                    } catch (Exception e) {
                        log.error("处理条码消息时发生错误: {}", e.getMessage());
                    }
                    break;
                case ERROR:
                    try {
                        if (BarcodeQueueManager.enqueueBarcode(channel, msg)) {
                            log.info("成功将错误消息加入队列: {}", msg);
                        } else {
                            log.warn("错误消息入队失败: {}", msg);
                        }
                    } catch (Exception e) {
                        log.error("处理错误消息时发生异常: {}", e.getMessage());
                    }
                    break;
                case HEARTBEAT:
                    log.info("收到心跳消息: {}", msg);
                    break;
                case INVALID:
                    log.warn("收到无效消息: {}", msg);
                    break;
            }

            lastHeartbeatTime = System.currentTimeMillis();
        } catch (Exception e) {
            log.error("消息处理过程中发生未预期的异常: {}", e.getMessage());
        }
    }

    /**
     * 消息类型枚举
     */
    private enum MessageType {
        BARCODE, // 条码消息
        ERROR, // ERROR消息
        HEARTBEAT, // 心跳消息
        INVALID // 无效消息
    }

    /**
     * 判断消息类型
     *
     * @param message 接收到的消息
     * @return 消息类型
     */
    private MessageType getMessageType(String message) {
        // 检查是否为ERROR消息
        if ("ERROR".equals(message)) {
            return MessageType.ERROR;
        }

        // 检查是否为心跳消息
        if (message.contains("OK")) {
            return MessageType.HEARTBEAT;
        }

        // 检查是否为有效条码
        if (isValidBarcode(message)) {
            return MessageType.BARCODE;
        }

        return MessageType.INVALID;
    }

    /**
     * 检查是否为有效的条码数据
     *
     * @param message 要检查的消息
     * @return true 如果是有效的条码，false 否则
     */
    private boolean isValidBarcode(String message) {
        // 检查长度是否大于等于16位
        if (message.length() < 16) {
            return false;
        }

        // 检查是否只包含英文字母和数字
        return message.chars().allMatch(c -> Character.isLetterOrDigit(c));
    }

    // 在 Netty 中，channelReadComplete(ChannelHandlerContext ctx) 方法是在当前 Channel
    // 的所有入站消息都被读取并处理完毕后调用的。这通常发生在以下情况下：
    // 何时调用 channelReadComplete
    // 消息读取完成:
    // 当 channelRead0 或其他处理方法（如 channelRead）处理完所有入站消息后，Netty 会调用 channelReadComplete
    // 方法。这表示当前 Channel 中的所有可读消息都已被处理。
    // 在 channelRead0 之后:
    // channelReadComplete 方法是在所有入站消息的处理完成后被调用的。因此，如果你在 channelRead0
    // 中处理了某些消息，随后没有更多的消息需要读取，Netty 将会调用这个方法。
    // Flush 操作:
    // 在 channelReadComplete 方法中，你可以调用 ctx.flush()
    // 来确保所有待发送的出站消息都被写入到网络通道。这是一个重要的步骤，因为它确保了任何未发送的数据都能及时发送出去。
    // 典型用法
    // 通常情况下，你可能不需要在每个处理器中实现
    // channelReadComplete，除非你有特定的逻辑需要在读取完成时执行。以下是一个示例，展示了如何使用这个方法：
    // java
    // @Override
    // public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
    // log.info("Read complete for channel: " + ctx.channel().remoteAddress());
    // ctx.flush(); // Flush the channel to send any pending messages
    // }
    //
    // 总结
    // 调用时机: channelReadComplete 在所有入站消息处理完成后被调用。
    // 主要用途: 这个方法通常用于记录日志、执行清理操作或确保所有待发送的数据被写入通道。
    // 通过实现这个方法，你可以更好地控制数据流，并确保在消息处理完成后采取适当的行动。
    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        log.info("Read complete for channel: " + ctx.channel().remoteAddress());
        ctx.flush(); // Flush the channel to send any pending messages
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        super.channelInactive(ctx);
        stopHeartbeat();
        log.info("Device disconnected: {}", ctx.channel().remoteAddress());

        // 注释掉以下两行清理代码
        // InetSocketAddress remoteAddress = (InetSocketAddress)
        // ctx.channel().remoteAddress();
        // String host = remoteAddress.getHostString();
        // BarcodeCache.getInstance().removeDevice(host);
        // int remaining = BarcodeQueueManager.cleanupQueue((NioSocketChannel)
        // ctx.channel());
    }

    /**
     * 异常捕获处理方法
     * 
     * 只在出现网络异常时关闭连接，其他异常只记录日志，不中断连接。
     *
     * @param ctx   通道处理器上下文
     * @param cause 发生的异常
     */
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        if (cause instanceof IOException) {
            // 网络异常处理
            stopHeartbeat();
            ctx.close();
        } else {
            // 业务异常处理
            log.error("业务异常", cause);
        }
    }
}