/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hihonor.iot.plc.cachingmachine.controller.ApiResponse;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchReadTransferBoardPropertiesResponse;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesRequest;
import com.hihonor.iot.plc.newplc.controller.request.BatchWriteTransferBoardPropertiesResponse;
import com.hihonor.iot.plc.newplc.controller.request.TransferBoardBatchWriteSignalRequest;
import com.hihonor.iot.plc.newplc.modle.DeviceStatus;
import com.hihonor.iot.plc.newplc.TransferBoardManager;
import com.hihonor.iot.plc.newplc.modle.TransferBoardSignal;
import com.hihonor.iot.plc.newplc.modle.TransferBoardStatus;
import com.hihonor.iot.plc.newplc.controller.request.TransferBoardBatchAddRequest;
import com.hihonor.iot.plc.newplc.controller.request.TransferBoardBatchRemoveRequest;
import com.hihonor.iot.plc.newplc.controller.request.TransferBoardNameRequest;
import com.hihonor.iot.plc.newplc.controller.request.TransferBoardStatusPageRequest;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-30
 */
//@RestController
//@RequestMapping("/plc/transferBoard")
@Component
public class TransferBoardController {

    @Autowired
    private TransferBoardManager transferBoardManager;

    /**
     * 批量添加过板台
     *
     * @param request 要添加的过板台信息列表
     * @return 添加结果
     */
    @PostMapping("/batchAdd")
    public ApiResponse<Void> batchAddTransferBoard(@RequestBody @Valid TransferBoardBatchAddRequest request) {
        transferBoardManager.batchAddTransferBoard(request.getTransferBoardInfoList());
        return new ApiResponse<>(true, "批量添加过板台成功", null);
    }

    /**
     * 批量删除过板台
     *
     * @param request 批量删除过板台请求对象
     * @return 删除结果
     */
    @PostMapping("/batchRemove")
    public ApiResponse<Void> batchRemoveTransferBoard(@RequestBody @Valid TransferBoardBatchRemoveRequest request) {
        transferBoardManager.batchRemoveTransferBoard(request.getNames());
        return new ApiResponse<>(true, "批量删除过板台成功", null);
    }

    /**
     * 获取过板台状态信息(模糊查询、分页)
     *
     * @param request 查询请求对象
     * @return 过板台状态信息分页列表
     */
    @PostMapping("/getAllstatus")
    public ApiResponse<List<TransferBoardStatus>> getTransferBoardStatusPage(@RequestBody @Valid TransferBoardStatusPageRequest request) {
        List<TransferBoardStatus> pageInfo = transferBoardManager.getTransferBoardStatus(request.getName());
        return new ApiResponse<>(true, "获取过板台状态信息成功", pageInfo);
    }

//    @PostMapping("/statusPage")e
//    public ApiResponse<PageInfo<TransferBoardStatus>> getTransferBoardStatusPage(@RequestBody @Valid TransferBoardStatusPageRequest request) {
//        PageInfo<TransferBoardStatus>  pageInfo = transferBoardManager.getTransferBoardStatus(request.getName(),request.getPage(),request.getSize());
//        return new ApiResponse<>(true, "获取过板台状态信息成功", pageInfo);
//    }


    /**
     * 获取过板台进板信号
     *
     * @param request 过板台名称
     * @return 进板信号值
     */
    @PostMapping("/loadingSignal")
    public ApiResponse<String> getLoadingSignal(@RequestBody @Valid TransferBoardNameRequest request) {
        String signal = transferBoardManager.getLoadingSignal(request.getName());
        if (signal != null) {
            return new ApiResponse<>(true, "获取进板信号成功", signal);
        } else {
            return new ApiResponse<>(false, "获取设备状态失败,过板台不存在或不在线", null);
        }
    }

    /**
     * 获取过板台设备状态
     *
     * @param request 过板台名称
     * @return 设备状态值
     */
    @PostMapping("/deviceStatus")
    public ApiResponse<String> getDeviceStatus(@RequestBody @Valid TransferBoardNameRequest request) {
        String status = transferBoardManager.getDeviceStatus(request.getName());
        if (status != null) {
            return new ApiResponse<>(true, "获取设备状态成功", status);
        } else {
            return new ApiResponse<>(false, "获取设备状态失败,过板台不存在或不在线", null);
        }
    }


    /**
     * 批量获取过板台进板信号(模糊查询)
     *
     * @param request 查询请求对象
     * @return 过板台进板信号列表
     */
    @PostMapping("/batchLoadingSignal")
    public ApiResponse<List<TransferBoardSignal>> getBatchLoadingSignal(@RequestBody @Valid TransferBoardNameRequest request) {
        List<TransferBoardSignal> signalList = transferBoardManager.getBatchLoadingSignal(request.getName());
        return new ApiResponse<>(true, "批量获取进板信号成功", signalList);
    }

    /**
     * 批量获取过板台设备状态(模糊查询)
     *
     * @param request 查询请求对象
     * @return 过板台设备状态列表
     */
    @PostMapping("/batchDeviceStatus")
    public ApiResponse<List<DeviceStatus>> getBatchDeviceStatus(@RequestBody @Valid TransferBoardNameRequest request) {
        List<DeviceStatus> statusList = transferBoardManager.getBatchDeviceStatus(request.getName());
        return new ApiResponse<>(true, "批量获取设备状态成功", statusList);
    }

    /**
     * 批量下发过板信号
     *
     * @param request 过板信号MAP,key为过板台名称,value为要下发的信号值
     * @return 下发结果MAP, key为过板台名称, value为下发结果
     */
    @PostMapping("/batchWriteLoadingSignal")
    public ApiResponse<Map<String, Boolean>> batchWriteLoadingSignal(@RequestBody @Valid TransferBoardBatchWriteSignalRequest request) {
        Map<String, Boolean> resultMap = transferBoardManager.batchWriteLoadingSignal(request.toSignalMap());
        return new ApiResponse<>(true, "批量下发过板信号", resultMap);
    }


    /**
     * 批量获取过板台属性信息。
     *
     * @param request 包含过板台名称和属性名称列表的请求对象
     * @return 包含属性值的响应对象
     */
    @PostMapping("/batchReadProperties")
    public ApiResponse<BatchReadTransferBoardPropertiesResponse> batchReadTransferBoardProperties(
            @RequestBody @Valid BatchReadTransferBoardPropertiesRequest request) {
        BatchReadTransferBoardPropertiesResponse response = transferBoardManager.batchReadTransferBoardProperties(request);
        return new ApiResponse<>(true, "批量获取过板台属性信息成功", response);
    }

    /**
     * 批量写入过板台属性值。
     *
     * @param request 包含过板台名称和属性名称及其值的请求对象
     * @return 包含写入结果的响应对象
     */
    @PostMapping("/batchWriteProperties")
    public ApiResponse<BatchWriteTransferBoardPropertiesResponse> batchWriteTransferBoardProperties(
            @RequestBody @Valid BatchWriteTransferBoardPropertiesRequest request) {
        BatchWriteTransferBoardPropertiesResponse response = transferBoardManager.batchWriteTransferBoardProperties(request);
        return new ApiResponse<>(true, "批量写入过板台属性值成功", response);
    }

}
