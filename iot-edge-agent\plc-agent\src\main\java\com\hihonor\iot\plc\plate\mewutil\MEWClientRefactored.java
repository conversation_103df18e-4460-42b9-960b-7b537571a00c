 /*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.plate.mewutil;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.StandardSocketOptions;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * MEWClient - 松下PLC通信客户端
 * 负责与PLC设备建立通信连接并执行读写操作
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Slf4j
@Component
@Scope("prototype")
public class MEWClientRefactored {
    // 通信相关常量
    private static final int DEFAULT_BUFFER_SIZE = 1024;
    private static final int DEFAULT_CONNECT_TIMEOUT = 1000;
    private static final int DEFAULT_COMMAND_TIMEOUT = 2000;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int RETRY_DELAY_MS = 2000;
    
    // NIO通信组件
    private SocketChannel socketChannel;
    private ByteBuffer readBuffer = ByteBuffer.allocate(DEFAULT_BUFFER_SIZE);
    private ByteBuffer writeBuffer = ByteBuffer.allocate(DEFAULT_BUFFER_SIZE);

    // 连接状态和配置
    private volatile boolean connected = false;
    private String host;
    private int port;
    private String thingName;

    /**
     * 检查客户端是否处于已连接状态
     *
     * @return 如果客户端已连接且通道可用则返回true，否则返回false
     */
    public boolean getConnected() {
        return connected && socketChannel != null && socketChannel.isOpen() && socketChannel.isConnected();
    }

    /**
     * 默认构造函数
     */
    public MEWClientRefactored() {
        // 默认构造函数，保持为空
    }

    /**
     * 初始化MEWClient实例
     *
     * @param host      PLC服务器的IP地址
     * @param port      PLC服务器的端口号
     * @param thingName 用于标识客户端的名称
     */
    public void init(String host, int port, String thingName) {
        this.thingName = thingName;
        this.host = host;
        this.port = port;
        log.info("MEWClient初始化: 设备[{}], 地址[{}:{}]", thingName, host, port);
    }

    /**
     * 初始化MEWClient实例(简化版)
     *
     * @param host PLC服务器的IP地址
     * @param port PLC服务器的端口号
     */
    public void init(String host, int port) {
        init(host, port, "mewclient");
    }

    /**
     * 尝试连接到服务器
     *
     * @param timeout 连接超时时间（毫秒）
     * @return 如果连接成功返回true，否则返回false
     */
    public synchronized boolean connect(int timeout) {
        // 如果已连接并且通道有效，则无需重新连接
        if (connected && socketChannel != null) {
            return true;
        }

        // 关闭之前的连接
        close();

        try {
            log.info("正在连接到PLC设备: {}@{}:{}", thingName, host, port);
            socketChannel = SocketChannel.open();
            socketChannel.configureBlocking(true);
            socketChannel.socket().connect(new InetSocketAddress(host, port), timeout);
            socketChannel.setOption(StandardSocketOptions.SO_KEEPALIVE, true);
            connected = true;
            log.info("PLC设备连接成功: {}@{}:{}", thingName, host, port);
            return true;
        } catch (Exception e) {
            log.error("PLC设备连接失败: {}@{}:{}, 错误: {}", thingName, host, port, e.getMessage());
            socketChannel = null;
            connected = false;
            return false;
        }
    }

    /**
     * 读取PLC的数据区间
     *
     * @param startAddress 起始地址
     * @param endAddress   结束地址
     * @return 结果字符串，连接失败时返回空字符串
     */
    public String readData(String startAddress, String endAddress) {
        // 检查并建立连接
        if (!connect(DEFAULT_CONNECT_TIMEOUT)) {
            log.warn("无法读取数据，PLC连接失败: {}@{}:{}", thingName, host, port);
            return "";
        }

        try {
            // 读取数据并转换为字符串
            List<String> dataList = readDataInner(startAddress, endAddress);
            StringBuilder sb = new StringBuilder();
            for (String data : dataList) {
                sb.append(data);
            }
            String result = sb.toString();
            log.debug("读取PLC数据成功: {}@{}:{}, 地址[{}-{}], 结果: {}", 
                    thingName, host, port, startAddress, endAddress, result);
            return result;
        } catch (Exception e) {
            log.error("读取PLC数据异常: {}@{}:{}, 地址[{}-{}], 错误: {}", 
                    thingName, host, port, startAddress, endAddress, e.getMessage());
            return "";
        }
    }

    /**
     * 内部方法：执行PLC数据读取
     *
     * @param startAddress 起始地址
     * @param endAddress   结束地址
     * @return 读取的数据列表
     */
    private List<String> readDataInner(String startAddress, String endAddress) {
        try {
            // 生成MEWTOCOL命令
            String command = MEWTOCOL.generateCommandByAddressType(startAddress, endAddress);
            log.debug("生成读取命令: {}", command);
            
            if (command == null || command.isEmpty()) {
                log.warn("生成读取命令失败，地址类型可能不支持: [{}-{}]", startAddress, endAddress);
                return new ArrayList<>();
            }

            // 发送命令并接收响应
            String response = sendCommandAndReceiveResponse(command, DEFAULT_COMMAND_TIMEOUT);
            
            // 解析响应结果
            if (response != null) {
                List<String> result = MEWTOCOL.parsePLCResponse(response);
                log.debug("读取数据成功: {}@{}:{}, 地址[{}-{}], 结果: {}", 
                        thingName, host, port, startAddress, endAddress, result);
                return result;
            } else {
                log.warn("读取数据失败，PLC响应为空: {}@{}:{}, 地址[{}-{}]", 
                        thingName, host, port, startAddress, endAddress);
                return new ArrayList<>();
            }
        } catch (Exception ex) {
            connected = false;
            log.error("读取数据异常: {}@{}:{}, 地址[{}-{}], 错误: {}", 
                     thingName, host, port, startAddress, endAddress, ex.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 向PLC发送命令
     *
     * @param address 目标地址
     * @param data    要写入的数据
     * @return 操作是否成功
     */
    public boolean sendCommon(String address, String... data) {
        // 检查并建立连接
        if (!connect(DEFAULT_CONNECT_TIMEOUT)) {
            log.warn("无法发送命令，PLC连接失败: {}@{}:{}", thingName, host, port);
            return false;
        }
        
        log.info("向PLC发送命令 - 设备: {}@{}:{}, 地址: {}, 数据: {}", 
                thingName, host, port, address, Arrays.toString(data));
        
        try {
            // 参数检查
            if (address == null || data == null || data.length == 0) {
                log.warn("无效的命令参数: {}@{}:{}, 地址或数据为空", thingName, host, port);
                return false;
            }

            // 根据地址类型生成不同命令
            String command;
            boolean isDTAddress = address.startsWith("DT");

            if (isDTAddress) {
                // DT类型地址 - 寄存器写入
                List<String> dataList = Arrays.asList(data);
                String endAddress = calculateEndAddress(address, data.length);
                command = MEWTOCOL.encodeWriteRegisterCommand(address, endAddress, dataList);
            } else {
                // R类型地址 - 单触点写入
                command = MEWTOCOL.writeSingleContact(address, data[0]);
            }

            // 命令检查
            if (command == null) {
                log.error("命令生成失败: {}@{}:{}, 地址: {}", thingName, host, port, address);
                return false;
            }
            log.debug("生成命令: {}", command);

            // 发送命令并接收响应
            String response = sendCommandAndReceiveResponse(command, DEFAULT_COMMAND_TIMEOUT);
            
            // 检查响应
            if (response == null) {
                log.error("PLC无响应: {}@{}:{}, 地址: {}", thingName, host, port, address);
                return false;
            }
            
            log.info("命令发送成功: {}@{}:{}, 地址: {}, 响应: {}", 
                    thingName, host, port, address, response);

            // 根据地址类型解析响应
            if (isDTAddress) {
                return MEWTOCOL.decodeWriteRegisterResponse(response);
            } else {
                return MEWTOCOL.parseWriteSingleContactResponse(response);
            }
        } catch (Exception ex) {
            connected = false;
            log.error("发送命令异常: {}@{}:{}, 地址: {}, 错误: {}", 
                    thingName, host, port, address, ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * 根据起始地址和数据数量计算结束地址
     *
     * @param startAddress 起始地址，格式为 "DTxxxxx"
     * @param dataCount    数据条目数量
     * @return 结束地址，格式为 "DTxxxxx"
     */
    private String calculateEndAddress(String startAddress, int dataCount) {
        int startNum = Integer.parseInt(startAddress.substring(2));
        int endNum = startNum + dataCount - 1;
        return String.format("DT%05d", endNum);
    }

    /**
     * 发送命令并接收响应，带重试机制
     *
     * @param command 要发送的命令
     * @param timeout 超时时间（毫秒）
     * @return 设备的响应，失败时返回null
     * @throws IOException 如果通信失败
     */
    private synchronized String sendCommandAndReceiveResponse(String command, int timeout) throws IOException {
        int attempt = 0;

        while (attempt < MAX_RETRY_COUNT) {
            long startTime = System.currentTimeMillis();
            log.debug("尝试发送命令: {}, 尝试次数: {}/{}", command, attempt + 1, MAX_RETRY_COUNT);

            try {
                // 准备发送缓冲区
                writeBuffer.clear();
                writeBuffer.put(command.getBytes(StandardCharsets.US_ASCII));
                writeBuffer.flip();

                // 发送命令
                while (writeBuffer.hasRemaining()) {
                    if (System.currentTimeMillis() - startTime > timeout) {
                        throw new IOException("发送命令超时");
                    }
                    socketChannel.write(writeBuffer);
                }
                log.debug("命令发送成功");
                
                // 接收响应
                readBuffer.clear();
                StringBuilder response = new StringBuilder();

                while (true) {
                    if (socketChannel.read(readBuffer) > 0) {
                        readBuffer.flip();
                        while (readBuffer.hasRemaining()) {
                            char character = (char) readBuffer.get();
                            response.append(character);
                            // 松下PLC的响应以'\r'结尾
                            if (character == '\r') {
                                String result = response.toString();
                                log.debug("收到PLC响应: {}", result);
                                return result;
                            }
                        }
                        readBuffer.clear();
                    } else if (System.currentTimeMillis() - startTime > timeout) {
                        throw new IOException("接收响应超时");
                    }
                }
            } catch (IOException e) {
                attempt++;
                log.warn("通信失败 ({}/{}): {}", attempt, MAX_RETRY_COUNT, e.getMessage());
                
                if (attempt >= MAX_RETRY_COUNT) {
                    log.error("通信失败，已达最大重试次数: {}", e.getMessage());
                    throw new IOException("通信失败，已达最大重试次数: " + e.getMessage(), e);
                }
                
                try {
                    log.debug("等待{}ms后重试", RETRY_DELAY_MS);
                    Thread.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("通信重试被中断", ie);
                }
            }
        }

        // 逻辑上不应该到达这里，但为了代码完整性添加
        throw new IOException("发送命令失败，已达到最大重试次数");
    }

    /**
     * 关闭与服务器的连接并释放资源
     */
    public void close() {
        if (socketChannel != null) {
            try {
                log.info("关闭PLC连接: {}@{}:{}", thingName, host, port);
                socketChannel.close();
            } catch (IOException e) {
                log.error("关闭PLC连接异常: {}@{}:{}, 错误: {}", 
                        thingName, host, port, e.getMessage());
            } finally {
                socketChannel = null;
                connected = false;
            }
        }
    }
}