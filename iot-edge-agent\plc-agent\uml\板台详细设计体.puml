@startuml
package "板台模块" {
    class "板台网络通信模块" as PlateNetworkModule {
        + 初始化实例
        + 连接尝试
        + 关闭连接
        + 读取 PLC 数据
        + 解析响应
        + 发送命令
        + 超时处理
        + 检查连接状态
    }

    class "板台设备控制模块" as PlateControlModule {
        + 初始化实例
        + 连接尝试
        + 关闭连接
        + 根据点位名称读取数据
        + 读取地址范围数据
        + 写入值
        + 发送命令
        + 检查连接状态
        + 定期状态更新
        + 发送轨道信号
        + 发送禁止通过信号
        + 日志功能
    }

    class "板台设备管理模块" as PlateManagementModule {
        + 加载板台信息
        + 批量读取属性
        + 批量写入属性
        + 发送进板信号
        + 发送禁止通行信号
        + 获取板台对象
        + 添加或更新设备
        + 移除设备
        + 批量删除设备
        + 读取指定地址范围的数据
        + 发送写入命令
    }

    class "数据库" as Database {
        + 存储板台配置
        + 存储设备状态
        + 读取板台配置
    }

    class "用户" as User {
        + 管理板台设备
        + 查询设备状态
    }

    class "日志模块" as Logging {
        + 记录操作日志
        + 记录错误日志
    }
}

PlateControlModule --> PlateNetworkModule : 依赖
PlateManagementModule --> PlateControlModule : 依赖
PlateManagementModule --> Database : 读取和写入板台配置
PlateManagementModule --> Logging : 记录日志
User --> PlateManagementModule : 增删查设备
@enduml