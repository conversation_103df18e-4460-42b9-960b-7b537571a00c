@startuml
package "日志模块" {
    class "PlcOperationLog" {
        + Long id
        + String deviceType
        + String deviceName
        + Boolean onlineStatus
        + String methodName
        + String methodParams
        + String userId
        + Date operationTime
        + Integer executionTime
        + String returnValue
        + String exceptionMessage
    }

    class "日志服务" as LogService {
        + insertLog(PlcOperationLog log) : void
        + queryLogs(String deviceName) : List<PlcOperationLog>
        + queryLogsByUserId(String userId) : List<PlcOperationLog>
        + queryLogsByDateRange(Date startDate, Date endDate) : List<PlcOperationLog>
    }

    class "日志存储" as LogRepository {
        + save(PlcOperationLog log) : void
        + findByDeviceName(String deviceName) : List<PlcOperationLog>
        + findByUserId(String userId) : List<PlcOperationLog>
        + findByDateRange(Date startDate, Date endDate) : List<PlcOperationLog>
    }

    class "数据库" as Database {
        + saveLog(PlcOperationLog log) : void
        + retrieveLogs(String query) : List<PlcOperationLog>
    }

    class "用户" as User {
        + manageLogs() : void
        + queryLogs() : List<PlcOperationLog>
    }

    class "设备" as Device {
        + String deviceName
        + String deviceType
        + Boolean onlineStatus
        + void updateStatus()
    }
}

LogService --> LogRepository : 使用
LogRepository --> Database : 交互
User --> LogService : 管理和查询日志
Device --> LogService : 记录设备状态
Device --> LogRepository : 提供设备信息
@enduml