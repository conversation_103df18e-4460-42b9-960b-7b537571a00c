# 过板台设备控制接口文档

## 写入过板台点位值 API

- **URL**: `/plc/plate/writeValue`
- **Method**: `POST`
- **描述**: 向指定过板台写入点位（R 类型）值
- **请求头**:
  - `Content-Type: application/json`

### 请求参数模型：WriteValueRequest

```java
public class WriteValueRequest {
    @NotBlank(message = "过板台名称不能为空")
    private String plateName;

    @NotBlank(message = "地址不能为空")
    private String address;

    @NotBlank(message = "值不能为空")
    private String value;
}
```

- **字段说明**:
  - `plateName` (string, 必填): 过板台名称
  - `address` (string, 必填): 点位地址，例如 `R0001`
  - `value` (string, 必填): 要写入的数值

### 请求示例

```json
{
  "plateName": "板台A",
  "address": "R0001",
  "value": "123"
}
```

### 响应体模型：ApiResponse<Boolean>

```json
{
  "success": true,
  "message": "写入成功",
  "data": true
}
```

- **字段说明**:
  - `success` (boolean): 操作是否成功
  - `message` (string): 提示信息
  - `data` (boolean): 返回具体结果（true: 写入成功；false: 写入失败）

### 错误响应

- **HTTP 400**: 请求参数校验失败，返回错误详情
- **HTTP 500**: 服务端写入失败，返回错误信息

### 接口使用示例

**写入接口示例 1：成功写入**
请求：

```json
{
  "plateName": "SMT09_HuiLiuLu",
  "address": "R2000",
  "value": "1"
}
```

响应：

```json
{
  "success": true,
  "message": "写入成功",
  "data": true,
  "length": 0
}
```

**写入接口示例 2：目标设备不存在**
请求：

```json
{
  "plateName": "123",
  "address": "R2000",
  "value": "0"
}
```

响应：

```json
{
  "success": false,
  "message": "写入失败",
  "data": false,
  "length": 0
}
```

**写入接口示例 3：地址格式不正确**
请求：

```json
{
  "plateName": "SMT09_HuiLiuLu",
  "address": "1R2000",
  "value": "0"
}
```

响应：

```json
{
  "success": false,
  "message": "写入失败",
  "data": false,
  "length": 0
}
```

> **注意**：本接口仅提供通用控制功能，具体业务逻辑（例如告警触发后向地址 `R2000` 写入 `1` 并自动复位写入 `0`）需由开发者在业务层自行实现。

---

## 读取过板台数据 API

- **URL**: `/plc/plate/readData`
- **Method**: `POST`
- **描述**: 从指定过板台读取起始地址到结束地址的数据，适用于验证写入结果或批量读取数据。
- **请求头**:
  - `Content-Type: application/json`

### 请求参数模型：ReadDataRequest

```java
public class ReadDataRequest {
    @NotBlank(message = "过板台名称不能为空")
    private String plateName;

    @NotBlank(message = "起始地址不能为空")
    private String startAddress;

    @NotBlank(message = "结束地址不能为空")
    private String endAddress;
}
```

- **字段说明**:
  - `plateName` (string, 必填): 过板台名称
  - `startAddress` (string, 必填): 起始地址，例如 `R0001` 或 `DT100`
  - `endAddress` (string, 必填): 结束地址，例如 `R0010` 或 `DT110`

### 请求示例

```json
{
  "plateName": "板台A",
  "startAddress": "R0001",
  "endAddress": "R0010"
}
```

### 响应体模型：ApiResponse<String>

```json
{
  "success": true,
  "message": "读取成功",
  "data": "0000112233",
  "length": 10
}
```

- **字段说明**:
  - `success` (boolean): 操作是否成功
  - `message` (string): 提示信息
  - `data` (string): 按地址顺序返回的原始数据字符串
  - `length` (integer): 返回的数据长度

### 错误响应

- **HTTP 400**: 请求参数校验失败，返回错误详情
- **HTTP 500**: 服务端读取失败，返回错误信息
