/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.newplc.controller.request;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-30
 */
@Data
public class TransferBoardStatusPageRequest {

    @NotNull(message = "过板台名称不能为空")
    private String name;

//    @Min(value = 0, message = "当前页码不能小于1")
//    private int page = 1;
//
//    @Min(value = 1, message = "每页大小不能小于1")
//    private int size = 10;
}
