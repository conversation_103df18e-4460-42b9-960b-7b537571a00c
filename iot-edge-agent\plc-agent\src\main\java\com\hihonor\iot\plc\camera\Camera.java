/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.iot.plc.camera;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.hihonor.iot.plc.boot.schedule.CustomScheduled;
import com.hihonor.iot.plc.boot.schedule.TaskRegistrar;
import com.hihonor.iot.plc.camera.netty.DeviceStatusListener;
import com.hihonor.iot.plc.camera.netty.NettyScannerClient;
import com.hihonor.iot.plc.camera.netty.NettyScannerClientFacade;
import com.hihonor.iot.plc.camera_and_plate.BarcodeService;
import com.hihonor.iot.plc.camera_and_plate.BarcodeValidationResult;
import com.hihonor.iot.plc.camera_and_plate.ScanRequest;
import com.hihonor.iot.plc.deviceLog.Device;
import com.hihonor.iot.plc.deviceLog.PlcLogOperation;
import com.hihonor.iot.plc.m2m.M2MService;
import com.hihonor.iot.plc.newplc.PlcConfiguration;
import com.hihonor.iot.plc.plcbase.PlcDeviceService;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Component
@Scope("prototype")
@Slf4j
public class Camera implements Device, DeviceStatusListener {
    @Getter
    private String ip;
    @Getter
    private int port;

    private String thingName;
    @Autowired
    TaskRegistrar taskRegistrar;

    @Autowired
    PlcDeviceService plcDeviceService;

    @Autowired
    private PlcConfiguration plcConfiguration;

    private Camera prox;

    // @Getter
    // private ScannerClient scannerClient;
    @Autowired
    NettyScannerClientFacade nettyScannerClientFacade;

    /**
     * 初始化方法，用于设置IP、端口、物体名称和传输板
     *
     * @param ip        IP地址
     * @param port      端口号
     * @param thingName 物体名称
     * @param porx      传输板
     */
    public void init(String ip, int port, String thingName, Camera porx) {
        // 将传入的IP赋值给对象的ip属性
        this.ip = ip;
        // 将传入的端口号赋值给对象的port属性
        this.port = port;
        // 将传入的物体名称赋值给对象的thingName属性
        this.thingName = thingName;
        // 创建一个新的MEWClient对象
        // scannerClient = new ScannerClient(ip, port);
        // 调用MEWClient对象的初始化方法，传入IP、端口和物体名称
        // 将传入的传输板赋值给对象的prox属性
        this.prox = porx;
        nettyScannerClientFacade.addDeviceStatusListener(ip, port, (DeviceStatusListener) this);
        // 调用connect方法，建立连接
        connect();
        // 使用taskRegistrar为该对象注册任务
        taskRegistrar.registerTasksForObject(this);
    }

    public void reInit(String ip, int port, String thingName) {
        nettyScannerClientFacade.close(ip, port);
        this.ip = ip;
        this.port = port;
        this.thingName = thingName;
        connect();
    }

    @Override
    public String getType() {
        return "camera";
    }

    @Override
    public String getThingName() {
        return thingName;
    }

    public boolean connect() {
        nettyScannerClientFacade.connect(ip, port);

        // boolean connected = scannerClient.connect(2000);
        // if (connected) {
        // log.info("过板台 {} 连接成功", thingName);
        // } else {
        // log.error("过板台 {} 连接失败", thingName);
        // }
        // return connected;
        return true;
    }

    @PlcLogOperation
    public String readBarcode() {
        return nettyScannerClientFacade.readBarcode(ip, port);
    }

    @PlcLogOperation
    public void reConnect() {
        nettyScannerClientFacade.reconnect(ip, port);
    }

    @PlcLogOperation
    public boolean heartBeat() {
        return getConnected();
    }

    @Autowired
    M2MService m2MService;

    @CustomScheduled(fixedRate = 30 * 1000)
    public void updateStatus() {
        // 这里的heartBeat() 是通PING 功能，查找设备是否能够PING通，如果能够PING通，说明设备在线。
        // 因为相机设备只能建立一个连接，所以这里还需要检测端口被占用的情况，
        // 如果连接上了，说明本程序没有连接上，返回FALSE，并且设置连接状态为FASLE
        if (prox.heartBeat()) {
            // 上报数据库
            plcDeviceService.reportDeviceOnline(thingName);
            // 上报M2M服务
            // boolean res = m2MService.sendHeartbeat(thingName, "120");
            // if (res) {
            // log.info("设备 {} (IP: {}) 发送心跳成功", thingName, ip + port);
            // } else {
            // log.info("设备 {} (IP: {}) 发送心跳失败", thingName, ip + port);
            // }
        }
        //// 这里获取连接的真实状态，真实状态由上述的PING ，端口占用检测，还有周期性的读取条码，来判断设备是否在线
        // if (!scannerClient.isConnected()) {
        // log.error("设备 {} (IP: {}) 心跳失败，尝试重连", thingName, ip + port);
        // // 重连
        // connect();
        //
        // }
        //
        // if(scannerClient.isConnected())
        // {
        // plcDeviceService.reportDeviceOnline(thingName);
        // log.info("设备 {} (IP: {}) 心跳成功", thingName, ip + port);
        // }
        // else
        // {
        // log.info("设备 {} (IP: {}) 心跳失败", thingName, ip + port);
        // }

    }

    /**
     * 获取连接状态
     *
     * @return 连接状态
     */
    @Override
    public boolean getConnected() {
        return nettyScannerClientFacade.isDeviceOnline(ip, port);
    }

    /**
     * 关闭连接
     */
    public void destroy() {
        taskRegistrar.cancelTask(this, "updateStatus");
        try {

            nettyScannerClientFacade.close(ip, port);
            nettyScannerClientFacade.removeDeviceStatusListener(ip, port);

            // New call to stop future reconnection attempts scheduled by NettyScannerClient
            // Ideally, this logic would be encapsulated within
            // nettyScannerClientFacade.close(),
            // but we call the singleton directly here as the facade source is not
            // available.
            NettyScannerClient.getInstance().stopConnectionAttempts(ip, port);

            log.info("设备 {} (IP: {}) 关闭连接成功", thingName, ip + port);
        } catch (Exception e) {
            log.error("设备 {} (IP: {}) 关闭连接失败", thingName, ip + port);
        }
    }

    @Autowired
    BarcodeService barcodeService;

    @PlcLogOperation
    public BarcodeValidationResult validateBarcode(ScanRequest scanRequest) {

        // BarcodeValidationResult result = new BarcodeValidationResult(Math.random() <
        // 0.5, "");
        BarcodeValidationResult result = barcodeService.validateBarcode(scanRequest);
        return result;

    }

    @Override
    public void onDeviceConnected() {

    }

    @Override
    public void onDeviceDisconnected() {
        reConnect();
    }
}
