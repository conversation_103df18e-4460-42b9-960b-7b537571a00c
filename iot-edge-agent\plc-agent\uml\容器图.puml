@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

System_Boundary(s1, "主控制系统") {
    Container(main_app, "主控制应用", "Java, Spring Boot", "处理条码信息和设备通讯")
    Container(plate_comm, "板台通讯模块", "使用Mewtocol协议", "与板台控制器通信")
    Container(camera_comm, "相机通讯模块", "使用基恩士专业TCP协议", "与相机控制器通信")
    Container(database, "数据库", "PostgreSQL", "存储条码和控制信息")
    Container(camera_config, "摄像头设备配置", "JSON", "包含设备名、IP 地址、端口、设备类型")
    Container(plate_config, "板台设备配置", "JSON", "包含设备名、IP 地址、端口、设备类型")
}

System(plate_controller, "板台控制器", "PLC控制器", "使用Mewtocol协议控制板台")
System(camera_controller, "相机控制器", "PLC控制器", "使用基恩士专业TCP协议与相机通信")
System(mes, "MES系统", "外部系统，用于条码验证")

Rel(main_app, plate_comm, "发送控制指令", "TCP")
Rel(main_app, camera_comm, "发送控制指令", "TCP")
Rel(main_app, database, "读取/写入", "JDBC")
Rel(main_app, mes, "调用", "HTTP")
Rel(main_app, camera_config, "管理摄像头配置")
Rel(main_app, plate_config, "管理板台配置")
Rel(plate_comm, plate_controller, "发送控制指令", "Mewtocol")
Rel(camera_comm, camera_controller, "发送控制指令", "基恩士专业TCP协议")

@enduml